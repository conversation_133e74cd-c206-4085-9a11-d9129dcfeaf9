{"name": "vite-ts-template", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "build:staging": "vite build --mode development", "build:prod": "vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint --fix --ext ts,tsx,js,jsx .", "preview": "vite preview", "format": "npx prettier --write .", "clean": "rm -rf node_modules pnpm-lock.yaml", "prepare": "husky install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@reduxjs/toolkit": "^1.9.5", "@types/lodash.debounce": "^4.0.9", "antd": "^5.20.6", "antd-phone-input": "^0.3.9", "axios": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "google-libphonenumber": "^3.2.40", "react": "^18.2.0", "react-big-calendar": "^1.13.4", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "strip-ansi": "6.0.1", "tailwind-merge": "^2.1.0", "wouter": "^2.12.1"}, "devDependencies": {"@rollup/plugin-typescript": "^12.1.2", "@storybook/addon-essentials": "7.6.6", "@storybook/addon-interactions": "7.6.6", "@storybook/addon-links": "7.6.6", "@storybook/addon-onboarding": "1.0.10", "@storybook/blocks": "7.6.6", "@storybook/react": "7.6.6", "@storybook/react-vite": "7.6.6", "@storybook/test": "7.6.6", "@types/node": "^20.8.6", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.27.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-unicorn": "^48.0.1", "eslint-plugin-unused-imports": "^3.0.0", "husky": "^8.0.3", "lint-staged": "^13.3.0", "postcss": "^8.4.31", "prettier": "2.8.8", "prettier-plugin-tailwindcss": "^0.2.7", "storybook": "7.6.6", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.11", "vite-plugin-eslint": "^1.8.1", "vite-plugin-remove-console": "2.1.1", "vite-plugin-svgr": "^2.4.0", "vite-tsconfig-paths": "^5.0.1"}}
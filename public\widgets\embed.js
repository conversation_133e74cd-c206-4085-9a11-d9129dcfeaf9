(function () {
  try {
    console.log("hey")
    // Step 1: Detect script tag or fallback to DOM
    const currentScript = document.currentScript || [...document.querySelectorAll('script')].find(s => s.src && s.src.includes('embed.js'));
    
    // Step 2: Parse configuration from script tag (if found)
    let widgetId = null;
    let origin = null;
    let target = null;
    let config = {};

    if (currentScript) {
      const scriptUrl = new URL(currentScript.src);
      widgetId = scriptUrl.searchParams.get('widgetId');
      origin = scriptUrl.searchParams.get('origin') || scriptUrl.origin;
      target = scriptUrl.searchParams.get('target');
      
      // Parse additional configuration options
      for (const [key, value] of scriptUrl.searchParams.entries()) {
        if (!['widgetId', 'origin', 'target'].includes(key)) {
          config[key] = value;
        }
      }
    }

    // Step 3: Fallback to DOM if widgetId missing
    if (!widgetId) {
      const domRoot = document.getElementById('hop-widget-root') || document.querySelector('[data-hop-widget]');
      widgetId = domRoot?.dataset?.widgetId || domRoot?.dataset?.hopWidget || null;
    }

    if (!widgetId) {
      console.error('Hop Widget: ❌ Missing widgetId. Provide it via script query param, data-widget-id, or data-hop-widget attribute.');
      return;
    }

    console.log('✅ Hop Widget: Initializing for widgetId =', widgetId);

    // Step 4: Create or find the widget container
    let container;
    if (target) {
      container = document.querySelector(target);
      if (!container) {
        console.warn(`Hop Widget: Target element "${target}" not found, falling back to default.`);
      }
    }
    
    if (!container) {
      container = document.getElementById('hop-widget-root') || document.querySelector('[data-hop-widget]');
    }
    
    if (!container) {
      container = document.createElement('div');
      container.id = 'hop-widget-root';
      document.body.appendChild(container);
    }
    
    // Set data attributes
    container.dataset.widgetId = widgetId;
    
    // Add any additional configuration as data attributes
    Object.entries(config).forEach(([key, value]) => {
      container.dataset[key] = value;
    });

    // Step 5: Inject the widget loader with proper origin handling
    const renderScript = document.createElement('script');
    renderScript.type = 'module';
    renderScript.src = `${origin}/widgets/render.js`;
    renderScript.onerror = () => {
      console.error(`Hop Widget: Failed to load from ${origin}/widgets/render.js`);
      // Try fallback to CDN if origin fails
      if (origin !== 'https://cdn.hopwellness.com') {
        console.log('Hop Widget: Trying CDN fallback...');
        renderScript.src = 'https://cdn.hopwellness.com/widgets/render.js';
      }
    };
    document.body.appendChild(renderScript);

    // Expose global configuration for the widget
    window.HopWidget = window.HopWidget || {
      config: {
        widgetId,
        origin,
        ...config
      },
      // Add API methods that can be called from the parent application
      refresh: function() {
        const event = new CustomEvent('hop-widget-refresh');
        container.dispatchEvent(event);
      }
    };
    
  } catch (err) {
    console.error('Hop Widget: 💥 Failed to initialize widget:', err);
  }
})();

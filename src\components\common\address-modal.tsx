import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button } from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { SetPosPurchasedData } from '~/redux/slices/purchaged-slice';

interface Address {
    id: number;
    name: string;
    address: any;
    isDefault: boolean;
}

interface AddressSelectionModalProps {
    open: boolean;
    onClose: () => void;
    addresses: Address[];
    onSelect: (selectedAddress: Address) => void;
}

const AddressSelectionModal: React.FC<AddressSelectionModalProps> = ({
    open,
    onClose,
    addresses,
    onSelect,
}) => {

    console.log("addresses--------", addresses)
    const dispatch = useAppDispatch();
     const store = useAppSelector((state) => ({
            posPurchasedData: state.purchased_store.posPuchasedData,
        }));

    const [selectedAddress, setSelectedAddress] = useState<Address | any>(
        addresses.find((addr) => addr.isDefault) || addresses[0] || null
    );

    useEffect(()=>{
        setSelectedAddress(addresses?.find((addr) => addr?.isDefault) || addresses?.[0])
        dispatch(SetPosPurchasedData({ ...store.posPurchasedData, billingAddressId: selectedAddress?.id }));
    },[])

    console.log("selectedAddress-----------", selectedAddress)

    return (
        <Modal
            title="Choose Your Location"
            open={open}
            onCancel={onClose}
            footer={null}
            // width={500}
            className='w-[30%]'
        >
            <p className="text-gray-600 text-16">
                Select a delivery location to see product availability and
                delivery options
            </p>

            <div className="mt-4 flex flex-col gap-4">
                {addresses.map((address) => (
                    <div
                        key={address.id}
                        className={`border p-4 rounded-lg cursor-pointer ${
                            selectedAddress?.id === address.id
                                // ? address.isDefault
                                    ? 'bg-purple-100 border-purple-400'
                                    // : 'border-purple-500'
                                : 'border-gray-300'
                        }`}
                        onClick={() => setSelectedAddress(address)}
                    >
                        <p className="font-semibold text-[#1A3353]">
                            {address.name}
                        </p>
                        <p className="text-gray-600 text-12">
                            {address.address}
                        </p>
                        {address.isDefault && (
                            <p className="text-12 font-medium text-purple-600">
                                Default address
                            </p>
                        )}
                    </div>
                ))}
            </div>

            {/* Footer Buttons */}
            <div className="flex justify-end gap-3 mt-6">
                <Button onClick={onClose}>Cancel</Button>
                <Button
                    type="primary"
                    className="bg-[#8143D1]"
                    onClick={() => {
                        if (selectedAddress) {
                            onSelect(selectedAddress);
                        }
                        onClose();
                    }}
                >
                    Save
                </Button>
            </div>
        </Modal>
    );
};

export default AddressSelectionModal;

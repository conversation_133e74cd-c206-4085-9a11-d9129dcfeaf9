import React, { useEffect, useState } from 'react';
import { ConfigProvider, Pagination, Table } from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { clientBookingList } from '~/redux/actions/customer-action';
import { useLoader } from '~/hooks/useLoader';
import { capitalizeFirstLetter, formatDate } from '../common/function';

// Column Definitions
const columns: any = [
    {
        title: 'Package Name',
        dataIndex: 'packageName',
    },
    {
        title: 'Date',
        dataIndex: 'date',
        render: (text: any) => {
            return formatDate(text);
        },
    },
    {
        title: 'Start Time',
        dataIndex: 'from',
    },
    {
        title: 'End Time',
        dataIndex: 'to',
    },
    {
        title: 'Location',
        dataIndex: 'facilityName',
    },
    {
        title: 'Service Category',
        dataIndex: 'serviceCategoryName',
    },
    {
        title: 'Booking Status',
        dataIndex: 'scheduleStatus',
        render: (text: any) => {
            const statusClass =
                text === 'booked'
                    ? 'bg-green-100 bg-opacity-50 w-fit text-green-500 py-1 px-3 rounded'
                    : text === 'checked-in'
                    ? 'bg-purple-100 text-primary py-1 w-fit px-3 rounded'
                    : text === 'canceled'
                    ? 'bg-red-100 text-primary py-1 w-fit px-3 rounded'
                    : 'py-1 px-3';
            return (
                <div className={statusClass}>{capitalizeFirstLetter(text)}</div>
            );
        },
    },
];

const ClientBookingCheckInModal = ({ clientId }: any) => {
    const dispatch = useAppDispatch();

    const userId = clientId;

    const [loader, startLoader, endLoader] = useLoader();

    const [currentPage, setCurrentPage] = useState(1);
    const [pageSizes, setPageSize] = useState(10);

    const store = useAppSelector((state) => ({
        customerSchedlingList: state.customer_store.customerSchedulingList,
        customerSchedulingListCount:
            state.customer_store.customerSchedulingListCount,
    }));

    useEffect(() => {
        const reqData = {
            page: currentPage,
            pageSize: pageSizes,
            clientId: userId,
        };
        if (userId) {
            startLoader();
            dispatch(clientBookingList(reqData))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [userId, currentPage, pageSizes]);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
    }

    return (
        <div className="mt-6">
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            borderColor: '#0000001A',
                            cellFontSize: 13,
                            headerBg: '#fff',
                            headerColor: '#1A3353',
                            colorText: '#455560',
                        },
                    },
                }}
            >
                <div className="rounded-xl border bg-white px-4 pb-2 pt-4 shadow-md">
                    <Table
                        columns={columns}
                        dataSource={store.customerSchedlingList}
                        pagination={false}
                        // scroll={{ y: 240 }}
                        loading={loader}
                    />
                    <div className="flex justify-center  py-10">
                        <Pagination
                            current={currentPage}
                            total={store.customerSchedulingListCount}
                            pageSizeOptions={['10', '20', '50']}
                            pageSize={pageSizes}
                            onChange={paginate}
                            // hideOnSinglePage
                        />
                    </div>
                </div>
            </ConfigProvider>
        </div>
    );
};

export default ClientBookingCheckInModal;

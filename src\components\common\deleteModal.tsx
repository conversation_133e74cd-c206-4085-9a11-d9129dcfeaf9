import React from 'react';
import { Modal, Button } from 'antd';

interface DeleteModalProps {
    isVisible: boolean;
    title: string;
    onDelete: () => void;
    onCancel: () => void;
    message?: string;
    deleteType?:string;
}

const DeleteModal: React.FC<DeleteModalProps> = ({
    isVisible,
    title,
    onDelete,
    onCancel,
    message,
    deleteType
}) => {
    return (
        <Modal
            title={title}
            open={isVisible}
            onOk={onDelete}
            onCancel={onCancel}
            footer={null}
        >
            <div className=" pb-6">
                <div className="">{message}</div>
            </div>
            <div className=" flex justify-end">
                <Button
                    onClick={onCancel}
                    htmlType="button"
                    className="me-6 w-[110px] border-[#1A3353] bg-[#fff] text-[#1A3353]"
                >
                    Cancel
                </Button>
                <Button
                    onClick={onDelete}
                    htmlType="button"
                    className="w-[110px] bg-purpleLight text-white"
                >
                    {deleteType === 'checkIn' ? "Check In" :'Delete'}
                </Button>
            </div>
        </Modal>
    );
};

export default DeleteModal;

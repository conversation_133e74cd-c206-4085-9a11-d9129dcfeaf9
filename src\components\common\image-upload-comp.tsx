import React, { useEffect, useState } from 'react';
import uploadIcon from '../../../public/assets/Profile_icon.png';

interface ImageUploadProps {
    onUpload: (file: File) => void;
    imageUrl?: string | null | undefined;
}

const ImageUpload: React.FC<ImageUploadProps> = ({ onUpload, imageUrl }) => {
    const [imagePreview, setImagePreview] = useState<string | null | undefined>(
        imageUrl && imageUrl
    );
    const [error, setError] = useState<string | null>(null);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (file.type.startsWith('image/')) {
                setError(null);
                const reader = new FileReader();
                reader.onloadend = () => {
                    setImagePreview(reader.result as string);
                };
                reader.readAsDataURL(file);
                onUpload(file);
            } else {
                setError('Please upload a valid image file.');
            }
        }
    };

    useEffect(() => {
        setImagePreview(imageUrl);
    }, []);

    // console.log('imagePreview------------', imagePreview);
    // console.log('imageUrl------------', imageUrl);

    return (
        <div style={{ textAlign: 'center' }}>
            <label
                htmlFor="imageUpload"
                style={{
                    cursor: 'pointer',
                    display: 'inline-flex',
                    alignItems: 'center',
                }}
            >
                <div className="relative w-full">
                    <div className="flex h-52 w-52 items-center justify-center overflow-hidden rounded-full border-2 ">
                        <img
                            src={imagePreview || imageUrl || uploadIcon}
                            alt="Upload Icon"
                            style={{
                                objectFit: 'cover',

                                width: '100%',
                                height: '100%',
                            }}
                        />
                    </div>
                    <div
                        className="absolute bottom-[5%] right-0 w-12 rounded-full bg-[#2C2C2E] p-3"
                        style={{ fontSize: '16px', color: '#000' }}
                    >
                        {/* <CameraFilled className="text-5xl" /> */}
                        <img
                            src="../assets/Camera.svg"
                            alt="Upload Icon"
                            // style={{
                            //     width: '100%',
                            //     height: '100%',
                            // }}
                        />
                    </div>
                </div>
            </label>
            <input
                id="imageUpload"
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                style={{ display: 'none' }} // Hide the default input element
            />
            {error && <p style={{ color: 'red' }}>{error}</p>}
        </div>
    );
};

export default ImageUpload;

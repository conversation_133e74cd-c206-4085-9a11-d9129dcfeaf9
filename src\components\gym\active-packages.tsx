import { Button, Collapse, Form, Pagination, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    ActivePurchasePricingPackagesByClient,
    InActivePurchasePricingPackagesByClient,
} from '~/redux/actions/pricing-actions';
import { formatCamelCase, formatDate } from '../common/function';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import BookingSuspensionModal from '~/screens/bookings/bookingSuspension-modal';

interface ActivePackageProps {
    clientId: string;
}

const ActivePackages: React.FC<ActivePackageProps> = ({ clientId }) => {
    const topRef = useRef<HTMLDivElement>(null);

    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const [loader, startLoader, endLoader] = useLoader();
    const [isSuspensionModalVisible, setIsSuspensionModalVisible] =
        useState<boolean>(false);
    const [purchaseData, setPurchaseData] = useState<any>({});

    const activePageParam = Number(params.activePage);
    const activePageSizeParam = Number(params.activePageSize);
    const inActivePageParam = Number(params.inActivePage);
    const inActivePageSizeParam = Number(params.inActivePageSize);

    const [activeCurrentPage, setActiveCurrentPage] = useState(
        !isNaN(activePageParam) ? activePageParam : 1
    );
    const [inActiveCurrentPage, setInActiveCurrentPage] = useState(
        !isNaN(inActivePageParam) ? inActivePageParam : 1
    );
    const [activePageSize, setActivePageSize] = useState(
        !isNaN(activePageSizeParam) ? activePageSizeParam : 10
    );
    const [inActivePageSize, setInActivePageSize] = useState(
        !isNaN(inActivePageSizeParam) ? inActivePageSizeParam : 10
    );
    const store = useAppSelector((state) => ({
        purchasepricingPackageList:
            state.pricing_store.purchasepricingListByClient,
        purchasePricingCount:
            state.pricing_store.purchasepricingListByClientCount,
        inActivePurchasepricingListByClient:
            state.pricing_store.inActivePurchasepricingListByClient,
        inActivePurchasepricingListByClientCount:
            state.pricing_store.inActivePurchasepricingListByClientCount,
    }));

    // console.log('Store-------------------, ', store.purchasepricingPackageList);

    function paginate(page: number, pageSize: number, isActive: boolean) {
        if (isActive) {
            setActiveCurrentPage(page);
            setActivePageSize(pageSize);
        } else {
            setInActiveCurrentPage(page);
            setInActivePageSize(pageSize);
        }
        topRef.current?.scrollIntoView({ behavior: 'smooth' });
        navigate(
            `?activePage=${
                isActive ? page : activeCurrentPage
            }&activePageSize=${
                isActive ? pageSize : activePageSize
            }&inActivePage=${
                !isActive ? page : inActiveCurrentPage
            }&inActivePageSize=${!isActive ? pageSize : inActivePageSize}`,
            { replace: true }
        );
    }

    const handleClose = (refresh = false) => {
        if (refresh)
            dispatch(
                ActivePurchasePricingPackagesByClient({
                    userId: clientId,
                    page: activeCurrentPage,
                    pageSize: activePageSize,
                })
            ).unwrap();
        setPurchaseData(null);
        setIsSuspensionModalVisible(false);
    };

    const handleSupensionModalOpen = (data: any) => {
        setPurchaseData({
            ...data,
            startDate: data?.createdAt,
            endDate: data?.expiryDate,
        });
        setIsSuspensionModalVisible(true);
    };

    useEffect(() => {
        startLoader();
        if (clientId) {
            dispatch(
                ActivePurchasePricingPackagesByClient({
                    userId: clientId,
                    page: activeCurrentPage,
                    pageSize: activePageSize,
                })
            )
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [clientId, activeCurrentPage, activePageSize]);

    useEffect(() => {
        startLoader();
        if (clientId) {
            dispatch(
                InActivePurchasePricingPackagesByClient({
                    userId: clientId,
                    page: inActiveCurrentPage,
                    pageSize: inActivePageSize,
                })
            )
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [clientId, inActiveCurrentPage, inActivePageSize]);

    function isPackageSuspended(item: any) {
        const now = new Date();
        if (item.suspensions && item.suspensions.length > 0)
            return item.suspensions.some((suspension: any) => {
                const fromDate = new Date(suspension.fromDate);
                const suspensionEndDate = new Date(suspension.endDate);
                return now >= fromDate && now <= suspensionEndDate;
            });
        return false;
    }

    return (
        <>
            <div ref={topRef} className="w-full lg:w-[100%] lg:py-10 lg:pr-10 ">
                <Space className="w-full" direction="vertical" size="large">
                    <div className="flex flex-col gap-5 ">
                        <Collapse
                            className="custom-collapse client-profile-collapse mt-10 w-full rounded-2xl"
                            bordered={false}
                            defaultActiveKey={['1', '2']}
                            items={[
                                {
                                    key: '1',
                                    label: (
                                        <div className="w-fit border-b-2 border-primary text-2xl font-semibold">
                                            Active Packages
                                        </div>
                                    ),
                                    children:
                                        // loader ? (
                                        //     <div className="flex justify-center items-center py-10">
                                        //         <FullLoader state={true}  />
                                        //     </div>
                                        // ) :
                                        store.purchasepricingPackageList
                                            ?.length > 0 ? (
                                            <>
                                                <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                                    {store.purchasepricingPackageList.map(
                                                        (
                                                            item: any,
                                                            index: number
                                                        ) => {
                                                            const isSuspended =
                                                                isPackageSuspended(
                                                                    item
                                                                );
                                                            return (
                                                                <div
                                                                    key={index}
                                                                    className="mt-4 flex flex-col gap-5 rounded-2xl border bg-white px-5 py-5"
                                                                >
                                                                    {/* <div className="flex flex-row justify-end">
                                                                        <Button
                                                                            onClick={() =>
                                                                                handleSupensionModalOpen(
                                                                                    item
                                                                                )
                                                                            }
                                                                            className="text-4 bg-purpleLight px-8 py-4"
                                                                            type="primary"
                                                                        >
                                                                            {isSuspended
                                                                                ? 'Resume Suspension'
                                                                                : 'Suspend'}
                                                                        </Button>
                                                                    </div> */}
                                                                    <div className="flex flex-row justify-between">
                                                                        <div className="flex w-[40%] flex-row items-center gap-5">
                                                                            <div className="flex w-[35%] flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Package
                                                                                    Name
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560]">
                                                                                {
                                                                                    item?.packageName
                                                                                }
                                                                            </p>
                                                                        </div>
                                                                        <div className="flex w-[40%] flex-row items-center gap-5">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Service
                                                                                    Category
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560] lg:w-[65%]">
                                                                                {item?.serviceNames?.join(
                                                                                    ', '
                                                                                )}
                                                                            </p>
                                                                        </div>
                                                                        <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Location
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560]">
                                                                                {
                                                                                    item?.location
                                                                                }
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div className="flex flex-row justify-between">
                                                                        <div className="flex w-[40%] flex-row items-center gap-5">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Expiry
                                                                                    Date
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560]">
                                                                                {formatDate(
                                                                                    item?.expiryDate
                                                                                )}
                                                                            </p>
                                                                        </div>
                                                                        <div className="flex w-[40%] flex-row items-center gap-5">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Sub
                                                                                    Type
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560] lg:w-[65%]">
                                                                                {item?.appointmentTypeName?.join(
                                                                                    ', '
                                                                                )}
                                                                            </p>
                                                                        </div>
                                                                        <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Total
                                                                                    No.
                                                                                    of
                                                                                    Sessions
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560]">
                                                                                {item?.sessionType ===
                                                                                'day_pass'
                                                                                    ? `${item?.dayPassLimit} X Day Pass(es)`
                                                                                    : item?.sessionType ===
                                                                                      'unlimited'
                                                                                    ? 'Unlimited'
                                                                                    : item?.totalSessions}
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div className="flex flex-row justify-between">
                                                                        <div className="flex w-[40%] flex-row items-center gap-5">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Service
                                                                                    Type
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560] lg:w-[65%]">
                                                                                {formatCamelCase(
                                                                                    item?.type
                                                                                )}
                                                                            </p>
                                                                        </div>
                                                                        {/* <div className="flex w-[40%] flex-row items-center gap-5">
                                                                    <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                        <p className="text-xl font-medium text-[#1A3353]">
                                                                            Bundle
                                                                            Name
                                                                        </p>
                                                                        <p>:</p>
                                                                    </div>
                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                        Package
                                                                        Name
                                                                    </p>
                                                                </div> */}
                                                                        {item?.isBundledPricing ? (
                                                                            <>
                                                                                <div className="flex w-[40%] flex-row items-center gap-5">
                                                                                    <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                                        <p className="text-xl font-medium text-[#1A3353]">
                                                                                            Bundle
                                                                                            Name
                                                                                        </p>
                                                                                        <p>
                                                                                            :
                                                                                        </p>
                                                                                    </div>
                                                                                    <div className="rounded-md bg-green-200 px-3 py-1">
                                                                                        <p className="truncated-text text-xl text-[#455560] ">
                                                                                            {
                                                                                                item.bundledPricingName
                                                                                            }
                                                                                        </p>
                                                                                    </div>
                                                                                </div>
                                                                            </>
                                                                        ) : (
                                                                            <>
                                                                                <div className="flex w-[40%] flex-row items-center gap-5">
                                                                                    <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                                        <p className="text-xl font-medium text-[#1A3353]">
                                                                                            Price
                                                                                        </p>
                                                                                        <p>
                                                                                            :
                                                                                        </p>
                                                                                    </div>
                                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                                        {
                                                                                            item.price
                                                                                        }
                                                                                    </p>
                                                                                </div>
                                                                            </>
                                                                        )}
                                                                        {item.type ===
                                                                        'courses' ? (
                                                                            <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                                <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                                    <p className="text-xl font-medium text-[#1A3353]">
                                                                                        Total
                                                                                        Enrollments
                                                                                    </p>
                                                                                    <p>
                                                                                        :
                                                                                    </p>
                                                                                </div>
                                                                                <p className="text-xl text-[#455560]">
                                                                                    {item.sessionConsumed
                                                                                        ? item.sessionConsumed
                                                                                        : '0'}
                                                                                </p>
                                                                            </div>
                                                                        ) : (
                                                                            <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                                <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                                    <p className="text-xl font-medium text-[#1A3353]">
                                                                                        Remaining
                                                                                        No.
                                                                                        of
                                                                                        Sessions
                                                                                    </p>
                                                                                    <p>
                                                                                        :
                                                                                    </p>
                                                                                </div>
                                                                                <p className="text-xl text-[#455560]">
                                                                                    {item.remainingSession
                                                                                        ? item.remainingSession
                                                                                        : '0'}
                                                                                </p>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                    {item?.type !== 'courses' && (
                                                                        <div className="flex flex-row justify-center pb-5">
                                                                            <Button
                                                                                onClick={() =>
                                                                                    handleSupensionModalOpen(
                                                                                        item
                                                                                    )
                                                                                }
                                                                                className="text-4 bg-purpleLight px-8 py-4"
                                                                                type="primary"
                                                                            >
                                                                                {isSuspended
                                                                                    ? 'Resume Suspension'
                                                                                    : 'Suspend'}
                                                                            </Button>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            );
                                                        }
                                                    )}
                                                </div>
                                                <div className="flex justify-center py-10">
                                                    <Pagination
                                                        current={
                                                            activeCurrentPage
                                                        }
                                                        total={
                                                            store.purchasePricingCount
                                                        }
                                                        pageSize={
                                                            activePageSize
                                                        }
                                                        onChange={(
                                                            page,
                                                            pageSize
                                                        ) =>
                                                            paginate(
                                                                page,
                                                                pageSize,
                                                                true
                                                            )
                                                        }
                                                        // hideOnSinglePage
                                                    />
                                                </div>
                                            </>
                                        ) : (
                                            <div className="rounded-2xl bg-[#f5f5f5] py-20 text-center text-xl text-[#455560]">
                                                No active package
                                            </div>
                                        ),
                                },
                            ]}
                            expandIconPosition="right"
                        />
                        <Collapse
                            className="custom-collapse client-profile-collapse mt-10 w-full rounded-2xl"
                            bordered={false}
                            defaultActiveKey={[]}
                            items={[
                                {
                                    key: '1',
                                    label: (
                                        <div className="w-fit border-b-2 border-primary text-2xl font-semibold">
                                            Inactive Packages
                                        </div>
                                    ),
                                    children:
                                        // loader ? (
                                        //     <div className="flex justify-center items-center py-10">
                                        //         <FullLoader state={true}  />
                                        //     </div>
                                        // ) :
                                        store
                                            .inActivePurchasepricingListByClient
                                            ?.length > 0 ? (
                                            <>
                                                <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                                    {store.inActivePurchasepricingListByClient.map(
                                                        (
                                                            item: any,
                                                            index: number
                                                        ) => (
                                                            <div
                                                                key={index}
                                                                className="mt-4 flex flex-col gap-5 rounded-2xl border bg-white px-5 py-5"
                                                            >
                                                                <div className="flex flex-row justify-between">
                                                                    <div className="flex w-[40%] flex-row items-center gap-5">
                                                                        <div className="flex w-[35%] flex-row items-center justify-between">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Package
                                                                                Name
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="text-xl text-[#455560]">
                                                                            {
                                                                                item?.packageName
                                                                            }
                                                                        </p>
                                                                    </div>
                                                                    <div className="flex w-[40%] flex-row items-center gap-5">
                                                                        <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Service
                                                                                Category
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="text-xl text-[#455560] lg:w-[65%]">
                                                                            {item?.serviceNames?.join(
                                                                                ', '
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                    <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                        <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Location
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="text-xl text-[#455560]">
                                                                            {
                                                                                item?.location
                                                                            }
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                                <div className="flex flex-row justify-between">
                                                                    <div className="flex w-[40%] flex-row items-center gap-5">
                                                                        <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Expiry
                                                                                Date
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="text-xl text-[#455560]">
                                                                            {formatDate(
                                                                                item?.expiryDate
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                    <div className="flex w-[40%] flex-row items-center gap-5">
                                                                        <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Sub
                                                                                Type
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="text-xl text-[#455560] lg:w-[65%]">
                                                                            {item?.appointmentTypeName?.join(
                                                                                ', '
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                    <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                        <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Total
                                                                                No.
                                                                                of
                                                                                Sessions
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="text-xl text-[#455560]">
                                                                            {item?.sessionType ===
                                                                            'day_pass'
                                                                                ? `${item?.dayPassLimit} X Day Pass(es)`
                                                                                : item?.sessionType ===
                                                                                  'unlimited'
                                                                                ? 'Unlimited'
                                                                                : item?.totalSessions}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                                <div className="flex flex-row justify-between">
                                                                    <div className="flex w-[40%] flex-row items-center gap-5">
                                                                        <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Service
                                                                                Type
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="text-xl text-[#455560] lg:w-[65%]">
                                                                            {formatCamelCase(
                                                                                item?.type
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                    {/* <div className="flex w-[40%] flex-row items-center gap-5">
                                                                    <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                        <p className="text-xl font-medium text-[#1A3353]">
                                                                            Bundle
                                                                            Name
                                                                        </p>
                                                                        <p>:</p>
                                                                    </div>
                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                        Package
                                                                        Name
                                                                    </p>
                                                                </div> */}
                                                                    {item?.isBundledPricing ? (
                                                                        <>
                                                                            <div className="flex w-[40%] flex-row items-center gap-5">
                                                                                <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                                    <p className="text-xl font-medium text-[#1A3353]">
                                                                                        Bundle
                                                                                        Name
                                                                                    </p>
                                                                                    <p>
                                                                                        :
                                                                                    </p>
                                                                                </div>
                                                                                <div className="rounded-md bg-green-200 px-3 py-1">
                                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                                        {
                                                                                            item.bundledPricingName
                                                                                        }
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <div className="flex w-[40%] flex-row items-center gap-5">
                                                                                <div className="flex flex-row items-center justify-between lg:w-[35%]">
                                                                                    <p className="text-xl font-medium text-[#1A3353]">
                                                                                        Price
                                                                                    </p>
                                                                                    <p>
                                                                                        :
                                                                                    </p>
                                                                                </div>
                                                                                <div
                                                                                    className={
                                                                                        item.paymentStatus ===
                                                                                            'refund' ||
                                                                                        item.paymentStatus ===
                                                                                            'cancel'
                                                                                            ? 'rounded-md bg-red-200 px-3 py-1'
                                                                                            : ''
                                                                                    }
                                                                                >
                                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                                        {item.paymentStatus ===
                                                                                        'refund'
                                                                                            ? 'Refunded'
                                                                                            : item.paymentStatus ===
                                                                                              'cancel'
                                                                                            ? 'Cancelled'
                                                                                            : item.price}
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </>
                                                                    )}
                                                                    {item.type ===
                                                                    'courses' ? (
                                                                        <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Total
                                                                                    Enrollments
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560]">
                                                                                {item.sessionConsumed
                                                                                    ? item.sessionConsumed
                                                                                    : '0'}
                                                                            </p>
                                                                        </div>
                                                                    ) : (
                                                                        <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Remaining
                                                                                    No.
                                                                                    of
                                                                                    Sessions
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560]">
                                                                                {item.remainingSession
                                                                                    ? item.remainingSession
                                                                                    : '0'}
                                                                            </p>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                                <div className="flex justify-center py-10">
                                                    <Pagination
                                                        current={
                                                            inActiveCurrentPage
                                                        }
                                                        total={
                                                            store.inActivePurchasepricingListByClientCount
                                                        }
                                                        pageSize={
                                                            inActivePageSize
                                                        }
                                                        onChange={(
                                                            page,
                                                            pageSize
                                                        ) =>
                                                            paginate(
                                                                page,
                                                                pageSize,
                                                                false
                                                            )
                                                        }
                                                        // hideOnSinglePage
                                                    />
                                                </div>
                                            </>
                                        ) : (
                                            <div className="rounded-2xl bg-[#f5f5f5] py-20 text-center text-xl text-[#455560]">
                                                No Inactive package
                                            </div>
                                        ),
                                },
                            ]}
                            expandIconPosition="right"
                        />
                    </div>
                </Space>
            </div>
            {isSuspensionModalVisible && (
                <BookingSuspensionModal
                    visible={isSuspensionModalVisible}
                    onClose={handleClose}
                    purchaseId={purchaseData?._id}
                    purchaseData={purchaseData}
                />
            )}
        </>
    );
};

export default ActivePackages;

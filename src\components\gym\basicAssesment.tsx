import React, { useEffect, useState } from 'react';
import {
    CollapseProps,
    Collapse,
    Space,
    Form,
    Input,
    Button,
    Select,
} from 'antd';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
import { UpdateBasicAssessment } from '~/redux/actions/customer-action';
import { useAppDispatch } from '~/hooks/redux-hooks';

const { Option } = Select;

interface BasicAssesmentProps {
    settingData: {
        assessment: {
            weight: boolean;
            measurement: boolean;
        };
    };
    clientId: string | undefined;
    facilityId: string;
    clientDetails?: any;
}

const BasicAssesment: React.FC<BasicAssesmentProps> = ({
    settingData,
    clientId,
    facilityId,
    clientDetails,
}) => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [editable, setEditable] = useState<boolean>(false);
    const onChange = (key: string | string[]) => {
        console.log(key);
    };
    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;

    const toggleEditable = () => {
        setEditable((prev) => !prev);
    };

    useEffect(() => {
        if (clientDetails) {
            form.setFieldsValue({
                weight: clientDetails?.basicAssessments?.weight,
                height: clientDetails?.basicAssessments?.height,
                goal: clientDetails?.basicAssessments?.goal,
                calf: clientDetails?.basicAssessments?.measurement?.calf,
                theigh: clientDetails?.basicAssessments?.measurement?.theigh,
                hip: clientDetails?.basicAssessments?.measurement?.hip,
                weist: clientDetails?.basicAssessments?.measurement?.weist,
                chest: clientDetails?.basicAssessments?.measurement?.chest,
                shoulder:
                    clientDetails?.basicAssessments?.measurement?.shoulder,
                biceps: clientDetails?.basicAssessments?.measurement?.biceps,
                forearm: clientDetails?.basicAssessments?.measurement?.forearm,
            });
        }
    }, [clientDetails, form]);

    const onFinish = (values: any) => {
        console.log('Form values:----------', values);

        const removeEmptyFields = (obj: any) => {
            return Object.fromEntries(
                Object.entries(obj).filter(
                    ([, value]) =>
                        value !== undefined && value !== null && value !== ''
                )
            );
        };

        const payload = {
            basicAssessments: {
                ...removeEmptyFields({
                    weight: values.weight,
                    height: values.height,
                    goal: values.goal,
                }),
                measurement: removeEmptyFields({
                    calf: values.calf,
                    theigh: values.theigh,
                    hip: values.hip,
                    weist: values.weist,
                    chest: values.chest,
                    shoulder: values.shoulder,
                    biceps: values.biceps,
                    forearm: values.forearm,
                }),
            },

            facilityId: facilityId,
        };

        // console.log('Data in payload-------------', payload, clientId);
        dispatch(
            UpdateBasicAssessment({ reqData: payload, clientId: clientId })
        );
    };

    return (
        <div className="w-full lg:py-10 lg:pr-10 ">
            <Form
                name="personal-info"
                layout="vertical"
                size="large"
                autoComplete="off"
                initialValues={{ gender: 'select' }}
                form={form}
                disabled={!editable}
                onFinish={onFinish}
            >
                <div
                    className="mb-2 flex justify-end"
                    onClick={() => toggleEditable()}
                >
                    <img
                        src="/icons/common/edit.svg"
                        alt="edit"
                        className="ms-auto h-[20px] cursor-pointer"
                    />
                </div>
                <Space className="w-full" direction="vertical" size="large">
                    {settingData?.assessment?.weight && (
                        <Collapse
                            className="custom-collapse client-profile-collapse w-full rounded-2xl"
                            bordered={false}
                            defaultActiveKey={['1', '2']}
                            onChange={onChange}
                            expandIcon={customExpandIcon}
                            items={[
                                {
                                    key: '1',
                                    label: (
                                        <div className="w-fit border-b-2 border-primary font-semibold">
                                            Height and Weight
                                        </div>
                                    ),
                                    children: (
                                        <div>
                                            <Form.Item
                                                label="Weight"
                                                name="weight"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please input your Weight',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter your Weight" />
                                            </Form.Item>

                                            {/* Height Input */}

                                            <Form.Item
                                                label="Height"
                                                name="height"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Height',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Height" />
                                            </Form.Item>
                                            <Form.Item
                                                label="Goal"
                                                name="goal"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Goal',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Goal" />
                                            </Form.Item>
                                        </div>
                                    ),
                                },
                            ]}
                            // className="custom-collapse"
                            expandIconPosition="right"
                        />
                    )}

                    {settingData?.assessment?.measurement && (
                        <Collapse
                            className="custom-collapse client-profile-collapse w-full rounded-2xl"
                            bordered={false}
                            defaultActiveKey={['1', '2']}
                            onChange={onChange}
                            expandIcon={customExpandIcon}
                            items={[
                                {
                                    key: '1',
                                    label: (
                                        <div className="w-fit border-b-2 border-[#406CF9] font-semibold">
                                            Measurements
                                        </div>
                                    ),
                                    children: (
                                        <div>
                                            <Form.Item
                                                label="Chest"
                                                name="chest"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input Your Chest Measurement ',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Chest Measurement" />
                                            </Form.Item>

                                            {/* Height Input */}

                                            <Form.Item
                                                label="Shoulder"
                                                name="shoulder"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Shoulder Measurement',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Shoulder Measurement" />
                                            </Form.Item>
                                            <Form.Item
                                                label="Biceps"
                                                name="biceps"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Biceps Measurement',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Biceps Measurement" />
                                            </Form.Item>
                                            <Form.Item
                                                label="Forearm"
                                                name="forearm"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Forearm Measurement',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Forearm Measurement" />
                                            </Form.Item>
                                            <Form.Item
                                                label="Weist"
                                                name="weist"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Weist Measurement',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Weist Measurement" />
                                            </Form.Item>

                                            <Form.Item
                                                label="Hip"
                                                name="hip"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Hip Measurement',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Hip Measurement" />
                                            </Form.Item>

                                            <Form.Item
                                                label="Theigh"
                                                name="theigh"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Theigh Measurement',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Theigh Measurement" />
                                            </Form.Item>

                                            <Form.Item
                                                label="Calf"
                                                name="calf"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please input your Calf Measurement',
                                                    },
                                                ]}
                                            >
                                                <Input placeholder="Enter Your Calf Measurement" />
                                            </Form.Item>
                                        </div>
                                    ),
                                },
                            ]}
                            // className="custom-collapse"
                            expandIconPosition="right"
                        />
                    )}
                </Space>

                {editable && (
                    <div className="flex flex-row justify-end gap-5">
                        <Form.Item>
                            <div className="mt-10" style={{ display: 'flex' }}>
                                <Button className="border-2 border-[#1A3353] px-20 py-7 text-2xl">
                                    Cancel
                                </Button>
                            </div>
                        </Form.Item>
                        <Form.Item>
                            <div
                                className="mt-10"
                                style={{ display: 'flex', gap: '10px' }}
                            >
                                <Button
                                    className="px-20 py-7 text-2xl"
                                    type="primary"
                                    htmlType="submit"
                                >
                                    Confirm
                                </Button>
                            </div>
                        </Form.Item>
                    </div>
                )}
            </Form>
        </div>
    );
};

export default BasicAssesment;

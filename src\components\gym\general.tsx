import React, { useEffect, useMemo, useState } from 'react';
import {
    CollapseProps,
    Collapse,
    Space,
    Form,
    Input,
    Button,
    Select,
    DatePicker,
    ConfigProvider,
    Checkbox,
} from 'antd';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    BillingCityList,
    CityById,
    CityList,
} from '~/redux/actions/common-action';
import { useDebounce, useEmailMobileValidator } from '~/hooks/useDebounce';
import { useSelector } from 'react-redux';
import { ClientsDetails, UpdateClient } from '~/redux/actions/customer-action';
import { useParams } from 'wouter';
import dayjs from 'dayjs';
import { FacilitiesList } from '~/redux/actions/facility-action';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import { PhoneNumberUtil, PhoneNumberType } from 'google-libphonenumber';
import { GetSettings } from '~/redux/actions/settings-actions';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { useLoader } from '~/hooks/useLoader';

const phoneUtil = PhoneNumberUtil.getInstance();
const { Option } = Select;

const General = ({ clientId }: any) => {
    const searchParams = new URLSearchParams(window.location.search);
    const userId = searchParams.get('userId');
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        clientDetails: state.customer_store.customerDetails,
        clientOnboarding: state.settings_store.clientOnboarding,
    }));
    const [editable, setEditable] = useState<boolean>(false);
    const [citySearchText, setCitySearchText] = useState<string>('');
    const [b2BcitySearchText, setB2bCitySearchText] = useState<string>('');
    const [selectedState, setSelectedState] = useState<string | null>(null);
    const [b2BselectedState, setB2bSelectedState] = useState<string | null>(
        null
    );
    const [isSelectedState, setIsSelectedState] = useState<boolean>(false);
    const [isB2BSelectedState, setIsB2BSelectedState] =
        useState<boolean>(false);
    const debouncedRequest = useDebounce((callback) => callback(), 200);
    const { facilityList } = useSelector((state: any) => state.facility_store);
    const [facilitySearch, setFacilitySearch] = useState<string>('');
    const [phoneError, setPhoneError] = useState<string | null>(null);
    const [dialCode, setDialCode] = useState<string>('');
    const [countryCode, setCountryCode] = useState<string>('IN');
    const [minLength, setMinLength] = useState<number>(0); // Default min length
    const [maxLength, setMaxLength] = useState<number>(0); // Default max length
    const [phone, setPhone] = useState('');
    const [loader, showLoader, hideLoader] = useLoader();

    const { countryList, cityList, billingCityList } = useSelector(
        (state: any) => state.common_store
    );

    // console.log('-------------------', store.clientDetails);
    const [form] = Form.useForm();

    const updatePhoneLengthLimits = async (country: string) => {
        try {
            const exampleNumber = await phoneUtil.getExampleNumberForType(
                country,
                PhoneNumberType.MOBILE
            );
            setMinLength(0);
            setMaxLength(0);
            if (exampleNumber) {
                console.log(exampleNumber.getNationalNumber());
                const exampleNumberLength = exampleNumber
                    .getNationalNumber()
                    .toString().length;
                console.log(exampleNumber);

                setMinLength(exampleNumberLength);
                setMaxLength(exampleNumberLength);
                return;
            }
        } catch (error) {
            console.error('Error fetching phone number metadata:', error);
        }
    };

    const removeCountryCode = (fullNumber: string, dialCode: string) => {
        if (fullNumber.startsWith(dialCode)) {
            return fullNumber.slice(dialCode.length);
        }
        return fullNumber;
    };

    const validatePhoneNumber = async (value: string) => {
        console.log(value);
        if (!value) {
            setPhoneError('Please enter a phone number');
            return false;
        }

        await updatePhoneLengthLimits(countryCode);
        const phoneNumberWithoutCountryCode = removeCountryCode(
            value,
            dialCode
        );

        if (phoneNumberWithoutCountryCode.length < minLength) {
            setPhoneError(
                `Phone number is too short. Minimum length: ${minLength}`
            );
            return false;
        }
        if (phoneNumberWithoutCountryCode.length > maxLength) {
            setPhoneError(
                `Phone number is too long. Maximum length: ${maxLength}`
            );
            return false;
        }

        setPhoneError(null);
        return true;
    };

    const handlePhoneChange = async (value: string, country: any) => {
        const newCountryCode = country?.countryCode?.toUpperCase() || 'IN';
        const newDialCode = country?.dialCode || '91';

        if (newCountryCode !== countryCode) {
            setPhone(newDialCode);
            form.setFieldsValue({ mobile: '', countryCode: `+${newDialCode}` });
            setPhoneError(null);
            setCountryCode(newCountryCode);
            await updatePhoneLengthLimits(newCountryCode);
        } else {
            if (value.length <= maxLength + newDialCode.length) {
                setPhone(value);
                form.setFieldsValue({
                    mobile: removeCountryCode(value, newDialCode),
                    countryCode: `+${newDialCode}`,
                });
            }
            setDialCode(newDialCode);
            validatePhoneNumber(value);
        }
    };
    const onChange = (key: string | string[]) => {
        console.log(key);
    };
    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;
    useEffect(() => {
        if (!userId) {
            setEditable(true);
        }
    }, []);

    useEffect(() => {
        if (store.clientDetails) {
            const stateId = store.clientDetails?.address?.state;
            const cityId = store.clientDetails?.address?.city;
            const B2bCityId = store.clientDetails?.businessAddress?.city;

            dispatch(CityList({ cityId: cityId, page: 1, pageSize: 50 })).then(
                (res: any) => {
                    // form.setFieldsValue({
                    //     city: res?.payload?.data?.data[0]?._id,
                    // });
                }
            );
            dispatch(
                BillingCityList({ cityId: B2bCityId, page: 1, pageSize: 50 })
            ).then((res: any) => {
                // form.setFieldsValue({
                //     B2bcity: B2bCityId,
                // });
            });
            form.setFieldsValue({
                firstName: store.clientDetails?.firstName,
                lastName: store.clientDetails?.lastName,
                gender: store.clientDetails?.gender,
                dob: store.clientDetails?.dob
                    ? dayjs(store.clientDetails?.dob)
                    : null,
                email: store.clientDetails?.email,
                activityLevel: store.clientDetails?.activityLevel,
                mobile: store.clientDetails?.mobile,
                countryCode: store.clientDetails?.countryCode,
                street: store.clientDetails?.address?.street,
                addressLine1: store.clientDetails?.address?.addressLine1,
                addressLine2: store.clientDetails?.address?.addressLine2,
                city: store.clientDetails?.address?.city,
                facilityId: store.clientDetails?.facilityId,
                postalCode: store.clientDetails?.address?.postalCode,
                state: stateId,
                country: store.clientDetails?.address?.country,
                emergencyContactPhone:
                    store.clientDetails?.emergencyContactPhone,
                emergencyContactPerson:
                    store.clientDetails?.emergencyContactPerson,
                B2bGST: store.clientDetails?.businessAddress?.gstNumber,
                B2bBusinessName:
                    store.clientDetails?.businessAddress?.businessName,
                B2bAddress1: store.clientDetails?.businessAddress?.addressLine1,
                B2bAddress2: store.clientDetails?.businessAddress?.addressLine2,
                B2bcity: store.clientDetails?.businessAddress?.city,
                B2bstate: store.clientDetails?.businessAddress?.state,
                B2bpostalCode: store.clientDetails?.businessAddress?.postalCode,
                isPrimaryB2BAddress:
                    store.clientDetails?.businessAddress?.isDefault,
                isPrimaryAddress: store.clientDetails?.address?.isDefault,
            });
            if (store?.clientDetails?.mobile) {
                const fullPhoneNumber =
                    store.clientDetails?.countryCode +
                    store.clientDetails.mobile;
                console.log(fullPhoneNumber, 'full Phone Number');
                setPhone(fullPhoneNumber);
                if (!editable) {
                    setPhoneError(null);
                }
            }
        }
    }, [store.clientDetails, form]);

    const CountryOptions = countryList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const handleStateChange = (value: string) => {
        setSelectedState(value);
        setIsSelectedState(true);
        form.setFieldValue('city', null);
        dispatch(CityList({ stateId: value, page: 1, pageSize: 50 }));
    };

    const handleBillingStateChange = (value: string) => {
        setB2bSelectedState(value);
        setIsB2BSelectedState(true);
        form.setFieldValue('B2bcity', null);
        dispatch(BillingCityList({ stateId: value, page: 1, pageSize: 50 }));
    };

    useEffect(() => {
        if (citySearchText) {
            debouncedRequest(() => {
                dispatch(
                    CityList({
                        stateId: selectedState,
                        page: 1,
                        pageSize: 50,
                        search: citySearchText,
                    })
                );
            });
        }
    }, [citySearchText]);

    useEffect(() => {
        if (b2BcitySearchText) {
            debouncedRequest(() => {
                dispatch(
                    BillingCityList({
                        stateId: b2BselectedState,
                        page: 1,
                        pageSize: 50,
                        search: b2BcitySearchText,
                    })
                );
            });
        }
    }, [b2BcitySearchText]);

    useEffect(() => {
        if (facilitySearch) {
            dispatch(
                FacilitiesList({
                    page: 1,
                    pageSize: 30,
                    search: facilitySearch,
                })
            );
        } else {
            dispatch(FacilitiesList({ page: 1, pageSize: 30 }));
        }
    }, [facilitySearch]);

    useEffect(() => {
        dispatch(GetSettings({})).unwrap();
    }, []);

    const CityOptions = cityList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const BillingCityOptions = Array.isArray(billingCityList)
        ? billingCityList?.map((item: any) => ({
              value: item._id,
              label: item.name,
              id: item._id,
          }))
        : [];

    const FacilityOptions = facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const toggleEditable = () => {
        setEditable((prev) => !prev);
    };

    const validateEmailAndPhone = useEmailMobileValidator();

    const onFinish = (values: any) => {
        console.log('Form values:----------', values);
        showLoader();
        const policiesWithoutId = store.clientDetails?.policies?.map(
            (policy: any) => {
                return {
                    policyType: policy.policyType,
                    isEnabled: policy.isEnabled,
                };
            }
        );

        const hasBusinessAddress =
            values.B2bGST ||
            values.B2bBusinessName ||
            values.B2bAddress1 ||
            values.B2bAddress2 ||
            values.B2bcity ||
            values.B2bstate ||
            values.B2bpostalCode;

        const payload = {
            firstName: values.firstName,
            lastName: values.lastName,
            dob: values.dob,
            gender: values.gender,
            activityLevel: values.activityLevel,
            mobile: values.mobile,
            email: values.email ? values.email : undefined,
            countryCode: values.countryCode,
            address: {
                addressLine1: values.addressLine1,
                addressLine2: values.addressLine2,
                postalCode: values.postalCode
                    ? Number(values.postalCode)
                    : null,
                city: values.city,
                state: values.state,
                country: 'India',
                isDefault: values.isPrimaryAddress,
            },
            businessAddress: {
                gstNumber: values.B2bGST,
                businessName: values.B2bBusinessName,
                addressLine1: values.B2bAddress1,
                addressLine2: values.B2bAddress2,
                city: values.B2bcity,
                state: values.B2bstate,
                postalCode: values.B2bpostalCode
                    ? Number(values.B2bpostalCode)
                    : null,
                isDefault: values.isPrimaryB2BAddress,
            },
            isBusiness: hasBusinessAddress ? true : false,
            emergencyContactPerson: values.emergencyContactPerson,
            emergencyContactPhone: values.emergencyContactPhone,
            policies: policiesWithoutId,
            facilityId: values.facilityId,
            photo: store.clientDetails?.photo,
        };

        // console.log('Data in payload-------------', payload);
        dispatch(UpdateClient({ reqData: payload, clientId: clientId }))
            .then((res: any) => {
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    dispatch(ClientsDetails({ clientId: clientId }));
                    setEditable(false);
                }
            })
            .catch((error: any) => {
                console.error('Error updating client:', error);
                hideLoader();
            })
            .finally(() => {
                hideLoader();
            });
    };
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasClientUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.CLIENTS_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    return (
        <div className="w-full lg:py-10 lg:pr-10 ">
            <Form
                name="personal-info"
                layout="vertical"
                size="large"
                form={form}
                onFinish={onFinish}
                disabled={!editable}
                autoComplete="off"
                initialValues={{ gender: 'select', countryCode: 'IN' }}
                // form={form}
            >
                {(hasClientUpdatePermission ||
                    store.role === RoleType.ORGANIZATION) && (
                    <div
                        className="mb-2 flex justify-end"
                        onClick={() => toggleEditable()}
                    >
                        <img
                            src="/icons/common/edit.svg"
                            alt="edit"
                            className="ms-auto h-[20px] cursor-pointer"
                        />
                    </div>
                )}
                <Space className="w-full" direction="vertical" size="large">
                    <ConfigProvider
                        theme={{
                            components: {
                                Collapse: {
                                    contentBg: '#f8f8f8',
                                    headerBg: '#f8f8f8',
                                },
                            },
                        }}
                    >
                        <div className="flex flex-col gap-5 ">
                            <Collapse
                                className=" custom-collapse client-profile-collapse w-full rounded-2xl bg-[#f8f8f8] "
                                bordered={false}
                                defaultActiveKey={['1', '2']}
                                onChange={onChange}
                                expandIcon={customExpandIcon}
                                items={[
                                    {
                                        key: '1',
                                        label: (
                                            <div className="w-fit border-b-2 border-primary text-[15px] font-semibold">
                                                Personal Details
                                            </div>
                                        ),
                                        children: (
                                            <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                                <Form.Item
                                                    label="First Name"
                                                    name="firstName"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please input first name',
                                                        },
                                                    ]}
                                                >
                                                    <Input placeholder="Enter first Name" />
                                                </Form.Item>

                                                <Form.Item
                                                    label="Last Name"
                                                    name="lastName"
                                                >
                                                    <Input placeholder="Enter last Name" />
                                                </Form.Item>

                                                {/* Gender Input */}

                                                <Form.Item
                                                    label="Gender"
                                                    name="gender"
                                                    rules={[
                                                        {
                                                            required: false,
                                                            message:
                                                                'Please select gender',
                                                        },
                                                    ]}
                                                >
                                                    <Select placeholder="Select Gender">
                                                        <Option value="male">
                                                            Male
                                                        </Option>
                                                        <Option value="female">
                                                            Female
                                                        </Option>
                                                        <Option value="other">
                                                            Other
                                                        </Option>
                                                    </Select>
                                                </Form.Item>

                                                {/* D.O.B Input */}

                                                <Form.Item
                                                    label="D.O.B"
                                                    name="dob"
                                                    rules={[
                                                        {
                                                            required: false,
                                                            message:
                                                                'Please input D.O.B',
                                                        },
                                                    ]}
                                                >
                                                    <DatePicker
                                                        popupClassName="custom-datepicker"
                                                        placeholder="DD/MM/YYYY"
                                                        format="DD/MM/YYYY"
                                                        style={{
                                                            width: '100%',
                                                        }}
                                                    />
                                                </Form.Item>

                                                {/* Email Input */}

                                                <Form.Item
                                                    label="Email"
                                                    name="email"
                                                    rules={[
                                                        {
                                                            required: false,

                                                            message:
                                                                'Please enter email address',
                                                        },
                                                        {
                                                            type: 'email',
                                                            message:
                                                                'Please enter valid email',
                                                        },
                                                        {
                                                            validator: (
                                                                _,
                                                                value
                                                            ) =>
                                                                value
                                                                    ? validateEmailAndPhone(
                                                                          value,
                                                                          true,
                                                                          store
                                                                              .clientDetails
                                                                              ?.userId
                                                                      )
                                                                    : Promise.resolve(),
                                                        },
                                                    ]}
                                                >
                                                    <Input
                                                        type="email"
                                                        placeholder="Enter Email"
                                                        disabled={
                                                            !editable ||
                                                            store.clientDetails
                                                                .parent
                                                        }
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    label="Facility"
                                                    name="facilityId"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please select facility!',
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        showSearch
                                                        onSearch={(value) =>
                                                            setFacilitySearch(
                                                                value
                                                            )
                                                        }
                                                        placeholder="Select facility"
                                                        filterOption={(
                                                            input,
                                                            option
                                                        ) =>
                                                            String(
                                                                option?.label ??
                                                                    ''
                                                            )
                                                                .toLowerCase()
                                                                ?.includes(
                                                                    input.toLowerCase()
                                                                )
                                                        }
                                                        options={
                                                            FacilityOptions
                                                        }
                                                    />
                                                </Form.Item>
                                                {store.clientOnboarding
                                                    ?.showProficiencyLevel && (
                                                    <Form.Item
                                                        label="Proficiency Level"
                                                        name="activityLevel"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    'Please input activity level',
                                                            },
                                                        ]}
                                                    >
                                                        <Select
                                                            showSearch
                                                            placeholder="Select Activity Level"
                                                            filterOption={(
                                                                input,
                                                                option
                                                            ) =>
                                                                (
                                                                    option?.label ??
                                                                    ''
                                                                )
                                                                    ?.toLowerCase()
                                                                    ?.includes(
                                                                        input.toLowerCase()
                                                                    )
                                                            }
                                                            options={store.clientOnboarding?.proficiencyLevel?.map(
                                                                (obj: any) => ({
                                                                    label: obj.value,
                                                                    value: obj.value,
                                                                })
                                                            )}
                                                        />
                                                    </Form.Item>
                                                )}
                                                {/* <Form.Item
                                                    label="Country Code"
                                                    name="countryCode"
                                                    rules={[
                                                        {
                                                            required: false,
                                                            message:
                                                                'Please enter Country Code',
                                                        },
                                                    ]}
                                                >
                                                    <Input
                                                        type="text"
                                                        // maxLength={10}
                                                        onInput={(e: any) => {
                                                            e.target.value = e.target.value.replace(/[^0-9+]/g, '');
                                                        }}
                                                        placeholder="Enter  Country  Code"
                                                    />
                                                </Form.Item> */}
                                                {/* <Form.Item
                                                    label="Mobile"
                                                    name="mobile"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please input Mobile',
                                                        },
                                                    ]}
                                                >
                                                    <Input
                                                        type="text"
                                                        maxLength={10}
                                                        onInput={(e: any) => {
                                                            e.target.value =
                                                                e.target.value.replace(
                                                                    /[^0-9]/g,
                                                                    ''
                                                                );
                                                        }}
                                                        placeholder="Enter Mobile"
                                                    />
                                                </Form.Item> */}

                                                {/* Hidden Fields for Country Code and Mobile Number */}
                                                <Form.Item
                                                    label="Phone Number"
                                                    validateTrigger={[
                                                        'onBlur',
                                                        'onSubmit',
                                                    ]}
                                                    help={
                                                        editable && phoneError
                                                    }
                                                    validateStatus={
                                                        editable && phoneError
                                                            ? 'error'
                                                            : ''
                                                    }
                                                >
                                                    <PhoneInput
                                                        inputProps={{
                                                            required: editable,
                                                            readOnly: !editable,
                                                        }}
                                                        country="in"
                                                        inputStyle={{
                                                            width: '100%',
                                                            height: '34px',
                                                            backgroundColor:
                                                                editable &&
                                                                !store
                                                                    .clientDetails
                                                                    .parent
                                                                    ? '#ffffff'
                                                                    : '#f0f0f0',
                                                            border:
                                                                editable &&
                                                                !store
                                                                    .clientDetails
                                                                    .parent &&
                                                                phoneError
                                                                    ? '1px solid red'
                                                                    : '1px solid #d9d9d9',
                                                            borderRadius: '6px',
                                                        }}
                                                        enableSearch={true}
                                                        value={phone}
                                                        onChange={
                                                            handlePhoneChange
                                                        }
                                                        isValid={() => true}
                                                        onBlur={() =>
                                                            validatePhoneNumber(
                                                                phone
                                                            )
                                                        }
                                                        disabled={
                                                            !editable ||
                                                            !!store
                                                                .clientDetails
                                                                .parent
                                                        }
                                                    />
                                                </Form.Item>

                                                {/* Hidden Fields for Country Code and Mobile Number */}
                                                <Form.Item
                                                    name="countryCode"
                                                    hidden
                                                >
                                                    <Input />
                                                </Form.Item>
                                                <Form.Item name="mobile" hidden>
                                                    <Input />
                                                </Form.Item>
                                            </div>
                                        ),
                                    },
                                ]}
                                // className="custom-collapse w-full rounded-2xl bg-[F2F2F280]"
                                // className="custom-collapse"
                                expandIconPosition="end"
                            />
                            {!store.clientDetails.parent && (
                                <>
                                    <Collapse
                                        bordered={false}
                                        defaultActiveKey={['1', '2']}
                                        onChange={onChange}
                                        expandIcon={customExpandIcon}
                                        className=" client-profile-collapse w-full rounded-2xl bg-[#f8f8f8]"
                                        items={[
                                            {
                                                key: '1',
                                                label: (
                                                    <div className="w-fit border-b-2 border-primary font-semibold">
                                                        Address
                                                    </div>
                                                ),
                                                children: (
                                                    <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                                        <Form.Item
                                                            name="isPrimaryAddress"
                                                            valuePropName="checked"
                                                        >
                                                            <ConfigProvider
                                                                theme={{
                                                                    token: {
                                                                        // colorBorder: '#8143D1',
                                                                        colorPrimary:
                                                                            '#8143D1',
                                                                        colorPrimaryHover:
                                                                            '#8143D1',
                                                                    },
                                                                }}
                                                            >
                                                                <Checkbox
                                                                    onChange={(
                                                                        e
                                                                    ) => {
                                                                        if (
                                                                            e
                                                                                .target
                                                                                .checked
                                                                        ) {
                                                                            form.setFieldsValue(
                                                                                {
                                                                                    isPrimaryAddress:
                                                                                        true,
                                                                                    isPrimaryB2BAddress:
                                                                                        false,
                                                                                }
                                                                            );
                                                                        } else {
                                                                            form.setFieldsValue(
                                                                                {
                                                                                    isPrimaryAddress:
                                                                                        false,
                                                                                }
                                                                            );
                                                                        }
                                                                    }}
                                                                >
                                                                    Set as
                                                                    Primary
                                                                    Address
                                                                </Checkbox>
                                                            </ConfigProvider>
                                                        </Form.Item>
                                                        <Form.Item
                                                            label="Address 1"
                                                            name="addressLine1"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please input street',
                                                                },
                                                            ]}
                                                        >
                                                            <Input placeholder="Enter Street Name" />
                                                        </Form.Item>
                                                        <Form.Item
                                                            label="Address 2"
                                                            name="addressLine2"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please input street',
                                                                },
                                                            ]}
                                                        >
                                                            <Input placeholder="Enter Street Name" />
                                                        </Form.Item>

                                                        {/* State Input */}

                                                        <Form.Item
                                                            label="State"
                                                            name="state"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        true,
                                                                    message:
                                                                        'Please select state!',
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                showSearch
                                                                placeholder="Select state"
                                                                filterOption={(
                                                                    input,
                                                                    option
                                                                ) =>
                                                                    String(
                                                                        option?.label ??
                                                                            ''
                                                                    )
                                                                        ?.toLowerCase()
                                                                        .includes(
                                                                            input.toLowerCase()
                                                                        )
                                                                }
                                                                options={
                                                                    CountryOptions
                                                                }
                                                                onChange={
                                                                    handleStateChange
                                                                }
                                                            />
                                                        </Form.Item>

                                                        {/* City Input  */}

                                                        <Form.Item
                                                            label="City"
                                                            name="city"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please select city!',
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                showSearch
                                                                disabled={
                                                                    !isSelectedState
                                                                }
                                                                onSearch={(
                                                                    value
                                                                ) =>
                                                                    setCitySearchText(
                                                                        value
                                                                    )
                                                                }
                                                                placeholder="Select city"
                                                                filterOption={(
                                                                    input,
                                                                    option
                                                                ) =>
                                                                    String(
                                                                        option?.label ??
                                                                            ''
                                                                    )
                                                                        .toLowerCase()
                                                                        .includes(
                                                                            input.toLowerCase()
                                                                        )
                                                                }
                                                                options={
                                                                    CityOptions
                                                                }
                                                            />
                                                        </Form.Item>

                                                        {/* Pin Code Input */}

                                                        <Form.Item
                                                            label="Postal Code"
                                                            name="postalCode"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please input Postal Code',
                                                                },
                                                                {
                                                                    pattern:
                                                                        /^[0-9]{6}$/,
                                                                    message:
                                                                        'Postal Code must be exactly 6 digits and contain only numbers',
                                                                },
                                                            ]}
                                                        >
                                                            <Input
                                                                maxLength={6}
                                                                placeholder="Enter Postal Code"
                                                            />
                                                        </Form.Item>
                                                        <Form.Item
                                                            label="Emergency Contact person"
                                                            name="emergencyContactPerson"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please input Emergency Contact',
                                                                },
                                                            ]}
                                                        >
                                                            <Input
                                                                type="text"
                                                                placeholder="Enter person name"
                                                            />
                                                        </Form.Item>
                                                        <Form.Item
                                                            label="Emergency Contact Number"
                                                            name="emergencyContactPhone"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please input Emergency Contact',
                                                                },
                                                            ]}
                                                        >
                                                            <Input
                                                                type="number"
                                                                placeholder="Enter  Mobile"
                                                            />
                                                        </Form.Item>
                                                    </div>
                                                ),
                                            },
                                        ]}
                                        // className="custom-collapse"
                                        expandIconPosition="end"
                                    />
                                    <Collapse
                                        bordered={false}
                                        defaultActiveKey={[]}
                                        onChange={onChange}
                                        expandIcon={customExpandIcon}
                                        className=" client-profile-collapse w-full rounded-2xl bg-[#f8f8f8]"
                                        items={[
                                            {
                                                key: '1',
                                                label: (
                                                    <div className="w-fit border-b-2 border-primary font-semibold">
                                                        B2B Billing (Optional)
                                                    </div>
                                                ),
                                                children: (
                                                    <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                                        <Form.Item
                                                            name="isPrimaryB2BAddress"
                                                            valuePropName="checked"
                                                        >
                                                            <ConfigProvider
                                                                theme={{
                                                                    token: {
                                                                        // colorBorder: '#8143D1',
                                                                        colorPrimary:
                                                                            '#8143D1',
                                                                        colorPrimaryHover:
                                                                            '#8143D1',
                                                                    },
                                                                }}
                                                            >
                                                                <Checkbox
                                                                    onChange={(
                                                                        e
                                                                    ) => {
                                                                        if (
                                                                            e
                                                                                .target
                                                                                .checked
                                                                        ) {
                                                                            form.setFieldsValue(
                                                                                {
                                                                                    isPrimaryB2BAddress:
                                                                                        true,
                                                                                    isPrimaryAddress:
                                                                                        false,
                                                                                }
                                                                            );
                                                                        } else {
                                                                            form.setFieldsValue(
                                                                                {
                                                                                    isPrimaryB2BAddress:
                                                                                        false,
                                                                                }
                                                                            );
                                                                        }
                                                                    }}
                                                                >
                                                                    Set as
                                                                    Primary
                                                                    Address
                                                                </Checkbox>
                                                            </ConfigProvider>
                                                        </Form.Item>
                                                        <Form.Item
                                                            label="GST No."
                                                            name="B2bGST"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please enter GST No.',
                                                                },
                                                            ]}
                                                        >
                                                            <Input placeholder="Enter GST No." />
                                                        </Form.Item>
                                                        <Form.Item
                                                            label="Business Name"
                                                            name="B2bBusinessName"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please enter business name.',
                                                                },
                                                            ]}
                                                        >
                                                            <Input placeholder="Enter business name" />
                                                        </Form.Item>

                                                        <Form.Item
                                                            label="Address 1"
                                                            name="B2bAddress1"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please enter address',
                                                                },
                                                            ]}
                                                        >
                                                            <Input placeholder="Enter address" />
                                                        </Form.Item>

                                                        <Form.Item
                                                            label="Address 2"
                                                            name="B2bAddress2"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please enter address 2',
                                                                },
                                                            ]}
                                                        >
                                                            <Input placeholder="Enter address 2" />
                                                        </Form.Item>

                                                        <Form.Item
                                                            label="State"
                                                            name="B2bstate"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please select state',
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                showSearch
                                                                placeholder="Select state"
                                                                filterOption={(
                                                                    input,
                                                                    option
                                                                ) =>
                                                                    String(
                                                                        option?.label ??
                                                                            ''
                                                                    )
                                                                        ?.toLowerCase()
                                                                        .includes(
                                                                            input.toLowerCase()
                                                                        )
                                                                }
                                                                options={
                                                                    CountryOptions
                                                                }
                                                                onChange={
                                                                    handleBillingStateChange
                                                                }
                                                            />
                                                        </Form.Item>

                                                        <Form.Item
                                                            label="City"
                                                            name="B2bcity"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please select city',
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                showSearch
                                                                disabled={
                                                                    !isB2BSelectedState
                                                                }
                                                                onSearch={(
                                                                    value
                                                                ) =>
                                                                    setB2bCitySearchText(
                                                                        value
                                                                    )
                                                                }
                                                                placeholder="Select city"
                                                                filterOption={(
                                                                    input,
                                                                    option
                                                                ) =>
                                                                    String(
                                                                        option?.label ??
                                                                            ''
                                                                    )
                                                                        .toLowerCase()
                                                                        .includes(
                                                                            input.toLowerCase()
                                                                        )
                                                                }
                                                                options={
                                                                    BillingCityOptions
                                                                }
                                                            />
                                                        </Form.Item>

                                                        <Form.Item
                                                            label="Postal Code"
                                                            name="B2bpostalCode"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        false,
                                                                    message:
                                                                        'Please enter postal code',
                                                                },
                                                                {
                                                                    pattern:
                                                                        /^[0-9]{6}$/,
                                                                    message:
                                                                        'Postal Code must be exactly 6 digits and contain only numbers',
                                                                },
                                                            ]}
                                                        >
                                                            <Input
                                                                placeholder="Enter postal code"
                                                                maxLength={6}
                                                            />
                                                        </Form.Item>
                                                    </div>
                                                ),
                                            },
                                        ]}
                                        // className="custom-collapse"
                                        expandIconPosition="end"
                                    />
                                </>
                            )}
                        </div>
                    </ConfigProvider>
                </Space>

                {editable && (
                    <div className="flex flex-row justify-end gap-5">
                        <Form.Item>
                            <div className="mt-10" style={{ display: 'flex' }}>
                                <Button
                                    onClick={() => setEditable(false)}
                                    className="border-1 border-[#1A3353] px-20 py-7 text-2xl"
                                >
                                    Cancel
                                </Button>
                            </div>
                        </Form.Item>
                        <Form.Item>
                            <div
                                className="mt-10"
                                style={{ display: 'flex', gap: '10px' }}
                            >
                                <Button
                                    className="bg-purpleLight px-20 py-7 text-2xl"
                                    type="primary"
                                    htmlType="submit"
                                    loading={loader}
                                >
                                    Save
                                </Button>
                            </div>
                        </Form.Item>
                    </div>
                )}
            </Form>
        </div>
    );
};

export default General;

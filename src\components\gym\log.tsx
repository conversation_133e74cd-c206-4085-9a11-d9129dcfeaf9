import { ConfigProvider, Tabs, TabsProps } from 'antd';
import React from 'react';
import Timeline from '../log/timeline';
import {
    FieldTimeOutlined,
    FileZipOutlined,
    FormOutlined,
} from '@ant-design/icons';
import FollowUpNotes from '../log/followUp-notes';
import DocumentLocker from '../log/document-Locker';

const Log = () => {
    const onChange = (key: string) => {
        console.log(key);
    };

    const items: TabsProps['items'] = [
        {
            key: '1',
            label: (
                <div className="flex  items-center">
                    {/* <img src={Asset.profileIconTab} /> */}
                    <FieldTimeOutlined className="text-2xl" />
                    <div>Timeline</div>
                </div>
            ),
            children: <Timeline />,
        },
        {
            key: '2',
            label: (
                <div className="flex items-center">
                    {/* <img src={Asset.profileIconTab} /> */}
                    <FormOutlined className="text-2xl" />
                    <div>Follow up and Notes</div>
                </div>
            ),
            children: <FollowUpNotes />,
        },
        {
            key: '3',
            label: (
                <div className="flex items-center">
                    {/* <img src={Asset.profileIconTab} /> */}
                    <FileZipOutlined className="text-2xl" />
                    <div>Document Locker</div>
                </div>
            ),
            children: <DocumentLocker />,
        },
    ];
    return (
        <>
            <div className=" mt-10  ">
                <ConfigProvider
                    theme={{
                        components: {},
                    }}
                >
                    <Tabs
                        defaultActiveKey="1"
                        items={items}
                        onChange={onChange}
                        tabPosition="left"
                        renderTabBar={(props, DefaultTabBar) => (
                            <DefaultTabBar {...props}>
                                {(node) => (
                                    <div
                                        style={{
                                            backgroundColor:
                                                node.key === props.activeKey
                                                    ? 'rgba(129, 67, 209, 0.1)'
                                                    : 'transparent',
                                            marginBottom: '20px',
                                            borderRadius: '4px',
                                            transition: 'background-color 0.3s',
                                        }}
                                    >
                                        {node}
                                    </div>
                                )}
                            </DefaultTabBar>
                        )}
                    />
                </ConfigProvider>
            </div>
        </>
    );
};

export default Log;

import {
    FileAddOutlined,
    LoadingOutlined,
    PlusOutlined,
} from '@ant-design/icons';
import { Button, Checkbox, Form, Upload } from 'antd';
import React, { useEffect, useState } from 'react';
import { formatStringWithSpaces } from '../common/function';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { UpdatePolicies } from '~/redux/actions/customer-action';

interface PoliciesProps {
    settingData: any;
    clientId: string | undefined;
    facilityId: string;
    clientDetails?: any;
}
const Policies: React.FC<PoliciesProps> = ({
    settingData,
    clientId,
    facilityId,
    clientDetails,
}) => {
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();
    const [imageUrl, setImageUrl] = useState();
    const dispatch = useAppDispatch();
    const [selectedPolicies, setSelectedPolicies] = useState<string[]>([]);
    const { policies } = settingData || {};

    console.log('Selected Policies:', selectedPolicies);

    const handleCheckboxChange = (policyKey: string) => {
        setSelectedPolicies((prevSelected) => {
            if (prevSelected?.includes(policyKey)) {
                return prevSelected.filter((key) => key !== policyKey);
            } else {
                return [...prevSelected, policyKey];
            }
        });
    };

    console.log('clientDetails----------------', clientDetails);
    useEffect(() => {
        if (clientDetails) {
            const clientPolicies =
                clientDetails?.policies?.map(
                    (policy: any) => policy.policyType
                ) || [];
            setSelectedPolicies(clientPolicies);
            form.setFieldsValue({
                policiesData: clientPolicies,
            });
        }
    }, [clientDetails, form]);

    const uploadButton = (
        <button
            style={{
                border: 0,
                background: 'none',
            }}
            type="button"
        >
            {loading ? (
                <LoadingOutlined />
            ) : (
                <FileAddOutlined className="text-4xl" />
            )}
            <div className="mt-3 text-lg text-[#455560]">
                Drop your JPG PNG here
            </div>
        </button>
    );

    const handleSubmit = () => {
        const formattedPolicies = selectedPolicies?.map((policyName) => {
            const policy: any = {
                policyType: policyName,
                isEnabled: true,
            };
            if (imageUrl) {
                policy.documentUrl = imageUrl;
            }
            return policy;
        });

        const payload = {
            facilityId: facilityId,
            policies: formattedPolicies,
        };
        // console.log('Payload---------------', payload);
        dispatch(UpdatePolicies({ reqData: payload, clientId: clientId }));
    };

    return (
        <div className="pt-20">
            <div className="lg:w-[100%] lg:rounded-3xl lg:bg-[#f8f8f8f8] lg:p-10 @sm:p-4">
                <Form
                    name="policies-info"
                    layout="vertical"
                    size="large"
                    autoComplete="off"
                    initialValues={{ gender: 'select' }}
                    form={form}
                    // disabled={!editable}
                    onFinish={handleSubmit}
                >
                    <div className="flex bg-[#ffff] lg:flex-row lg:p-8 @sm:flex-col @sm:gap-6">
                        <div className="flex flex-col gap-5 lg:w-[60%] lg:pt-5">
                            <p className="text-xl font-semibold  text-[#1A3353]">
                                Policies
                            </p>
                            <Form.Item
                                label=""
                                name="policiesData"
                                rules={[
                                    {
                                        required: false,
                                        message: 'Please select policies',
                                    },
                                ]}
                            >
                                <div className="flex flex-col gap-3">
                                    {policies &&
                                        Object.keys(policies).map(
                                            (policyKey) =>
                                                policies[policyKey] && (
                                                    <Checkbox
                                                        key={policyKey}
                                                        onChange={() =>
                                                            handleCheckboxChange(
                                                                policyKey
                                                            )
                                                        }
                                                        checked={selectedPolicies?.includes(
                                                            policyKey
                                                        )}
                                                    >
                                                        {formatStringWithSpaces(
                                                            policyKey
                                                        )}
                                                    </Checkbox>
                                                )
                                        )}
                                </div>
                            </Form.Item>
                        </div>
                        {/* <div className=" lg:w-[40%]">
                            <div className="border border-[#E6EBF1] p-4 @sm:rounded-lg">
                                <p className="mb-2 text-xl font-medium uppercase text-purpleLight">
                                    Upload Document
                                </p>

                                <Upload
                                    name="avatar"
                                    listType="picture-card"
                                    className="avatar-uploader"
                                    showUploadList={false}
                                    action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                                >
                                    {imageUrl ? (
                                        <img
                                            src={imageUrl}
                                            alt="avatar"
                                            style={{
                                                width: '100%',
                                            }}
                                        />
                                    ) : (
                                        uploadButton
                                    )}
                                </Upload>
                            </div>
                        </div> */}
                    </div>

                    <div className="flex flex-row justify-end gap-5">
                        {/* <Form.Item>
                            <div className="mt-10" style={{ display: 'flex' }}>
                                <Button className="border    border-[#1A3353] px-20 py-7 text-2xl">
                                    Cancel
                                </Button>
                            </div>
                        </Form.Item> */}
                        <Form.Item>
                            <div
                                className="mt-10"
                                style={{ display: 'flex', gap: '10px' }}
                            >
                                <Button
                                    className="bg-purpleLight px-20 py-7 text-2xl text-white"
                                    htmlType="submit"
                                >
                                    Save
                                </Button>
                            </div>
                        </Form.Item>
                    </div>
                </Form>
            </div>
        </div>
    );
};

export default Policies;

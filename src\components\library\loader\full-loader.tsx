import React from 'react';
import { useLocation } from 'wouter';
import SpinnerSvg from './spinner.svg';

export interface IFullLoader {
    state: boolean;
}

const FullLoader: React.FC<IFullLoader> = ({ state }) => {
    const [location] = useLocation();

    if (!state || location.startsWith('/point-of-sales')) {
        return null;
    }

    return (
        <div className="absolute bottom-0 left-0 right-0 top-0 flex items-center justify-center bg-black bg-opacity-20">
            <SpinnerSvg className="fill-current text-white" />
        </div>
    );
};

export default FullLoader;

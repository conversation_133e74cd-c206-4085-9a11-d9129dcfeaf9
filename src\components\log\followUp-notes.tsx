import {
    EditOutlined,
    FieldTimeOutlined,
    FileTextOutlined,
    FormOutlined,
    MailOutlined,
    PhoneOutlined,
} from '@ant-design/icons';
import {
    Button,
    Checkbox,
    ConfigProvider,
    DatePicker,
    Select,
    Steps,
} from 'antd';
import React, { useState } from 'react';

const FollowUpNotes = () => {
    const [current, setCurrent] = useState(0);
    const onChange = (value: any) => {
        console.log('onChange:', value);
        setCurrent(value);
    };
    const description1 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">14 Jan 2023</p>
                    <p className="text-[12px] text-[#72849A]">10:15 AM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium  text-[#1A3353]">
                                Follow up -
                            </p>
                        </div>
                        <div className="w-[85%] ">
                            <p className="text-[13px] text-[#1A3353]">
                                with Uday Vihari
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Assigned to -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Ashwani Sakpal |{' '}
                                <span className="font-medium"> Due : </span> 18
                                Jan 11:22 AM
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Created By -
                            </p>
                        </div>
                        <div className="flex w-[85%] items-center ">
                            <p className="text-[13px] text-[#1A3353]">
                                Ashwani Sakpal |{' '}
                                <span className="font-medium"> Status : </span>{' '}
                                <span className="text-purpleLight">
                                    Upcoming
                                </span>{' '}
                                |{' '}
                            </p>
                            <Checkbox className="ms-2">
                                {' '}
                                <p className="text-[13px]  font-medium text-[#1A3353]">
                                    Mark as complete{' '}
                                </p>
                            </Checkbox>
                            <EditOutlined className="text-primary" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    const description2 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">14 Jan 2023</p>
                    <p className="text-[12px] text-[#72849A]">10:15 AM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium  text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Lorem ipsum dolor sit amet consectetur. Odio
                                morbi diam facilisi facilisis amet venenatis
                                orci. Viverra elit morbi ornare porttitor
                                blandit felis et.
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Added by -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Ashwani Sakpal
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    const description3 = (
        <div>
            <div className="flex items-center pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">14 Jan 2023</p>
                    <p className="text-[12px] text-[#72849A]">10:15 AM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium  text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Lorem ipsum dolor sit amet consectetur. Odio
                                morbi diam facilisi facilisis amet venenatis
                                orci. Viverra elit morbi ornare porttitor
                                blandit felis et.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    return (
        <>
            <div className="flex flex-row items-center justify-between border-b  pb-5">
                <p className="text-[15px] font-semibold text-[#1a3353]">
                    Follow up & Notes
                </p>
                <div className="flex flex-row gap-5">
                    <Button className="h-12 border-1 border-[#1A3353] px-4 text-xl  text-[#1A3353]">
                        <FormOutlined /> Add Follow Up
                    </Button>
                    <Button className="h-12 border-1 border-[#1A3353] px-4 text-xl  text-[#1A3353]">
                        <FieldTimeOutlined /> Add Reminder
                    </Button>
                    <Select
                        allowClear
                        placeholder="Action"
                        className="w-[10vw]"
                        options={[]}
                    />

                    <DatePicker
                        placeholder="DD/MM/YYYY"
                        format="DD/MM/YYYY"
                        className="w-[10vw]"
                        popupClassName="custom-datepicker"
                    />
                </div>
            </div>
            <div className="py-14">
                <ConfigProvider
                    // theme={{
                    //     components: {
                    //         Steps: {
                    //             navArrowColor: '#8143D1',
                    //         },
                    //     },
                    // }}
                    theme={{
                        token: {
                            colorPrimary: '#8143D1',
                            colorBorderSecondary: '#8143D1',
                        },
                    }}
                >
                    <Steps
                        current={current}
                        onChange={onChange}
                        direction="vertical"
                        id="log-stepper"
                        items={[
                            {
                                title: '',
                                icon: (
                                    <PhoneOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description1,
                            },

                            {
                                title: '',
                                icon: (
                                    <MailOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description3,
                            },
                        ]}
                    />
                </ConfigProvider>
            </div>
        </>
    );
};

export default FollowUpNotes;

import React, { useEffect, useState } from 'react';
import {
    Button,
    ConfigProvider,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
} from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { STAFF_ROLE_DROPDOWN } from '~/types/enums-value';
import dayjs, { Dayjs } from 'dayjs';
import Title from 'antd/es/typography/Title';
import { useEmailMobileValidator } from '~/hooks/useDebounce';
import { GetRolesList } from '~/redux/actions/permission-action';

const { Option } = Select;

interface IStaffFormFields {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
    gender: 'male' | 'female' | 'other';
    role: 'webmaster' | 'trainer' | 'other';
    setUpDate: Dayjs;
    facilityId: string[];
}

interface IFacility {
    _id: string;
    facilityName: string;
}

interface IAddEditStaffModal {
    visible: boolean;
    onClose: () => void;
    onSave: (fields: IStaffFormFields, formRef: any) => void;
    onSaveAnother: (fields: IStaffFormFields, formRef: any) => void;
    loader: boolean;
    addOtherStaffLoader: boolean;
}

const AddEditStaffModal: React.FC<IAddEditStaffModal> = ({
    visible,
    onClose,
    onSave,
    onSaveAnother,
    loader,
    addOtherStaffLoader,
}) => {
    const [form] = Form.useForm<IStaffFormFields>();
    const [rolesList, setRolesList] = useState([]);

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
    }));

    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(GetRolesList()).then((res: any) => {
            console.log('res', res?.payload?.data?.data);
            setRolesList(res?.payload?.data?.data || []);
        });
    }, []);

    const validateEmailAndPhone = useEmailMobileValidator();

    const disabledDate = (current: Dayjs) => {
        // Disable dates before today
        return current && current < dayjs().startOf('day');
    };

    const handleClose = () => {
        onClose();
        form.resetFields();
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        itemMarginBottom: 22,
                        verticalLabelMargin: -5,
                    },
                },
                token: {
                    borderRadius: 5,
                },
            }}
        >
            <Modal
                open={visible}
                onOk={handleClose}
                onCancel={handleClose}
                title={
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        }}
                    >
                        <Title level={4} className=" text-[#1a3353]">
                            Add New Staff
                        </Title>
                    </div>
                }
                footer={null}
                closeIcon={true}
                style={{ top: 10 }}
                className="lg:w-[50%]"
            >
                <Form
                    className=" flex flex-col gap-4 pt-10"
                    name="add-trainer"
                    layout="vertical"
                    size="large"
                    autoComplete="off"
                    onFinish={(fields) => onSave(fields, form)}
                    form={form}
                >
                    <Form.Item
                        label="First Name"
                        name="firstName"
                        rules={[
                            {
                                required: true,
                                message: 'Please enter full name',
                            },
                        ]}
                    >
                        <Input placeholder="Enter First Name" />
                    </Form.Item>
                    <Form.Item label="Last Name" name="lastName">
                        <Input placeholder="Enter Last Name" />
                    </Form.Item>
                    <Form.Item
                        label="Email"
                        name="email"
                        rules={[
                            { required: true, message: 'Email is required' },
                            {
                                type: 'email',
                                message: 'Please enter a valid email',
                            },
                            {
                                validator: (_, value) =>
                                    value
                                        ? validateEmailAndPhone(value, true)
                                        : Promise.resolve(),
                            },
                        ]}
                    >
                        <Input placeholder="Enter Email" />
                    </Form.Item>
                    <Form.Item
                        label="Mobile"
                        name="mobile"
                        rules={[
                            {
                                required: true,
                                message: 'Mobile number is required',
                            },
                            {
                                pattern: /^\d{10}$/,
                                message:
                                    'Mobile number must be exactly 10 digits',
                            },
                            {
                                validator: (_, value) =>
                                    value
                                        ? validateEmailAndPhone(value, false)
                                        : Promise.resolve(),
                            },
                        ]}
                    >
                        <Input
                            type="text"
                            maxLength={10}
                            onInput={(e: any) => {
                                e.target.value = e.target.value.replace(
                                    /[^0-9]/g,
                                    ''
                                );
                            }}
                            placeholder="Enter Mobile"
                        />
                    </Form.Item>
                    <Form.Item
                        label="Gender"
                        name="gender"
                        rules={[
                            {
                                required: true,
                                message: 'Please select gender',
                            },
                        ]}
                    >
                        <Select placeholder="Select Gender">
                            <Option value="male">Male</Option>
                            <Option value="female">Female</Option>
                            <Option value="other">Other</Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        label="Role"
                        name="role"
                        rules={[
                            {
                                required: true,
                                message: 'Please select role',
                            },
                        ]}
                    >
                        <Select placeholder="Select Role">
                            {rolesList.map((role: any) => (
                                <Option key={role._id} value={role._id}>
                                    {role.name}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item
                        label="Send Account Setup Email Date"
                        name="setUpDate"
                        rules={[
                            {
                                required: true,
                                message: 'Please select the date',
                            },
                        ]}
                    >
                        <DatePicker
                            popupClassName="custom-datepicker"
                            placeholder="DD/MM/YYYY"
                            format="DD/MM/YYYY"
                            disabledDate={disabledDate}
                            style={{ width: '100%' }}
                        />
                    </Form.Item>
                    <Form.Item
                        label="Branch Name"
                        name="facilityId"
                        rules={[
                            {
                                required: true,
                                message: 'Please select branch name(s)',
                            },
                        ]}
                    >
                        <Select
                            mode="multiple"
                            placeholder="Enter the branch name"
                            filterOption={(input, option) =>
                                (option?.children as unknown as string)
                                    .toLowerCase()
                                    .indexOf(input.toLowerCase()) >= 0
                            }
                        >
                            {store.facilityList.map((facility: IFacility) => (
                                <Option key={facility._id} value={facility._id}>
                                    {facility.facilityName}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <div className="flex flex-row justify-end">
                        <Form.Item>
                            <div style={{ display: 'flex', gap: '10px' }}>
                                <Button
                                    className=" rounded-lg border   border-[#1a3353]  text-xl text-[#1a3353]"
                                    type="default"
                                    onClick={() =>
                                        onSaveAnother(
                                            form.getFieldsValue(),
                                            form
                                        )
                                    }
                                    loading={addOtherStaffLoader}
                                    disabled={loader}
                                >
                                    Save and Add Another
                                </Button>
                                <Button
                                    className="rounded-lg bg-purpleLight px-14   py-7 text-xl text-white  "
                                    htmlType="submit"
                                    loading={loader}
                                    disabled={addOtherStaffLoader}
                                >
                                    Save
                                </Button>
                            </div>
                        </Form.Item>
                    </div>
                </Form>
            </Modal>
        </ConfigProvider>
    );
};

export default AddEditStaffModal;

import React, { useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import {
    Button,
    ConfigProvider,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
} from 'antd';
import LocalConfigProvider from '~/components/common/local-config-provider';
import PasswordChecklist from '~/components/common/password-checklist';

const { Option } = Select;

interface ICreateStaff {
    handleOk?: () => void;
    handleCancel?: () => void;
    visible?: boolean;
    onClose?: () => void;
}

const CreateStaffModal: React.FC<ICreateStaff> = ({
    // handleCancel,
    visible,
    onClose,
}) => {
    const [password, setPassword] = useState('');

    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            itemMarginBottom: 22,
                            verticalLabelMargin: -5,
                        },
                    },
                }}
            >
                <Modal
                    open={visible}
                    onOk={onClose}
                    onCancel={onClose}
                    title={
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                            }}
                        >
                            <span>Create Staff Login</span>
                        </div>
                    }
                    footer={null}
                    closeIcon={true}
                    style={{ top: 10 }}
                    className="lg:w-[50%]"
                >
                    <Form
                        className="pt-10"
                        name="add-trainer"
                        layout="vertical"
                        size="large"
                        autoComplete="off"
                        initialValues={{ country: 'india' }}
                        // form={form}
                    >
                        <Form.Item
                            label="First Name"
                            name="firstName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input first name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter First Name" />
                        </Form.Item>
                        <Form.Item
                            label="Last Name"
                            name="lastName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input lasst name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Last Name" />
                        </Form.Item>
                        <Form.Item
                            label="Email"
                            name="email"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input email',
                                },
                                {
                                    type: 'email',
                                    message: 'The input is not valid E-mail!',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Email" />
                        </Form.Item>
                        <Form.Item
                            label="Password"
                            name="password"
                            rules={[
                                {
                                    required: true,
                                    // message: 'Please input password',
                                },
                            ]}
                        >
                            <Input.Password
                                placeholder="Enter Password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                            />
                        </Form.Item>
                        <PasswordChecklist password={password} />
                        <Form.Item
                            className="pt-6"
                            label="Country"
                            name="country"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select country',
                                },
                            ]}
                        >
                            <Select
                                placeholder="Select Country"
                                disabled={true}
                            >
                                <Option value="india">India</Option>
                            </Select>
                        </Form.Item>
                        <div className="flex flex-row justify-end">
                            <Form.Item>
                                <div style={{ display: 'flex', gap: '10px' }}>
                                    <Button type="primary" htmlType="submit">
                                        Confirm
                                    </Button>
                                </div>
                            </Form.Item>
                        </div>
                    </Form>
                </Modal>
            </ConfigProvider>
        </>
    );
};

export default CreateStaffModal;

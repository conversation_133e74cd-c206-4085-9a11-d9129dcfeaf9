import { CloseOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { ConfigProvider, Menu } from 'antd';
import Sider from 'antd/es/layout/Sider';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'wouter';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import Alertify from '~/services/alertify';
import { RoleType, SUBJECT_TYPE } from '~/types/enums';

const SideBarDuplicate = ({
    collapsed,
    setCollapsed,
}: {
    collapsed: boolean;
    setCollapsed: (value: boolean) => void;
}) => {
    const { role } = useSelector((state: any) => state.auth_store);
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );

    const [selectedKey, setSelectedKey] = useState<string>();
    const [selectedItem, setSelectedItem] = useState<{
        key: string;
        keyPath: string[];
        item: any;
    }>({ key: '', keyPath: [], item: {} });
    const [openKeys, setOpenKeys] = useState<string[]>([]);
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(false);
    const [isCourseActive, setIsCourseActive] = useState(false);
    const [isCourseEnabled, setIsCourseEnabled] = useState(false);
    const [, setLocation] = useLocation();

    // Memoize all permission checks
    const permissions = useMemo(
        () => ({
            customer: all_permissions_for_role?.some((module) =>
                module.subjects?.some(
                    (subject) =>
                        subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING
                )
            ),
            staff: all_permissions_for_role?.some((module) =>
                module.subjects?.some(
                    (subject) =>
                        subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_STAFF_ONBOARDING
                )
            ),
            pos: all_permissions_for_role?.some((module) =>
                module.subjects?.some(
                    (subject) => subject.type === SUBJECT_TYPE.POS
                )
            ),
            pricing: all_permissions_for_role?.some((module) =>
                module.subjects?.some(
                    (subject) => subject.type === SUBJECT_TYPE.PRICING_PRICING
                )
            ),
            booking: all_permissions_for_role?.some((module) =>
                module.subjects?.some(
                    (subject) =>
                        subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING
                )
            ),
            orders: all_permissions_for_role?.some((module) =>
                module.subjects?.some(
                    (subject) => subject.type === SUBJECT_TYPE.ORDER
                )
            ),
            services_setup: all_permissions_for_role?.some((module) =>
                module.subjects?.some(
                    (subject) =>
                        subject.type === SUBJECT_TYPE.SERVICES_SERVICE_SETUP
                )
            ),
        }),
        [all_permissions_for_role]
    );

    const desiredOrder = [
        'Home',
        'Location',
        'Classes',
        'Clients',
        'Appointments',
        'Bookings',
        'Courses',
        'Point of Sale',
        'Insights',
        'Marketing',
        'Services',
        'Store',
        'Orders',
        'Staff',
        'Reports',
        'Settings',
    ];

    const labelOrderMap = desiredOrder.reduce((acc, label, index) => {
        acc[label] = index;
        return acc;
    }, {});

    const items = useMemo(() => {
        const menuItems = [
            ...(role != RoleType.SUPER_ADMIN
                ? [
                      {
                          key: 1,
                          icon: 'home.svg',
                          activeIcon: 'home_active.svg',
                          label: 'Home',
                          link: '/dashboard',
                          children: [
                              {
                                  key: 201,
                                  label: 'Dashboard',
                                  link: '/',
                              },
                              ...(role === RoleType.ORGANIZATION
                                  ? [
                                        {
                                            key: 202,
                                            label: 'Setup Checklist',
                                            link: '/setup-checklist',
                                        },
                                    ]
                                  : []),
                          ],
                      },
                  ]
                : []),
            ...(role === RoleType.SUPER_ADMIN
                ? [
                      {
                          key: 14343,
                          icon: 'organization_vector.svg',
                          activeIcon: 'Active_organization_vector.svg',
                          label: 'Organization',
                          link: '/organizations',
                      },
                      {
                          key: 101010,
                          icon: 'organization_vector.svg',
                          activeIcon: 'Active_organization_vector.svg',
                          label: 'Payment Methods',
                          link: '/admin/payment-methods',
                      },
                      {
                          key: 7,
                          icon: 'organization_vector.svg',
                          activeIcon: 'Active_organization_vector.svg',
                          label: 'Role & Permissions',
                          children: [
                              {
                                  key: 7.1,
                                  label: 'Roles',
                                  link: '/roles-super-admin',
                              },
                              {
                                  key: 7.2,
                                  label: 'Policies & Permissions',
                                  link: '/policies',
                              },
                          ],
                      },
                  ]
                : []),
            ...(role === RoleType.ORGANIZATION || role === RoleType.WEBMASTER
                ? [
                      {
                          key: 4,
                          icon: 'gym.svg',
                          activeIcon: 'activeGym.svg',
                          label: 'Location',
                          link: '/facilities-location',
                      },
                  ]
                : []),
            ...(role !== RoleType.SUPER_ADMIN &&
            (role === RoleType.ORGANIZATION || permissions.customer)
                ? [
                      {
                          key: 8,
                          icon: 'Clients.svg',
                          activeIcon: 'Active_Clients.svg',
                          label: 'Clients',
                          link: '/customers',
                          module: SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING,
                      },
                  ]
                : []),
            ...(role === RoleType.TRAINER
                ? [
                      {
                          key: 1345,
                          icon: 'appoi_svg.svg',
                          activeIcon: 'active_appointment.svg',
                          label: 'Appointments',
                          link: '/appointments',
                      },
                      ...(permissions.booking || role === RoleType.ORGANIZATION
                          ? [
                                {
                                    key: 2343,
                                    icon: 'classes_svg.svg',
                                    activeIcon: 'classes_active.svg',
                                    label: 'Bookings',
                                    link: '/bookings',
                                },
                            ]
                          : []),
                      ...(role === RoleType.ORGANIZATION || permissions.orders
                          ? [
                                {
                                    key: 17,
                                    icon: 'orders_svg.svg',
                                    activeIcon: 'active_orders.svg',
                                    label: 'Orders',
                                    link: '/order-listing',
                                },
                            ]
                          : []),
                      ...(role === RoleType.ORGANIZATION || permissions.pricing
                          ? [
                                {
                                    key: 16,
                                    icon: 'Services&Products.svg',
                                    activeIcon: 'Active_Services.svg',
                                    label: 'Services',
                                    children: [
                                        {
                                            key: 161,
                                            label: 'Pricing',
                                            link: '/pricing',
                                        },
                                    ],
                                },
                            ]
                          : []),
                  ]
                : []),
            ...(role !== RoleType.SUPER_ADMIN &&
            (role === RoleType.ORGANIZATION || permissions.pos)
                ? [
                      {
                          key: 9564,
                          icon: 'POS_svg.svg',
                          activeIcon: 'active_pos.svg',
                          label: 'Point of Sale',
                          link: '/point-of-sales',
                          module: SUBJECT_TYPE.POS,
                      },
                  ]
                : []),
            ...(role === RoleType.ORGANIZATION || role === RoleType.WEBMASTER
                ? [
                      {
                          key: 1345,
                          icon: 'appoi_svg.svg',
                          activeIcon: 'active_appointment.svg',
                          label: 'Appointments',
                          link: '/appointments',
                      },
                      ...(permissions.booking || role === RoleType.ORGANIZATION
                          ? [
                                {
                                    key: 2343,
                                    icon: 'classes_svg.svg',
                                    activeIcon: 'classes_active.svg',
                                    label: 'Bookings',
                                    link: '/bookings',
                                },
                                {
                                    key: 143,
                                    icon: 'classes_svg.svg',
                                    activeIcon: 'classes_active.svg',
                                    label: 'Classes',
                                    link: '/classes-listing',
                                },
                            ]
                          : []),
                      ...(isCourseEnabled
                          ? [
                                {
                                    key: 98765,
                                    icon: 'course_svg.svg',
                                    activeIcon: 'active_course.svg',
                                    label: 'Courses',
                                    link: '/courses-listing',
                                },
                            ]
                          : []),
                      {
                          key: 16,
                          icon: 'Services&Products.svg',
                          activeIcon: 'Active_Services.svg',
                          label: 'Services',
                          children: [
                              {
                                  key: 2202,
                                  label: 'Appointment',
                                  link: '/appointments-types',
                              },
                              {
                                  key: 22043,
                                  label: 'Booking',
                                  link: '/booking-types',
                              },
                              ...(isCourseEnabled
                                  ? [
                                        {
                                            key: 2204,
                                            label: 'Courses',
                                            link: '/courses',
                                        },
                                    ]
                                  : []),
                              ...(role === RoleType.ORGANIZATION ||
                              permissions.pricing
                                  ? [
                                        {
                                            key: 161,
                                            label: 'Pricing',
                                            link: '/pricing',
                                            module: SUBJECT_TYPE.PRICING_PRICING,
                                        },
                                    ]
                                  : []),
                          ],
                      },
                      {
                          key: 26,
                          icon: 'store.svg',
                          activeIcon: 'active-store.svg',
                          label: 'Store',
                          children: [
                              {
                                  key: 22029,
                                  label: 'Inventory',
                                  link: '/store-listing',
                              },
                              {
                                  key: 2204356,
                                  label: 'Products',
                                  link: '/product-listing',
                              },
                              {
                                  key: 22078884,
                                  label: 'Attributes',
                                  link: '/attribute-listing',
                              },
                              {
                                  key: 161111,
                                  label: 'Category',
                                  link: '/categories',
                              },
                          ],
                      },
                      ...(role === RoleType.ORGANIZATION || permissions.orders
                          ? [
                                {
                                    key: 17,
                                    icon: 'orders_svg.svg',
                                    activeIcon: 'active_orders.svg',
                                    label: 'Orders',
                                    link: '/order-listing',
                                    module: SUBJECT_TYPE.ORDER,
                                },
                            ]
                          : []),
                      {
                          key: 15,
                          icon: 'Setting.svg',
                          activeIcon: 'Active_settings.svg',
                          label: 'Settings',
                          link: '/setting',
                      },
                  ]
                : []),
            ...(role === RoleType.FRONT_DESK_ADMIN
                ? [
                      {
                          key: 1345,
                          icon: 'appoi_svg.svg',
                          activeIcon: 'active_appointment.svg',
                          label: 'Appointments',
                          link: '/appointments',
                      },
                      ...(permissions.booking || role === RoleType.ORGANIZATION
                          ? [
                                {
                                    key: 2343,
                                    icon: 'classes_svg.svg',
                                    activeIcon: 'classes_active.svg',
                                    label: 'Bookings',
                                    link: '/bookings',
                                    module: SUBJECT_TYPE.SCHEDULING_BOOKING,
                                },
                            ]
                          : []),
                      {
                          key: 16,
                          icon: 'Services&Products.svg',
                          activeIcon: 'Active_Services.svg',
                          label: 'Services',
                          children: [
                              {
                                  key: 2202,
                                  label: 'Appointment',
                                  link: '/appointments-types',
                              },
                              {
                                  key: 22043,
                                  label: 'Booking',
                                  link: '/booking-types',
                              },
                              ...(role === RoleType.ORGANIZATION ||
                              permissions.pricing
                                  ? [
                                        {
                                            key: 161,
                                            label: 'Pricing',
                                            link: '/pricing',
                                        },
                                    ]
                                  : []),
                          ],
                      },
                      ...(role === RoleType.ORGANIZATION || permissions.orders
                          ? [
                                {
                                    key: 17,
                                    icon: 'orders_svg.svg',
                                    activeIcon: 'active_orders.svg',
                                    label: 'Orders',
                                    link: '/order-listing',
                                    module: SUBJECT_TYPE.ORDER,
                                },
                            ]
                          : []),
                      {
                          key: 15,
                          icon: 'Setting.svg',
                          activeIcon: 'Active_settings.svg',
                          label: 'Settings',
                          link: '/setting',
                      },
                  ]
                : []),
            ...(role !== RoleType.SUPER_ADMIN &&
            (role === RoleType.ORGANIZATION || permissions.staff)
                ? [
                      {
                          key: 12,
                          icon: 'Staff.svg',
                          activeIcon: 'Active_Staff.svg',
                          label: 'Staff',
                          link: '/staffs',
                          module: SUBJECT_TYPE.AUTHENTICATION_STAFF_ONBOARDING,
                      },
                  ]
                : []),
            ...(role != RoleType.SUPER_ADMIN
                ? [
                      {
                          key: 115,
                          icon: 'course_svg.svg',
                          activeIcon: 'active_course.svg',
                          label: 'Reports',
                          link: '/reports',
                          module: SUBJECT_TYPE.REPORTS_REPORT_EXPORT,
                      },
                  ]
                : []),
        ];

        return menuItems.sort((a, b) => {
            const orderA = labelOrderMap[a.label] ?? Infinity;
            const orderB = labelOrderMap[b.label] ?? Infinity;
            return orderA - orderB;
        });
    }, [role, permissions, isCourseEnabled, isCourseActive]);

    const flattenedItems = useMemo(
        () => items.flatMap((item) => [item, ...(item.children || [])]),
        [items]
    );
    const dispatch = useAppDispatch();
    useEffect(() => {
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_setup_courses',
            })
        )
            .then((response: any) => {
                setIsCourseActive(response?.payload?.data?.data?.isActive);
                setIsCourseEnabled(response?.payload?.data?.data?.isEnabled);
            })
            .catch((error: any) =>
                Alertify.error('Error in fetching setting status', error)
            );
    }, []);
    const handleRedirect = ({ key, keyPath, item }: typeof selectedItem) => {
        localStorage.setItem('selectedKey', key);
        setSelectedKey(keyPath[keyPath.length - 1]);
        setOpenKeys(keyPath.slice(0, -1));
        if (item?.link) setLocation(item.link);
        setPinModalVisible(false);
    };

    const handleClick = ({
        key,
        keyPath,
    }: {
        key: string;
        keyPath: string[];
    }) => {
        const item = flattenedItems.find((i) => String(i.key) === key);
        const newSelectedItem = { key, keyPath, item };
        setSelectedItem(newSelectedItem);

        if (
            ['Reports'].includes(item.label) &&
            role !== RoleType.ORGANIZATION
        ) {
            setPinModalVisible(true);
        } else {
            handleRedirect(newSelectedItem);
        }
    };

    useEffect(() => {
        const updateSelectedKey = () => {
            const currentPath = window.location.pathname;
            const isPointOfSalesRoute = /^\/point-of-sales\/[^/]+\/[^/]+$/.test(
                currentPath
            );

            const matchedItem = items.find(
                (item) =>
                    item.link === currentPath ||
                    (isPointOfSalesRoute && item.link === '/point-of-sales') ||
                    item.children?.some((child) => child.link === currentPath)
            );

            if (matchedItem) {
                const newSelectedItem = {
                    key: String(matchedItem.key),
                    keyPath: [String(matchedItem.key)],
                    item: matchedItem,
                };
                setSelectedItem(newSelectedItem);

                if (
                    ['Reports'].includes(matchedItem.label) &&
                    role !== RoleType.ORGANIZATION
                ) {
                    setPinModalVisible(true);
                } else {
                    setSelectedKey(matchedItem.key);
                }
            } else {
                const savedKey = localStorage.getItem('selectedKey');
                setSelectedKey(savedKey || '');
            }
        };

        updateSelectedKey();
        window.addEventListener('popstate', updateSelectedKey);
        return () => window.removeEventListener('popstate', updateSelectedKey);
    }, [items]);

    useEffect(() => {
        if (selectedKey) {
            localStorage.setItem('selectedKey', String(selectedKey));
        }
    }, [selectedKey]);
    const renderChildMenuItem = (child: any) => ({
        key: child.key,
        className:
            String(selectedKey) === String(child.key)
                ? 'bg-[#A77BDF]'
                : 'text-white',
        label: (
            <Link to={child.link}>
                <span
                    className={`m-0 flex items-center text-xl ${
                        collapsed ? 'text-[#1A3353]' : ''
                    } ${
                        openKeys.includes(String(child.key))
                            ? collapsed
                                ? 'mt-5 text-[#1A3353]'
                                : 'text-[#fff]'
                            : ''
                    }`}
                >
                    {child.label}
                </span>
            </Link>
        ),
    });

    const renderMenuItem = (item: any) => ({
        key: item.key,
        className: `
            ${item.children ? 'menu-item-with-children py-1 my-2' : ''} 
            ${
                openKeys.includes(String(item.key))
                    ? 'menu-item-children-open bg-primary'
                    : ''
            }
            ${String(selectedKey) === String(item.key) ? 'bg-primary' : ''}
        `,
        icon: (
            <div
                className={`flex w-fit justify-center ${
                    collapsed ? 'mt-4 w-full' : 'w-1/3'
                }`}
            >
                <img
                    src={`/menuIcons/${
                        String(selectedKey) === String(item.key)
                            ? item.activeIcon
                            : item.children &&
                              openKeys.includes(String(item.key))
                            ? item.activeIcon
                            : item.icon
                    }`}
                    alt="icon"
                />
            </div>
        ),
        label: item.children ? (
            <Link to="#">
                <span
                    className={`m-0 text-xl font-medium ${
                        collapsed ? 'hidden' : ''
                    } ${
                        openKeys.includes(String(item.key)) ||
                        String(selectedKey) === String(item.key)
                            ? 'text-white'
                            : 'text-[#1A3353]'
                    }`}
                >
                    {item.label}
                </span>
            </Link>
        ) : (
            <Link to={item.link}>
                <span
                    className={`m-0 text-xl font-medium ${
                        collapsed ? '' : 'text-[#1A3353]'
                    } ${
                        String(selectedKey) === String(item.key)
                            ? 'text-white'
                            : ''
                    }`}
                >
                    {item.label}
                </span>
            </Link>
        ),
        children: item.children?.map(renderChildMenuItem),
    });

    return (
        <>
            <div className="sticky top-0 h-screen bg-white">
                <Sider
                    trigger={null}
                    collapsed={collapsed}
                    width={window.innerWidth >= 768 ? 260 : '100%'}
                    className="h-full"
                >
                    <div className="flex h-full max-h-screen flex-col justify-between overflow-y-scroll bg-white @sm:hidden @sm:w-0">
                        <div>
                            <div className="sticky top-0 z-10 bg-white py-1 shadow">
                                <img
                                    src="/menuIcons/logo_svg.svg"
                                    alt="logo"
                                    className="mx-auto h-[65px]"
                                />
                            </div>
                            <div className="sidebar-scrollbar overflow-y-scroll">
                                <ConfigProvider theme={{}}>
                                    <Menu
                                        mode="inline"
                                        openKeys={openKeys}
                                        onOpenChange={setOpenKeys}
                                        selectedKeys={[selectedKey as string]}
                                        onClick={handleClick}
                                        items={items.map(renderMenuItem)}
                                    />
                                </ConfigProvider>
                            </div>
                        </div>
                        <a
                            href="http://hopwellness.ai/Release-notes"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <div className="flex cursor-pointer justify-center gap-3 rounded-xl bg-[#f2f2f2] py-2 text-xl text-[#455560]">
                                {!collapsed && <p>Release Notes</p>}
                                <InfoCircleOutlined className="text-xl" />
                            </div>
                        </a>
                    </div>
                </Sider>
            </div>
            <div
                className={`bottom-0 right-0 top-0 z-20 min-h-screen overflow-y-scroll border-0 transition-all duration-300 ease-linear sm:hidden sm:text-center @sm:fixed @sm:bg-[#fff] ${
                    !collapsed
                        ? 'overflow-hidden @sm:w-0 @sm:p-0'
                        : '@sm:w-[100vw] @sm:p-8'
                }`}
            >
                <div className="flex items-center justify-between">
                    <img
                        src="../menuIcons/logo.svg"
                        alt="logo"
                        className="my-0 h-[40px]"
                    />
                    <CloseOutlined
                        className="text-[19px] sm:hidden"
                        onClick={() => setCollapsed(!collapsed)}
                    />
                </div>
                <div className="mt-16">
                    <Menu
                        mode="inline"
                        className="border-0"
                        selectedKeys={[selectedKey as string]}
                        onClick={({ key }) => {
                            setSelectedKey(key);
                            setCollapsed(!collapsed);
                        }}
                        items={items.map(renderMenuItem)}
                    />
                </div>
            </div>
            {pinModalVisible && (
                <ModulePinConfirmationModal
                    visible={pinModalVisible}
                    onConfirm={() => handleRedirect(selectedItem)}
                    isModule={true}
                    module={selectedItem.item?.module}
                    onCancel={() => {
                        setLocation('/setup-checklist');
                        setPinModalVisible(false);
                    }}
                />
            )}
        </>
    );
};

export default SideBarDuplicate;

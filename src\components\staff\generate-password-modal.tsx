// ModalComponent.tsx
import React from 'react';
import { Modal, Button } from 'antd';

interface GeneratePasswordModalProps {
    isVisible: boolean;
    onClose: () => void;
}

const GeneratePasswordModal: React.FC<GeneratePasswordModalProps> = ({
    isVisible,
    onClose,
}) => {
    return (
        <Modal
            title={
                <p className="border-b-2 text-[#1A3353]">Generate Password</p>
            }
            open={isVisible}
            footer={false}
            onCancel={onClose}
        >
            <div className="flex flex-col gap-10">
                <p>Do you want to Reset a Password ?</p>
                <div className="flex justify-end gap-5">
                    <Button className="w-[90px] bg-purpleLight text-white">
                        Yes
                    </Button>
                    <Button
                        onClick={onClose}
                        className="w-[90px] border border-[#1A3353] text-[#1A3353]"
                    >
                        No
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default GeneratePasswordModal;

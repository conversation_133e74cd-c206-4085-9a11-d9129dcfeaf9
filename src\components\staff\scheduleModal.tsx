import React, { useEffect, useState } from 'react';
import {
    Modal,
    Select,
    Radio,
    DatePicker,
    Input,
    Form,
    ConfigProvider,
    Button,
    FormProps,
    Checkbox,
} from 'antd';
import WeeklySchedule from './weeklySchedule';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    AddStaffAvailability,
    GetStaffList,
    UpdateStaffAvailability,
} from '~/redux/actions/staff-action';
import { useDebounce } from '~/hooks/useDebounce';
import {
    FacilityDetails,
    GetFacilityListByStaffId,
} from '~/redux/actions/facility-action';

import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { AvailabilityType, DateRangeType, RoleType } from '~/types/enums';
import {
    capitalizeFirstLetter,
    cleanScheduleAndShowError,
} from '../common/function';
import { useSelector } from 'react-redux';
import { StaffAvailabilityDetails } from '~/redux/actions/appointment-action';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '../library/loader/full-loader';
import {
    getAllClassTypeByStaffId,
    getAllServiceCategories,
} from '~/redux/actions/pay-rate-actions';
import Alertify from '~/services/alertify';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';

dayjs.extend(utc);
dayjs.extend(timezone);

const { TextArea } = Input;
const { Option } = Select;

interface ScheduleModalProps {
    visible: boolean;
    onClose: () => void;
    isStaffDetails?: boolean;
    staffName?: string;
    staffId?: string;
    isEdit?: boolean;
    availabilityId?: string;
    slotUpdateInfo?: any;
    isCalender?: boolean;
    isStaffDetailsPage?: boolean;
    view?: string;
    date?: any;
    slotSelectedInfo?: any;
    isAddUnavailability?: boolean;
}

interface TimeSlot {
    from: Dayjs | null | any;
    to: Dayjs | null | any;
    payRateIds: any;
}

interface WorkingHours {
    [key: string]: TimeSlot[];
}

const InitialWorkingHours = {
    mon: [],
    tue: [],
    wed: [],
    thu: [],
    fri: [],
    sat: [],
    sun: [],
};

const ScheduleModal: React.FC<ScheduleModalProps> = ({
    visible,
    onClose,
    isStaffDetails,
    staffName,
    staffId,
    isEdit,
    availabilityId,
    slotUpdateInfo,
    isStaffDetailsPage,
    view,
    date,
    slotSelectedInfo,
    isAddUnavailability,
}) => {
    const [dateRange, setDateRange] = useState<'Single' | 'Multiple'>('Single');
    const [datePickerOpen, setDatePickerOpen] = useState(false);

    const [form] = Form.useForm();
    const [dateSelectedTime, setDateSelectedTime] = useState<string>();
    const [isCourseEnabled, setIsCourseEnabled] = useState(false);
    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [workingHours, setWorkingHours] = useState<WorkingHours>({
        mon: [],
        tue: [],
        wed: [],
        thu: [],
        fri: [],
        sat: [],
        sun: [],
    });
    const [availabilityType, setAvailabilityType] =
        useState<string>('available');
    const [daySelected, setDaySelected] = useState<string>('');
    const [selectedStaffId, setSelectedStaffId] = useState(staffId);
    const [selectedClassType, setSelectedClassType] = useState(
        'personalAppointment'
    );
    const [staffSearchText, setStaffSearchText] = useState<string>('');
    const debouncedRequest = useDebounce((callback) => callback(), 200);
    const { role, user } = useSelector((state: any) => state.auth_store);
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        user: state.auth_store.user,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
        staffList: state.staff_store.staffListForDropdown,
        ServiceCategoryListData:
            state.service_category_store.ServiceCategoryListData,
        staffAvailabilityDetails:
            state.appointment_store.StaffAvailabilityDetails,
        classTypes: state.pay_rate_store.classTypes,
        serviceCategoryList: state.pay_rate_store.serviceCategoryList,
    }));

    const [repeatOption, setRepeatOption] = useState<string>('weekly');

    console.log('Staff availability details-------------', staffId);

    useEffect(() => {
        if (isAddUnavailability) {
            setAvailabilityType('unavailable');
        }
    }, [isAddUnavailability]);

    useEffect(() => {
        setDateRange(slotUpdateInfo?.isMultiple ? 'Multiple' : 'Single');
        if (
            isStaffDetails &&
            role !== RoleType.TRAINER &&
            (role === RoleType.WEBMASTER ||
                role === RoleType.ORGANIZATION ||
                role === RoleType.FRONT_DESK_ADMIN)
        ) {
            if (staffId) {
                dispatch(
                    GetFacilityListByStaffId({
                        staffId: staffId,
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        const firstLocation = res?.data?.data?.[0]?._id;
                        if (firstLocation) {
                            form.setFieldsValue({ facilityId: firstLocation });
                        }
                    });
                dispatch(
                    getAllClassTypeByStaffId({
                        staffId: staffId,
                    })
                );
            }
        } else if (
            role === RoleType.TRAINER ||
            role === RoleType.WEBMASTER ||
            role === RoleType.ORGANIZATION
        ) {
            if (user._id) {
                dispatch(GetFacilityListByStaffId({ staffId: user._id }));
                dispatch(getAllClassTypeByStaffId({ staffId: user._id }));
            }
        }
    }, [isStaffDetails, isEdit]);

    useEffect(() => {
        if (selectedClassType?.length)
            dispatch(
                getAllServiceCategories({
                    staffId:
                        role === RoleType.TRAINER ? user?._id : selectedStaffId,
                    data: {
                        serviceType: [selectedClassType],
                        page: 1,
                        pageSize: 50,
                    },
                })
            );
    }, [selectedClassType, selectedStaffId]);

    useEffect(() => {
        if (role !== RoleType.TRAINER && visible) {
            debouncedRequest(() => {
                dispatch(
                    GetStaffList({
                        page: 1,
                        role: ['trainer'],
                        pageSize: 10,
                        search: staffSearchText || undefined,
                        forDropdown: true,
                    })
                );
            });
        }
    }, [staffSearchText, isStaffDetails, visible]);

    const fetchStaffList = async (searchText = '', page: number) => {
        const response = await dispatch(
            GetStaffList({
                page,
                pageSize: 50,
                role: ['trainer'],
                search: searchText,
                isActive: true,
            })
        ).unwrap();
        return response?.res?.data?.data?.map((item: any) => ({
            value: item.userId,
            label: item?.firstName + ' ' + item?.lastName,
            id: item._id,
        }));
    };

    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const StaffOption = store.staffList?.map((item: any) => ({
        value: item.userId,
        label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
        id: item._id,
    }));

    const classTypes = [
        {
            label: 'Personal Appointment',
            value: 'personalAppointment',
        },
        // {
        //     label: 'Classes',
        //     value: 'classes',
        // },
        // {
        //     label: 'Bookings',
        //     value: 'bookings',
        // },
        // {
        //     label: 'Courses',
        //     value: 'courses',
        // },
    ];

    const ClassTypeOption = classTypes.filter((item: { value: string }) =>
        store.classTypes?.includes(item.value)
    );

    console.log('isEdit----------------', isEdit);

    useEffect(() => {
        if (isEdit) {
            setAvailabilityType(slotUpdateInfo?.availabilityStatus);
            startLoader();
            setWorkingHours(InitialWorkingHours);
            dispatch(
                StaffAvailabilityDetails({
                    payload: {
                        facilityId: slotUpdateInfo?.facilityId,
                        trainerId: slotUpdateInfo?.trainerId,
                        startDate: slotUpdateInfo?.startDate,
                        endDate: slotUpdateInfo?.endDate,
                        from: slotUpdateInfo.from,
                        // to: slotUpdateInfo.to,
                        dateRange,
                        availabilityStatus: slotUpdateInfo?.availabilityStatus,
                        classType: slotUpdateInfo?.classType,
                    },
                })
            )
                .then((res: any) => {
                    const staffAvailabilityDetails = res.payload?.data?.data;
                    console.log(
                        'staffAvailabilityDetails----------------',
                        staffAvailabilityDetails
                    );
                    const { startDate, timeSlot, privacy } =
                        staffAvailabilityDetails;
                    // Wait for timeSlot to be available
                    if (!timeSlot) return;

                    const dayOfWeek = dayjs(startDate)
                        .format('ddd')
                        ?.toLowerCase();
                    setDaySelected(dayOfWeek);

                    const status = slotUpdateInfo?.availabilityStatus;
                    setAvailabilityType(status);
                    setDateSelectedTime(startDate);
                    if (staffAvailabilityDetails?.userId) {
                        dispatch(
                            GetFacilityListByStaffId({
                                staffId: staffAvailabilityDetails?.userId,
                            })
                        );
                        dispatch(
                            getAllClassTypeByStaffId({
                                staffId: staffAvailabilityDetails?.userId,
                            })
                        );
                    }
                    dispatch(
                        getAllServiceCategories({
                            staffId: availabilityId,
                            data: {
                                serviceType: [timeSlot?.classType],
                                page: 1,
                                pageSize: 50,
                            },
                        })
                    );
                    setWorkingHours((prev) => ({
                        // ...prev,
                        [dayOfWeek]: [timeSlot],
                    }));

                    form.setFieldsValue({
                        staff: isStaffDetails ? staffName : availabilityId,
                        privacy: privacy || timeSlot?.privacy,
                        facilityId: staffAvailabilityDetails?.facilityId,
                        availabilityStatus: status,
                        startDate: dayjs(staffAvailabilityDetails?.startDate),
                        endDate: dayjs(staffAvailabilityDetails?.endDate),
                        selectedDate: dayjs(
                            staffAvailabilityDetails?.startDate
                        ),
                        type: timeSlot?.classType,
                        reason: timeSlot?.reason,
                    });
                })
                .finally(endLoader);
        }
        // else {
        //     setAvailabilityType(slotUpdateInfo?.availabilityStatus);
        // }
    }, [isEdit, availabilityId]);

    const handleClose = () => {
        setWorkingHours(InitialWorkingHours);
        setDaySelected('');
        setDateRange('Single');
        form.resetFields();
        onClose();
    };

    useEffect(() => {
        setSelectedClassType('personalAppointment');
    }, []);

    const handleAvailabilityChange = (value: string) => {
        if (value === 'available')
            dispatch(
                getAllServiceCategories({
                    staffId: selectedStaffId,
                    data: {
                        serviceType: [selectedClassType],
                        page: 1,
                        pageSize: 50,
                    },
                })
            );
        setAvailabilityType(value);
    };

    useEffect(() => {
        if (slotSelectedInfo?.resourceId && !isEdit) {
            const staffId = slotSelectedInfo.resourceId;
            form.setFieldsValue({ staff: staffId });
            setSelectedStaffId(staffId);

            if (staffId) {
                dispatch(GetFacilityListByStaffId({ staffId: staffId }))
                    .unwrap()
                    .then((res: any) => {
                        const firstLocation = res?.data?.data?.[0]?._id;
                        if (firstLocation) {
                            form.setFieldsValue({ facilityId: firstLocation });
                        }
                    });

                dispatch(getAllClassTypeByStaffId({ staffId }))
                    .unwrap()
                    .then((res: any) => {
                        console.log('Res--1---------------', res);
                        const defaultClassType = res?.data?.[0];
                        if (defaultClassType) {
                            form.setFieldsValue({ type: defaultClassType });
                            setSelectedClassType(defaultClassType);

                            dispatch(
                                getAllServiceCategories({
                                    staffId,
                                    data: {
                                        serviceType: [defaultClassType],
                                        page: 1,
                                        pageSize: 50,
                                    },
                                })
                            );
                        }
                    });
            }
            const defaultTimeSlot = {
                from: '10:00',
                to: '11:00',
                payRateIds:
                    store.ServiceCategoryListData?.flatMap(
                        (s: any) => s.payRateId
                    ) || [],
            };

            const dayOfWeek = dayjs().format('ddd').toLowerCase();

            setWorkingHours((prev) => ({
                ...prev,
                [dayOfWeek]: [defaultTimeSlot],
            }));
        }
    }, [slotSelectedInfo?.resourceId, isEdit, isAddUnavailability]);

    useEffect(() => {
        // if(visible){

        if (slotSelectedInfo) {
            const dayOfWeek = dayjs(slotSelectedInfo.startDate)
                .format('ddd')
                .toLowerCase();
            setDaySelected(dayOfWeek);

            const startTime = dayjs(slotSelectedInfo.startDate).format('HH:mm');
            const endTime = dayjs(slotSelectedInfo.endDate).format('HH:mm');

            const timeSlot = {
                from: startTime,
                to: endTime,
                payRateIds: [],
            };

            console.log('Time slot: ', timeSlot, dayOfWeek);

            // Update working hours for the specific day
            setWorkingHours((prev) => ({
                [dayOfWeek]: [timeSlot],
            }));

            form.setFieldsValue({
                selectedDate: dayjs(slotSelectedInfo.startDate),
                dateRange: 'Single',
                staff: slotSelectedInfo.resourceId,
            });
            setSelectedStaffId(slotSelectedInfo.resourceId);
            if (slotSelectedInfo.resourceId) {
                dispatch(
                    GetFacilityListByStaffId({
                        staffId: slotSelectedInfo.resourceId,
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        const firstLocation = res?.data?.data?.[0]?._id;
                        if (firstLocation) {
                            form.setFieldsValue({ facilityId: firstLocation });
                        }
                    });
            }
        }
        // }
    }, [slotSelectedInfo, form]);

    const onFinish: FormProps['onFinish'] = (values) => {
        const scheduleData: any = cleanScheduleAndShowError(
            workingHours,
            availabilityType
        );
        setWorkingHours(scheduleData.cleanedSchedule);
        if (scheduleData.showEmptyError) {
            Alertify.error('There is no availability added.');
            return;
        }
        if (scheduleData.hasErrors) {
            Alertify.error('Please clear all the errors.');
            return;
        }
        let startDate: string | null = null;
        let endDate: string | null = null;

        if (dateRange === DateRangeType.SINGLE) {
            startDate = dayjs(values.selectedDate)
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');

            endDate = dayjs(values.selectedDate)
                .endOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        } else {
            // For range selections, if startDate and endDate are provided
            if (values.startDate) {
                startDate = dayjs(values.startDate)
                    .startOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
            }
            if (values.endDate) {
                endDate = dayjs(values.endDate)
                    .endOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
            }
        }

        startSubmitLoader();

        console.log(
            'isStaffDetails-------------',
            isStaffDetails,
            user?._id,
            staffId
        );

        const payload = {
            userId:
                role === RoleType.TRAINER && isStaffDetails
                    ? user?._id === staffId
                        ? staffId
                        : user?._id
                    : role === RoleType.TRAINER && !isStaffDetails
                    ? values.staff
                    : role === RoleType.TRAINER
                    ? user?._id
                    : isStaffDetails
                    ? staffId
                    : values.staff,
            facilityId: values.facilityId,
            startDate: startDate,
            endDate: endDate,
            dateRange,
            availabilityStatus: availabilityType,
        } as any;

        if (dateRange === DateRangeType.MULTIPLE) {
            payload.markType = repeatOption;
        }

        if (availabilityType !== 'unavailable') {
            payload.classType = 'personalAppointment'; // values.type;
            // payload.markType = repeatOption;
        }

        if (values.reason) {
            payload.reason = values.reason;
        }
        if (values.privacy) {
            payload.privacy = values.privacy;
        }
        payload.schedule = scheduleData.cleanedSchedule;

        console.log('payload--------------', payload);

        if (isEdit) {
            if (!slotUpdateInfo?.isMarkUnavailable) {
                payload.startTime = slotUpdateInfo.from;
                payload.endTime = slotUpdateInfo.to;
            }
            dispatch(
                UpdateStaffAvailability({
                    payload: payload,
                    view: view,
                    date: date,
                    isStaffDetails: isStaffDetails,
                })
            )
                .then((res: any) => {
                    if (
                        res?.payload?.status === 200 ||
                        res?.payload?.status === 201
                    ) {
                        handleClose();
                        setWorkingHours(InitialWorkingHours);
                        form.resetFields();
                    }
                })
                .finally(endSubmitLoader);
        } else {
            dispatch(
                AddStaffAvailability({
                    payload: payload,
                    isStaffDetailsPage: isStaffDetailsPage,
                    view: view,
                    date: date,
                    isStaffDetails: isStaffDetails,
                })
            )
                .then((res: any) => {
                    if (
                        res?.payload?.status === 200 ||
                        res?.payload?.status === 201
                    ) {
                        handleClose();
                    }
                })
                .finally(endSubmitLoader);
        }
    };

    const handleDateChange = (date: any) => {
        if (!date) return;
        const dayOfWeek = date.format('ddd').toLowerCase();
        setDaySelected(dayOfWeek);
    };

    const getDefaultTimeSlots = () => {
        // Default to current time rounded to nearest 30 minutes
        const now = dayjs();
        const minutes = now.minute();
        const roundedMinutes = Math.ceil(minutes / 30) * 30;
        const startTime = now.minute(roundedMinutes).second(0).millisecond(0);
        const endTime = startTime.add(1, 'hour');

        return {
            startTime: startTime.format('HH:mm'),
            endTime: endTime.format('HH:mm'),
        };
    };

    // console.log(
    //     'Visible is ediot slot info isstaff details------------',
    //     visible,
    //     isEdit,
    //     slotSelectedInfo,
    //     isStaffDetails
    // );

    // useEffect(() => {
    //     if (visible && !isEdit && !isStaffDetails) {
    //         console.log('Trigged-------dvdvdvdvdvdvdvdvdvdvdvdvdvdvdvdv');
    //         form.resetFields();
    //         setSelectedStaffId(staffId);
    //         setSelectedClassType('personalAppointment');
    //         setAvailabilityType('available');
    //     }
    // }, [visible]);

    useEffect(() => {
        if (visible && !isEdit && !slotSelectedInfo) {
            const today = dayjs();
            const dayOfWeek = today.format('ddd').toLowerCase();
            const { startTime, endTime } = getDefaultTimeSlots();

            form.setFieldsValue({
                selectedDate: today,
                dateRange: 'Single',
            });

            setDaySelected(dayOfWeek);

            const defaultTimeSlot = {
                from: startTime,
                to: endTime,
                payRateIds: [],
            };

            setWorkingHours((prev) => ({
                ...InitialWorkingHours,
                [dayOfWeek]: [defaultTimeSlot],
            }));

            if (selectedStaffId) {
                dispatch(getAllClassTypeByStaffId({ staffId: selectedStaffId }))
                    .unwrap()
                    .then((res: any) => {
                        const defaultClassType =
                            res?.data?.[0] || 'personalAppointment';
                        setSelectedClassType(defaultClassType);

                        dispatch(
                            getAllServiceCategories({
                                staffId: selectedStaffId,
                                data: {
                                    serviceType: [defaultClassType],
                                    page: 1,
                                    pageSize: 50,
                                },
                            })
                        )
                            .unwrap()
                            .then((res: any) => {
                                console.log(
                                    'Res-------vdvdv---------dvdv-------',
                                    res
                                );
                                const serviceList = res?.data || [];
                                const payRateIds = serviceList.flatMap(
                                    (s: any) => s.payRateId
                                );

                                const defaultTimeSlot = {
                                    from: startTime,
                                    to: endTime,
                                    payRateIds,
                                };

                                setWorkingHours({
                                    ...InitialWorkingHours,
                                    [dayOfWeek]: [defaultTimeSlot],
                                });
                            });
                    });
            }
        }
    }, [visible, isEdit, slotSelectedInfo, selectedStaffId]);

    const updateAvailabilityTimeSlots = (
        date: any,
        updateDayOfWeek: boolean,
        range: string,
        start?: Dayjs,
        end?: Dayjs
    ) => {
        if (updateDayOfWeek && date) handleDateChange(date);
        const values = form.getFieldsValue();

        const startDate =
            start?.toDate() ||
            (range === 'Single'
                ? dayjs(values.selectedDate).startOf('day').toDate()
                : dayjs(values.startDate).startOf('day').toDate());

        const endDate =
            end?.toDate() ||
            (range === 'Single'
                ? dayjs(values.selectedDate).endOf('day').toDate()
                : dayjs(values.endDate).endOf('day').toDate());

        const facilityId =
            slotUpdateInfo?.facilityId || values.facilityId || null;
        const trainerId =
            slotUpdateInfo?.trainerId ||
            selectedStaffId ||
            values.staff ||
            null;
        const classType =
            slotUpdateInfo?.classType ||
            selectedClassType ||
            values.type ||
            'personalAppointment';
        const availabilityStatus =
            slotUpdateInfo?.availabilityStatus ||
            availabilityType ||
            values.availabilityStatus;

        if (!trainerId || !facilityId || !classType) {
            console.warn('Insufficient data to fetch availability details');
            return;
        }

        if (isEdit) {
            dispatch(
                StaffAvailabilityDetails({
                    payload: {
                        facilityId,
                        trainerId,
                        startDate,
                        endDate,
                        from: slotUpdateInfo?.from,
                        dateRange: range,
                        availabilityStatus,
                        classType,
                    },
                })
            ).then((res: any) => {
                const staffAvailabilityDetails = res.payload?.data?.data;
                const { timeSlots } = staffAvailabilityDetails;
                if (!timeSlots) return;
                setWorkingHours(timeSlots);
            });
        }
    };

    const handleDateRange = (value: any) => {
        setDateRange(value);
        setRepeatOption('weekly');
        // form.setFieldsValue({
        //     startDate: dayjs(slotUpdateInfo?.startDate),
        //     endDate: dayjs(slotUpdateInfo?.endDate),
        //     selectedDate: dayjs(slotUpdateInfo?.startDate),
        // });
        // updateAvailabilityTimeSlots(null, false, value);
        if (value === DateRangeType.MULTIPLE) {
            const start = dayjs(slotUpdateInfo?.startDate).startOf('day');
            const end = start.add(6, 'day');

            form.setFieldsValue({
                startDate: start,
                endDate: end,
            });

            updateAvailabilityTimeSlots(null, false, value, start, end);
        } else if (value === DateRangeType.SINGLE) {
            const selected = dayjs(slotUpdateInfo?.startDate);
            form.setFieldsValue({
                selectedDate: selected,
            });

            const start = selected.startOf('day');
            const end = selected.endOf('day');

            updateAvailabilityTimeSlots(null, true, value, start, end);
        }
    };

    // if (loader) {
    //     return (
    //         <div className="flex h-[50vh] w-full items-center justify-center">
    //             <FullLoader state={true} />
    //         </div>
    //     );
    // }

    useEffect(() => {
        if (isStaffDetails && visible) {
            const current = dayjs().startOf('day');
            setDaySelected(current.format('ddd').toLowerCase());
            setDateSelectedTime(current.toISOString());
            form.setFieldsValue({ selectedDate: current });

            updateAvailabilityTimeSlots(
                current,
                true,
                dateRange,
                current,
                current
            );
        }
    }, [isStaffDetails, visible]);

    return (
        <Modal
            title={
                <div className="border-b-2 text-[#1A3353]">
                    {isEdit
                        ? availabilityType === 'unavailable'
                            ? 'Edit Unavailability'
                            : 'Edit Staff Availability'
                        : availabilityType === 'unavailable'
                        ? 'Add Unavailability'
                        : 'Staff Availability'}
                </div>
            }
            open={visible}
            centered
            onCancel={handleClose}
            footer={false}
            // width={800}
            className="w-[80%]"
            // style={{ top: 10 }}
        >
            {loader ? (
                <div className="flex h-[70vh] w-full items-center justify-center">
                    <FullLoader state={true} />
                </div>
            ) : (
                <ConfigProvider
                    theme={{
                        components: {
                            Form: {
                                verticalLabelMargin: -5,
                            },
                        },
                    }}
                >
                    <Form
                        className="  pt-10 lg:px-8"
                        name="schedule-form-2123"
                        layout="horizontal"
                        size="large"
                        form={form}
                        initialValues={{
                            remember: true,
                            availabilityStatus:
                                slotUpdateInfo?.availabilityStatus ||
                                'available',
                            privacy: 'Allow clients to see schedule',
                            type: 'personalAppointment',
                        }}
                        onFinish={onFinish}
                    >
                        <div className="flex w-full flex-col  justify-between gap-4  lg:mb-4  ">
                            {isStaffDetails ? (
                                <div className="lg:w-[100%]  ">
                                    <Form.Item
                                        label="Staff"
                                        name="staff"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select the Staff name',
                                            },
                                        ]}
                                    >
                                        <Input
                                            defaultValue={staffName}
                                            disabled
                                        />
                                    </Form.Item>
                                </div>
                            ) : (
                                <div className="lg:w-[100%]  ">
                                    <Form.Item
                                        label="Staff"
                                        name="staff"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select the Staff name',
                                            },
                                        ]}
                                    >
                                        <InfiniteScrollSelect
                                            fetchOptions={fetchStaffList}
                                            onChange={(value) => {
                                                form.setFieldsValue({
                                                    staff: value,
                                                });
                                                setSelectedStaffId(value);
                                                if (value) {
                                                    dispatch(
                                                        GetFacilityListByStaffId(
                                                            {
                                                                staffId: value,
                                                            }
                                                        )
                                                    )
                                                        .unwrap()
                                                        .then((res: any) => {
                                                            form.setFieldsValue(
                                                                {
                                                                    facilityId:
                                                                        res
                                                                            ?.data
                                                                            ?.data?.[0]
                                                                            ?._id,
                                                                }
                                                            );
                                                        });
                                                    dispatch(
                                                        getAllClassTypeByStaffId(
                                                            {
                                                                staffId: value,
                                                            }
                                                        )
                                                    )
                                                        .unwrap()
                                                        .then((res: any) => {
                                                            console.log(
                                                                'Res--2---------------',
                                                                res
                                                            );
                                                            form.setFieldsValue(
                                                                {
                                                                    type: res
                                                                        ?.data?.[0],
                                                                }
                                                            );
                                                            setSelectedClassType(
                                                                res?.data?.[0]
                                                            );
                                                        });
                                                }
                                            }}
                                            placeholder="Select Staff"
                                        />
                                    </Form.Item>
                                </div>
                            )}

                            <div className="lg:w-[100%] @sm:w-full  ">
                                <Form.Item
                                    label="Location"
                                    name="facilityId"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select location',
                                        },
                                    ]}
                                >
                                    <Select
                                        placeholder="Select location"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={LocationOptionByStaff}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex w-full flex-col justify-between   lg:mb-4">
                            {/* <div className="lg:w-[100%]">
                            <Form.Item
                                label="Types of Availability"
                                        name="availabilityStatus"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select the availability',
                                            },
                                        ]}
                                    >
                                        <Select
                                            style={{
                                                borderBottom:
                                                    '1px solid #e5e7eb',
                                                borderRadius: '0px',
                                            }}
                                            onChange={handleAvailabilityChange}
                                    // defaultValue="available"
                                    disabled={true}
                                >
                                    {isEdit ? (
                                        availabilityType === 'available' ? (
                                            <Option value="available">
                                                Available
                                            </Option>
                                        ) : (
                                            <Option value="unavailable">
                                                Unavailable
                                            </Option>
                                        )
                                    ) : (
                                        <>
                                            <Option value="unavailable">
                                                Unavailable
                                            </Option>
                                            <Option value="available">
                                                Available
                                            </Option>
                                        </>
                                    )}
                                        </Select>
                                    </Form.Item>
                        </div> */}
                            {/* {availabilityType !== 'unavailable' && (
                            <>
                                <div className="lg:w-[100%]">
                                    <Form.Item
                                        label="Available for"
                                        name="type"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select type',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="PA / Classes / Courses"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            onChange={(value) =>
                                                        setSelectedClassType(
                                                            value
                                                        )
                                            }
                                            options={ClassTypeOption}
                                        />
                                    </Form.Item>
                                </div>
                            </>
                        )} */}
                        </div>

                        <div className="mb-8 flex w-full justify-between ">
                            <div className="flex items-center ps-2 lg:w-[100%]">
                                <p className=" text-[13px] font-medium text-[#1A3353] lg:w-[20%]">
                                    Date Range
                                </p>
                                <Radio.Group
                                    value={dateRange}
                                    onChange={(e) =>
                                        handleDateRange(e.target.value)
                                    }
                                >
                                    <Radio
                                        className="text-[#455560]"
                                        value="Single"
                                    >
                                        Single
                                    </Radio>
                                    <Radio
                                        className="text-[#455560]"
                                        value="Multiple"
                                    >
                                        Multiple
                                    </Radio>
                                </Radio.Group>
                            </div>
                        </div>

                        {dateRange === DateRangeType.SINGLE ? (
                            <div className="mb-4">
                                <div className="flex w-full items-center  gap-10 lg:flex-row">
                                    <div className="lg:w-[100%] @sm:w-full">
                                        <Form.Item
                                            label="Select Date"
                                            name="selectedDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please select date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                placeholder="DD/MM/YYYY"
                                                onOpenChange={(status) =>
                                                    setDatePickerOpen(status)
                                                }
                                                format="DD/MM/YYYY"
                                                style={{ width: '100%' }}
                                                open={datePickerOpen}
                                                disabledDate={(currentDate) =>
                                                    currentDate &&
                                                    currentDate.isBefore(
                                                        dayjs().startOf('day')
                                                    )
                                                }
                                                onChange={(date) => {
                                                    setDatePickerOpen(false);
                                                    updateAvailabilityTimeSlots(
                                                        date,
                                                        true,
                                                        dateRange
                                                    );
                                                }}
                                            />
                                        </Form.Item>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <div className="flex w-full flex-col items-center  gap-5">
                                <div className="lg:w-[100%] @sm:w-full">
                                    <Form.Item
                                        label="Start Date"
                                        name="startDate"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please enter start date',
                                            },
                                        ]}
                                    >
                                        <DatePicker
                                            popupClassName="custom-datepicker"
                                            placeholder="DD/MM/YYYY"
                                            format="DD/MM/YYYY"
                                            style={{ width: '100%' }}
                                            disabledDate={(currentDate) =>
                                                currentDate &&
                                                currentDate.isBefore(
                                                    dayjs().startOf('day')
                                                )
                                            }
                                            onChange={(date) =>
                                                updateAvailabilityTimeSlots(
                                                    date,
                                                    true,
                                                    dateRange
                                                )
                                            }
                                        />
                                    </Form.Item>
                                </div>
                                {dateRange === DateRangeType.MULTIPLE && (
                                    <div className="lg:w-[100%] @sm:w-full">
                                        <Form.Item label="Repeat" required>
                                            <Select
                                                placeholder="Select Repeat Type"
                                                value={repeatOption}
                                                onChange={(value) =>
                                                    setRepeatOption(value)
                                                }
                                                options={[
                                                    {
                                                        label: 'Repeat Weekly',
                                                        value: 'weekly',
                                                    },
                                                    {
                                                        label: 'Custom',
                                                        value: 'custom',
                                                    },
                                                ]}
                                            />
                                        </Form.Item>
                                    </div>
                                )}
                                {repeatOption === 'custom' && (
                                    <div className="lg:w-[100%] @sm:w-full">
                                        <Form.Item
                                            label="End Date"
                                            name="endDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter end date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                placeholder="DD/MM/YYYY"
                                                format="DD/MM/YYYY"
                                                style={{ width: '100%' }}
                                                // disabledDate={(currentDate) =>
                                                //     currentDate &&
                                                //     currentDate.isBefore(
                                                //         dayjs().startOf('day')
                                                //     )
                                                // }
                                                onChange={(date) =>
                                                    updateAvailabilityTimeSlots(
                                                        date,
                                                        false,
                                                        dateRange
                                                    )
                                                }
                                                disabledDate={(current) =>
                                                    current &&
                                                    current <
                                                        dayjs(
                                                            form.getFieldValue(
                                                                'startDate'
                                                            )
                                                        ).startOf('day')
                                                }
                                            />
                                        </Form.Item>
                                    </div>
                                )}
                            </div>
                        )}

                        <div className="">
                            {(dateRange === DateRangeType.SINGLE ||
                                dateRange === DateRangeType.MULTIPLE) && (
                                <WeeklySchedule
                                    setWorkingHours={setWorkingHours}
                                    workingHours={workingHours}
                                    dateRange={dateRange}
                                    daySelected={daySelected}
                                    dateSelectedTime={dateSelectedTime}
                                    availabilityType={availabilityType}
                                    isEdit={isEdit}
                                />
                            )}
                        </div>

                        {availabilityType === AvailabilityType.UNAVAILABLE && (
                            <div className="mb-4 mt-4 flex w-full justify-between">
                                <div className="w-full">
                                    <Form.Item
                                        label="Reason"
                                        name="reason"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please enter reason',
                                            },
                                        ]}
                                    >
                                        <TextArea
                                            // value={value}
                                            // onChange={(e) => setValue(e.target.value)}
                                            placeholder="Enter Reason"
                                            autoSize={{
                                                minRows: 3,
                                                maxRows: 5,
                                            }}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        )}

                        <div className="mb-8">
                            <Form.Item
                                label="Privacy"
                                name="privacy"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select privacy',
                                    },
                                ]}
                            >
                                <Radio.Group className="flex flex-col gap-2">
                                    <Radio
                                        className="text-[#455560]"
                                        value="Allow clients to see schedule"
                                    >
                                        Allow client to see Schedule
                                    </Radio>
                                    <Radio
                                        className="text-[#455560]"
                                        value="Mask staff members name"
                                    >
                                        Mask staff member's name
                                    </Radio>
                                    <Radio
                                        className="text-[#455560]"
                                        value="Hide schedule from clients"
                                    >
                                        Hide schedule from clients
                                    </Radio>
                                </Radio.Group>
                            </Form.Item>
                        </div>

                        <div className="flex flex-row justify-end gap-5 @sm:justify-center">
                            <Form.Item>
                                <div
                                    className="mt-10"
                                    style={{ display: 'flex' }}
                                >
                                    <Button
                                        onClick={handleClose}
                                        className="border border-[#1A3353] py-7 text-xl text-[#1A3353] lg:px-20 @sm:px-10"
                                    >
                                        Cancel
                                    </Button>
                                </div>
                            </Form.Item>
                            <Form.Item>
                                <div
                                    className="mt-10"
                                    style={{ display: 'flex', gap: '10px' }}
                                >
                                    <Button
                                        className="bg-purpleLight py-7  text-xl lg:px-20 @sm:px-6"
                                        type="primary"
                                        htmlType="submit"
                                        loading={submitLoader}
                                    >
                                        Save
                                    </Button>
                                </div>
                            </Form.Item>
                        </div>
                    </Form>
                </ConfigProvider>
            )}
        </Modal>
    );
};

export default ScheduleModal;

import React, { useEffect, useMemo, useState } from 'react';
import { Button, Select, Typography } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import {
    GetStaffPermissions,
    UpdateStaffPermissions,
} from '~/redux/actions/staff-action';
import { useLocation } from 'wouter';
import Alertify from '~/services/alertify';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { useAppSelector } from '~/hooks/redux-hooks';

const { Text, Title } = Typography;

const StaffPermissions = () => {
    const [location] = useLocation();
    const dispatch = useDispatch();
    const [permissionsData, setPermissionsData] = useState([]);
    const [isEditing, setIsEditing] = useState(false);
    const [selectedValues, setSelectedValues] = useState({});
    const [userId, setUserId] = useState<string>('');
    const fetchPermissions = () => {
        const path = location.split('/');
        const userId = String(path[path.length - 1]);
        setUserId(userId);
        const payload = { userId };
        dispatch(GetStaffPermissions(payload))
            .then((res: any) => {
                const rawData = res?.payload?.data?.data || [];
                const groupedPermissions: Record<string, any> = {};

                rawData.forEach((item: any) => {
                    const category = item.module;

                    if (!groupedPermissions[category]) {
                        groupedPermissions[category] = {
                            category,
                            permissions: [],
                        };
                    }

                    item.subjects?.forEach((subject: any) => {
                        const defaultOptions = [
                            { label: 'RESTRICTED', value: 'RESTRICTED' },
                        ];

                        const userActions =
                            subject.actions?.map((action: any) => ({
                                label: action.action.toUpperCase(),
                                value: action._id,
                                permitted: action.permitted,
                            })) || [];

                        // Combine RESTRICTED with API response actions
                        const actionOptions = [
                            ...defaultOptions,
                            ...userActions,
                        ];

                        let defaultValue = 'RESTRICTED';
                        const permittedActions = userActions.filter(
                            (action) => action.permitted
                        );
                        if (permittedActions.length > 0) {
                            defaultValue = permittedActions[0].value;
                        }

                        const key = `${category}-${subject.subject}`;
                        setSelectedValues((prev) => ({
                            ...prev,
                            [key]: defaultValue,
                        }));

                        groupedPermissions[category].permissions.push({
                            name: subject.subject,
                            actions: actionOptions,
                            defaultValue,
                        });
                    });
                });

                const transformedData = Object.values(groupedPermissions);
                setPermissionsData(transformedData);
            })
            .catch((error: any) => {
                Alertify.error(error);
            });
    };
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    useEffect(() => {
        fetchPermissions();
    }, [dispatch, location]);

    const handleSave = () => {
        const assign = [];
        const remove = [];

        permissionsData.forEach((section) => {
            section.permissions.forEach((perm) => {
                const key = `${section.category}-${perm.name}`;
                const selectedValue = selectedValues[key];
                const selectedAction = perm.actions.find(
                    (action) => action.value === selectedValue
                );

                if (selectedAction) {
                    if (selectedAction.label === 'RESTRICTED') {
                        remove.push({
                            module: section.category,
                            subject: perm.name,
                        });
                    } else {
                        assign.push({
                            module: section.category,
                            subject: perm.name,
                            actionId: selectedValue,
                        });
                    }
                }
            });
        });

        // console.log('Values for save are', { assign, remove });
        const payload = { userId, assign, remove };
        // console.log('Payload', payload);
        dispatch(UpdateStaffPermissions(payload))
            .then((res: any) => {
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    Alertify.success('Permissions updated successfully');
                    fetchPermissions();
                }
            })
            .catch((error: any) => {
                Alertify.error(error);
            });
        setIsEditing(false);
    };

    const handleSelectChange = (
        value: string,
        section: string,
        permName: string
    ) => {
        const key = `${section}-${permName}`;
        setSelectedValues((prev) => ({
            ...prev,
            [key]: value,
        }));
    };
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasPermissionsAssignPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.AUTHENTICATION_PERMISSIONS &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.POLICY_ASSIGN
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    return (
        <div className="mx-auto w-full rounded-xl p-6">
            <div className="flex items-center justify-between">
                <Title level={4} className="underline">
                    Additional Permissions
                </Title>
                {(hasPermissionsAssignPermission ||
                    store.role === RoleType.ORGANIZATION) &&
                    !isEditing && (
                        <div
                            className="mb-2 flex justify-end"
                            onClick={() => setIsEditing(true)}
                        >
                            <img
                                src="/icons/common/edit.svg"
                                alt="edit"
                                className="ms-auto h-[20px] cursor-pointer"
                            />
                        </div>
                    )}
            </div>
            <div className="mt-4 p-8">
                {permissionsData.map((section, index) => (
                    <div key={index}>
                        <Text strong>{section.category}</Text>
                        {section.permissions.map((perm, i) => (
                            <div
                                key={i}
                                className="my-4 flex items-center justify-between rounded-md px-4 py-2 shadow-sm"
                            >
                                <Text>{perm.name}</Text>
                                <Select
                                    options={perm.actions}
                                    value={
                                        selectedValues[
                                            `${section.category}-${perm.name}`
                                        ]
                                    }
                                    onChange={(value) =>
                                        handleSelectChange(
                                            value,
                                            section.category,
                                            perm.name
                                        )
                                    }
                                    disabled={!isEditing}
                                    className="w-[150px]"
                                />
                            </div>
                        ))}
                    </div>
                ))}
            </div>
            {isEditing && (
                <div className="flex flex-row justify-end gap-5 @sm:justify-center">
                    <div className="mt-10" style={{ display: 'flex' }}>
                        <Button
                            className="border-1 border-[#1A3353] py-7 text-xl text-[#1A3353] lg:px-20 @sm:px-10"
                            onClick={() => setIsEditing(false)}
                        >
                            Cancel
                        </Button>
                    </div>
                    <div
                        className="mt-10"
                        style={{ display: 'flex', gap: '10px' }}
                    >
                        <Button
                            className="bg-purpleLight py-7  text-xl lg:px-20 @sm:px-6"
                            type="primary"
                            onClick={handleSave}
                        >
                            Save
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default StaffPermissions;

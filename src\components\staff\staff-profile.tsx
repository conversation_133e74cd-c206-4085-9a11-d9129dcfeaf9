import { ConfigProvider, Tabs, TabsProps } from 'antd';
import PersonalInfo from './personal-info';
import Skills from './skills';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { UpdateStaffProfile } from '~/redux/actions/staff-action';
import { useParams } from 'wouter';
import { summarizeNestedObject } from '~/redux/actions/settings-actions';
import StaffPermissions from './staff-permissions';
import { useMemo } from 'react';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { useSelector } from 'react-redux';

const StaffPersonalInfo = () => {
    const { id } = useParams();
    const dispatch = useAppDispatch();
    function savePersonalInfo(fields: any) {
        return dispatch(UpdateStaffProfile({ fields, id })).then((res) => {
            if (
                res?.payload?.res?.status === 200 ||
                res?.payload?.res?.status === 201
            ) {
                return res;
            }
        });
    }

    return <PersonalInfo role="staff" onSave={savePersonalInfo} />;
};

const StaffSkillsInfo = () => {
    const { id } = useParams();
    const dispatch = useAppDispatch();
    function saveSkillsInfo(fields) {
        dispatch(UpdateStaffProfile({ fields, id }));
    }

    return <Skills onSave={saveSkillsInfo} role="staff" />;
};

const StaffProfile = () => {
    const onChange = (key: string) => {
        // console.log(key);
    };

    const store = useAppSelector((state) => ({
        staffOnboarding: state.settings_store.staffOnboarding,
        role: state.auth_store.role,
    }));
    const ShowTabvalue = summarizeNestedObject(store.staffOnboarding);

    // console.log('staff onboading-----------', ShowTabvalue)
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasPermissionsReadPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.AUTHENTICATION_PERMISSIONS &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type === PERMISSIONS_ENUM.POLICY_READ
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const items: TabsProps['items'] = [
        {
            key: '1',
            label: (
                <span className="px-8 font-semibold">Personal Information</span>
            ),
            children: <StaffPersonalInfo />,
        },
        ...(ShowTabvalue?.skillsExperience
            ? [
                  {
                      key: '2',
                      label: (
                          <span className="px-8 font-semibold">
                              Skills and Experiences
                          </span>
                      ),
                      children: <StaffSkillsInfo />,
                  },
              ]
            : []),
        ...(hasPermissionsReadPermission || store.role === RoleType.ORGANIZATION
            ? [
                  {
                      key: '3',
                      label: (
                          <span className="px-8 font-semibold">
                              Permissions
                          </span>
                      ),
                      children: <StaffPermissions />,
                  },
              ]
            : []),
    ];

    return (
        <div className="lg:w-[95%]">
            <ConfigProvider
                theme={{
                    components: {},
                }}
            >
                <Tabs
                    defaultActiveKey="1"
                    items={items}
                    onChange={onChange}
                    tabPosition="top"
                    tabBarStyle={{}}
                />
            </ConfigProvider>
        </div>
    );
};

export default StaffProfile;

import {
    Button,
    Form,
    FormProps,
    Input,
    Modal,
    Select,
    Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    createPayRate,
    getAllPayRates,
    getPayRateDetails,
    updatePayRate,
} from '~/redux/actions/pay-rate-actions';
import {
    activeServiceCategoyListv1,
    ServiceCategoryList,
} from '~/redux/actions/serviceCategoryAction';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';

const { Title } = Typography;
const { Option, OptGroup } = Select;

interface AddPayRateModalProps {
    visible: boolean | undefined;
    id?: string | null;
    onClose: () => void;
    staffId: string | undefined;
}

const transformServiceData = (apiData: any) => {
    return apiData.map((service: any) => ({
        category: service.name,
        options: service.appointmentType.map((appointment: any) => ({
            label: `${appointment.name} - ${appointment.durationInMinutes} mins`,
            value: JSON.stringify({
                serviceId: service._id,
                appointmentId: appointment._id,
                duration: appointment.durationInMinutes,
            }),
        })),
    }));
};

const AddPayRate: React.FC<AddPayRateModalProps> = ({
    visible,
    onClose,
    id,
    staffId,
}) => {
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    const [appointmentTypeOption, setAppointmentTypeOption] = useState<any>([]);
    const [payRate, setPayRate] = React.useState('');
    const [isCourseEnabled, setIsCourseEnabled] = useState(false);
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        payRateDetails: state.pay_rate_store.payRateDetails,
        ActiveServiceCategoryListV1:
            state.service_category_store.ActiveServiceCategoryListV1,
    }));

    const serviceOptions = transformServiceData(
        store.ActiveServiceCategoryListV1
    );

    // Fetch pay rate details and populate form
    useEffect(() => {
        if (!id || !visible) return;

        const fetchPayRateDetails = async () => {
            try {
                const res = await dispatch(getPayRateDetails(id));
                const data = res.payload?.data;
                if (data) {
                    const selectedServiceValue = serviceOptions
                        .flatMap((group: any) => group.options)
                        .find((option: any) => {
                            const parsed = JSON.parse(option.value);
                            return (
                                parsed.serviceId === data.serviceCategory &&
                                parsed.appointmentId === data.appointmentType
                            );
                        })?.value;
                    form.setFieldsValue({
                        service: selectedServiceValue || null,
                        serviceType: data.serviceType,
                        payRate: data.payRate,
                        userId: staffId,
                    });

                    dispatch(
                        activeServiceCategoyListv1({
                            classType: data.serviceType,
                        })
                    );
                } else {
                    console.error('No data found in response', res);
                }
            } catch (error) {
                console.error('Error fetching pay rate details:', error);
            }
        };

        fetchPayRateDetails();
    }, [id, visible, dispatch, form, staffId]);

    // Reset form when modal closes
    useEffect(() => {
        if (!visible) {
            form.resetFields();
        }
    }, [visible, form]);

    useEffect(() => {
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_setup_courses',
            })
        ).then((response: any) => {
            setIsCourseEnabled(
                response?.payload?.data?.data?.isEnabled || false
            );
        });
    }, []);

    const onFinish: FormProps['onFinish'] = (values: any) => {
        startLoader();
        const selectedService = JSON.parse(values.service);
        const payload = {
            serviceCategory: selectedService.serviceId,
            appointmentType: selectedService.appointmentId,
            serviceType: values.serviceType,
            payRate: 'No-pay',
            userId: id ? undefined : staffId,
        };
        // console.log('payload--------------', payload);

        const afterSubmit = (res: any) => {
            const status = res?.payload?.status ?? res?.status;
            if (status === 200 || status === 201) {
                dispatch(
                    getAllPayRates({
                        page: 1,
                        pageSize: 10,
                        staffId,
                    })
                );
                onClose();
            }
        };

        const action = id
            ? updatePayRate({ id, payload })
            : createPayRate(payload);

        dispatch(action).unwrap().then(afterSubmit).finally(endLoader);
    };

    const handleServiceTypeChange = (serviceType: string) => {
        form.setFieldsValue({
            serviceCategory: null,
            appointmentType: null,
            service: null,
        });
        dispatch(
            activeServiceCategoyListv1({
                classType: serviceType,
            })
        );
    };

    return (
        <Modal
            title={
                <Title className="text-[#1A3353]" level={4}>
                    {id ? 'Edit' : 'Add'} Specialization
                </Title>
            }
            open={visible}
            centered
            width={1000}
            onCancel={onClose}
            footer={false}
            style={{ top: '20px' }}
        >
            <Form
                name="payRateForm"
                layout="vertical"
                size="large"
                form={form}
                autoComplete="off"
                onFinish={onFinish}
            >
                <div className="flex gap-8">
                    <Form.Item
                        className="w-1/2"
                        label="Service Type"
                        name="serviceType"
                        rules={[
                            {
                                required: true,
                                message: 'Please select Service Type',
                            },
                        ]}
                    >
                        <Select
                            placeholder="Select Service Type"
                            showSearch
                            filterOption={(input, option) =>
                                (option?.label ?? '')
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                            }
                            options={[
                                {
                                    label: 'Personal Appointment',
                                    value: 'personalAppointment',
                                },
                                {
                                    label: 'Classes',
                                    value: 'classes',
                                },
                                ...(isCourseEnabled
                                    ? [
                                          {
                                              label: 'Courses',
                                              value: 'courses',
                                          },
                                      ]
                                    : []),
                            ]}
                            onChange={handleServiceTypeChange}
                        />
                    </Form.Item>

                    <Form.Item
                        className="w-1/2"
                        label="Service"
                        name="service"
                        rules={[
                            {
                                required: true,
                                message: 'Please select a service',
                            },
                        ]}
                    >
                        <Select
                            placeholder="Select Service"
                            showSearch
                            filterOption={(input, option) =>
                                String(option?.label ?? '')
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                            }
                        >
                            {serviceOptions.map((group: any) => (
                                <OptGroup
                                    key={group.category}
                                    label={group.category}
                                >
                                    {group.options.map((service: any) => (
                                        <Option
                                            key={service.value}
                                            value={service.value}
                                        >
                                            {service.label}
                                        </Option>
                                    ))}
                                </OptGroup>
                            ))}
                        </Select>
                    </Form.Item>
                </div>

                <div className="ms-auto mt-8 text-right">
                    <Button
                        onClick={onClose}
                        htmlType="button"
                        className="Paragraph-white me-5 ms-auto h-16 border-[#1A3353] text-xl text-[#1A3353]"
                    >
                        Cancel
                    </Button>
                    <Button
                        loading={loader}
                        htmlType="submit"
                        className="Paragraph-white h-16 bg-purpleLight px-10 text-xl text-white"
                    >
                        Save
                    </Button>
                </div>
            </Form>
        </Modal>
    );
};

export default AddPayRate;

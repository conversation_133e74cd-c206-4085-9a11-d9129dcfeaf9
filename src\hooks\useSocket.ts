import React, { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { ProjectUrl, WebSocketurl } from '~/env';
import { AnyAction, Dispatch } from '@reduxjs/toolkit';
import { useAppSelector } from './redux-hooks';

const useSocket = (
    dispatch?: Dispatch<AnyAction>,
    id?: string 
): React.MutableRefObject<Socket | null> => {
    const socketRef = useRef<Socket | null>(null);

    const store = useAppSelector((state)=>({
        role : state.auth_store.role
    }))

    useEffect(() => {
        const token = window.localStorage.getItem('token');
        socketRef.current = io(WebSocketurl, {
            autoConnect: true,
            // auth: { authorization: token },
            // extraHeaders: {
            //     'ngrok-skip-browser-warning': '69420',
            // },
            transports: ["websocket"],
            query: {
                facilityId: id,
            },
            reconnection: true, 
            reconnectionAttempts: 10,
            reconnectionDelay: 1000,
            reconnectionDelayMax: 5000, 
            randomizationFactor: 0.5,
        });

        socketRef.current.connect();

        // Connection Events
        socketRef.current.on('connect', () => {
            console.log('Socket connected');
        });

        socketRef.current.on('disconnect', () => {
            console.log('Socket disconnected');
        });

        socketRef.current.on('notification-connection-success', (message:any) => {
            console.log('notification-connection-success', message);
        });

        socketRef.current.on('invalid_facility', (message) => {
            console.log('user is invalid_facility', message);
        });

        socketRef.current.on('error', (error) => {
            console.log('Error', error);
        });

        socketRef.current.on('facility_not_found', (message:any) => {
            console.log('facility_not_found', message);
        });

        // Cleanup
        return () => {
            if (socketRef.current) {
                socketRef.current.off('connect');
                socketRef.current.off('disconnect');
                socketRef.current.disconnect();
            }
        };
    }, [dispatch, store]);

    return socketRef;
};

export default useSocket;
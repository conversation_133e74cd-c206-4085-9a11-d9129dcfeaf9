import store, { persistor } from '~/redux/store';
import 'react-quill/dist/quill.snow.css';
import 'react-big-calendar/lib/css/react-big-calendar.css';

import ReactDOM from 'react-dom/client';
import { Toaster } from 'react-hot-toast';
import { Provider } from 'react-redux';
import App from '~/App.tsx';
import '~/styles/globals.css';
import { ConfigProvider } from 'antd';
import { PersistGate } from 'redux-persist/integration/react';

const Root = () => {
    // function resetLocalState() {
    //     store.dispatch(ResetLocalState());
    // }
    return (
        <>
            <ConfigProvider
                theme={{
                    token: {
                        // colorPrimary: '#8143d1',
                        // colorPrimaryHover: 'rgba(129, 67, 209, 0.3)',
                        colorPrimaryHover: '',
                        colorPrimaryActive: '',
                    },
                    components: {
                        Menu: {
                            itemHoverBg: 'transparent',
                            itemSelectedBg: 'rgba(129, 67, 209, 1)',
                            horizontalItemSelectedBg: 'rgba(129, 67, 209, 1)',
                            subMenuItemBg: '#a77bdf',
                            itemSelectedColor: '#fff',
                        },
                        Button: {
                            // defaultBg: 'rgba(167, 123, 223, 1)',
                        },
                        Tabs: {
                            inkBarColor: '#8143D1',
                            itemActiveColor: '#1A3353',
                            itemSelectedColor: '#1A3353',
                            itemColor: '#1A3353',
                            itemHoverColor: '#1A3353',
                            colorPrimaryActive: '#000',
                        },
                        Radio: {
                            // change following colors as desired
                            colorPrimary: '#8143D1',
                        },
                        Progress: {
                            defaultColor: '#8143D1',
                        },
                        Modal: {
                            titleColor: '#1A3353',
                        },
                        Table: {
                            borderColor: '#0000001A',
                            cellFontSize: 13,
                            headerBg: '#fff',
                            headerColor: '#1A3353',
                            colorText: '#455560',
                            cellPaddingBlock: 10,
                            cellPaddingInline: 10,
                        },
                        Calendar: {
                            itemActiveBg: '#8143D1',
                        },
                        DatePicker: {
                            cellActiveWithRangeBg: 'rgba(129, 67, 209, 0.2)',
                            cellRangeBorderColor: '#8143D1',
                        },
                    },
                }}
            >
                <Provider store={store}>
                    <PersistGate
                        loading={
                            <div className="flex h-screen flex-row items-center justify-center">
                                <p>Loading...</p>
                            </div>
                        }
                        persistor={persistor}
                    >
                        <App />
                        <Toaster />
                    </PersistGate>
                </Provider>
            </ConfigProvider>
        </>
    );
};

ReactDOM.createRoot(document.getElementById('root')!).render(Root());

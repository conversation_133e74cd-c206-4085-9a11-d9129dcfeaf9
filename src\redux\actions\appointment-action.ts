import { createAsyncThunk } from '@reduxjs/toolkit';
import dayjs from 'dayjs';
import {
    BOOKED_APPOINTMENT_LIST,
    CREATE_BOOK_APPOINTMENT,
    REGISTER_STAFF,
    STAFF_AVAILABILITY_DELETE,
    STAFF_AVAILABILITY_DETAILS,
    STAFF_AVAILABILITY_LIST,
    STAFF_TRAINER_LIST,
    BOOKED_APPOINTMENT_DETAILS,
    UPDATE_BOOKED_APPOINTMENT,
    DELETE_BOOKED_APPOINTMENT,
    PRICE_LIST_BY_USERID,
    PRICE_LIST_BY_USER_AND_TYPE,
    CANCEL_SCHEDULEING_APPOINTMENT,
    STAFF_TRAINER_FOR_PAY_RATE_LIST,
    PRICING_LIST_BY_USERANDSUBTYPE,
    STAFF_TRAINER_LIST_V1,
    SERVICE_BY_PRICING_V1,
    PRICE_LIST_FOR_SHARE_PASS,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    deleteApi,
    getApi,
    handleApiError,
    patchApi,
    postApi,
} from '~/services/api-services';
import { RoleType } from '~/types/enums';

/*-------------- Staff Available Listing -------------- */

export const StaffAvailabilityList: any = createAsyncThunk(
    'staffs/StaffAvailabilityList',
    async ({ reqData }: any, { rejectWithValue, dispatch }: any) => {
        try {
            const response = await postApi(STAFF_AVAILABILITY_LIST, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- trainer Listing -------------- */

export const TrainerListingForPayRate: any = createAsyncThunk(
    'staffs/TrainerListingForPayRate',
    async (
        { serviceCategory, serviceType, appointmentType }: any,
        { getState }: any
    ) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(STAFF_TRAINER_FOR_PAY_RATE_LIST, {
                organizationId,
                serviceCategory,
                serviceType,
                appointmentType,
            });
            return response;
        } catch (error: any) {
            // return handleApiError(error, rejectWithValue);
            console.error(error);
        }
    }
);

export const TrainerListing: any = createAsyncThunk(
    'staffs/TrainerListing',
    async ({ facilityId, isActive }: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(STAFF_TRAINER_LIST, {
                facilityId,
                isActive,
            });
            return response;
        } catch (error: any) {
            // return handleApiError(error, rejectWithValue);
            console.error(error);
        }
    }
);

/*-------------- trainer Listing -------------- */

export const PricingListing: any = createAsyncThunk(
    'staffs/PricingListing',
    async ({ clientId }: any) => {
        try {
            const response = await getApi(
                `${PRICE_LIST_BY_USERID}/${clientId}`
            );
            return response;
        } catch (error: any) {
            console.error(error);
        }
    }
);

export const PricingListingByUserAndType: any = createAsyncThunk(
    'PricingListingByUserAndType',
    async (reqData: any) => {
        try {
            const response = await postApi(
                PRICE_LIST_BY_USER_AND_TYPE,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error', error);
        }
    }
);

export const PricingListingForSharePass: any = createAsyncThunk(
    'PricingListingForSharePass',
    async (reqData: any) => {
        try {
            const response = await postApi(PRICE_LIST_FOR_SHARE_PASS, reqData);
            return response;
        } catch (error: any) {
            console.log('Error', error);
        }
    }
);

/*-------------- Staff availability details -------------- */

export const StaffAvailabilityDetails: any = createAsyncThunk(
    'staffs/StaffAvailabilityDetails',
    async ({ payload }: any, { rejectWithValue, dispatch }: any) => {
        try {
            const response = await postApi(STAFF_AVAILABILITY_DETAILS, {
                ...payload,
            });
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Staff availability Delete -------------- */

export const StaffAvailabilityDelete: any = createAsyncThunk(
    'staffs/StaffAvailabilityDelete',
    async (
        { payload, view, date, isStaffDetails }: any,
        { rejectWithValue, dispatch, getState }: any
    ) => {
        const { role, user } = getState().auth_store;
        const { facilityId } = getState().appointment_store;
        let startDate, endDate;

        if (view === 'day') {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        } else if (view === 'week') {
            startDate = dayjs(date).startOf('week').toDate();
            endDate = dayjs(date).endOf('week').toDate();
        } else {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        }

        const data = {
            startDate: startDate,
            endDate: endDate,
        } as any;

        if (role === RoleType.TRAINER && isStaffDetails) {
            data.userIds = [user?._id];
        }
        if (role === RoleType.TRAINER) {
            data.facilityIds = [facilityId];
        }

        try {
            const response = await postApi(STAFF_AVAILABILITY_DELETE, {
                ...payload,
            });
            dispatch(StaffAvailabilityList({ reqData: data }));
            Alertify.success(
                `Staff ${
                    payload?.deleteSlot ? 'slot' : 'day'
                } availability deleted successfully`
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/* --------------- Add book appointment ----------------- */

export const CreateBookAppointment: any = createAsyncThunk(
    'staffs/CreateBookAppointment',
    async (
        { payload, view, date }: any,
        { rejectWithValue, getState }: any
    ) => {
        const { role, user } = getState().auth_store;
        const { facilityId } = getState().appointment_store;
        let startDate, endDate;

        if (view === 'day') {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        } else if (view === 'week') {
            startDate = dayjs(date).startOf('week').toDate();
            endDate = dayjs(date).endOf('week').toDate();
        } else {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        }

        const data = {
            startDate: startDate,
            endDate: endDate,
        } as any;

        if (role === RoleType.TRAINER) {
            data.userIds = [user?._id];
            data.facilityIds = [facilityId];
        }

        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(CREATE_BOOK_APPOINTMENT, {
                ...payload,
                organizationId,
            });
            Alertify.success('Appointment booked successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Appointment Listing -------------- */

export const BookedAppointmentList: any = createAsyncThunk(
    'staffs/BookedAppointmentList',
    async ({ reqData }: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(BOOKED_APPOINTMENT_LIST, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            // return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Appointment Details -------------- */

export const BookedAppointmentDetails: any = createAsyncThunk(
    'staffs/BookedAppointmentDetails',
    async ({ id }: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(
                `${BOOKED_APPOINTMENT_DETAILS}/${id}`
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Appointment Listing -------------- */

export const DeleteBookedAppointment: any = createAsyncThunk(
    'staffs/DeleteBookedAppointment',
    async ({ id }: any, { rejectWithValue }: any) => {
        try {
            const response = await deleteApi(
                `${DELETE_BOOKED_APPOINTMENT}/${id}`
            );
            Alertify.success('Appointment deleted successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Appointment Listing -------------- */

export const CanceledBookedAppointment: any = createAsyncThunk(
    'staffs/CanceledBookedAppointment',
    async ({ id }: any, { rejectWithValue }: any) => {
        try {
            const response = await patchApi(
                `${CANCEL_SCHEDULEING_APPOINTMENT}/${id}`
            );
            Alertify.success('Appointment deleted successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Appointment Listing -------------- */

export const UpdateBookedAppointment: any = createAsyncThunk(
    'staffs/UpdateBookedAppointment',
    async ({ payload }: any, { rejectWithValue, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const data = JSON.parse(JSON.stringify(payload));
            delete data.id;
            const response = await postApi(
                `${UPDATE_BOOKED_APPOINTMENT}/${payload.id}`,
                {
                    ...data,
                    organizationId,
                }
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export function ManipulateBookAppointmentData(item: any) {
    const date = new Date(item.date);
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    const [fromHour, fromMinute] = item.from.split(':').map(Number);
    const [toHour, toMinute] = item.to.split(':').map(Number);
    return {
        id: item._id,
        title: item.title,
        name: item.clientName,
        start: new Date(year, month, day, fromHour, fromMinute),
        end: new Date(year, month, day, toHour, toMinute),
        instructor: item.trainerName,
        resourceId: item.trainerId,
        facilityId: item.facilityId,
        paymentStatus: item.paymentStatus,
        planType: item.planType,
        tier: item.tier,
        class_type: item.title,
        capacity: item.capacity,
    };
}

export const PricingListingByUserAndSubType: any = createAsyncThunk(
    'PricingListingByUserAndsubType',
    async (reqData: any) => {
        try {
            const response = await postApi(
                PRICING_LIST_BY_USERANDSUBTYPE,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error', error);
        }
    }
);
export const StaffAvailabilityListV1: any = createAsyncThunk(
    'staffs/StaffAvailabilityList',
    async ({ reqData }: any, { rejectWithValue, dispatch }: any) => {
        try {
            const response = await postApi(STAFF_TRAINER_LIST_V1, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);
export const serviceBypricingIdV1: any = createAsyncThunk(
    'staffs/StaffAvailabilityList',
    async ({ pricingId }: any, { rejectWithValue, dispatch }: any) => {
        try {
            const response = await postApi(SERVICE_BY_PRICING_V1, {
                pricingId,
            });
            console.log(response, 'kgjkwehrgkh');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

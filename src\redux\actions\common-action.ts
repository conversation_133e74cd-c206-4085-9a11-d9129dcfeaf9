import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CITY_LIST,
    CITY_LIST_BY_ID,
    COUNTRY_LIST,
    GET_SETTINGS,
    SAVE_SETTINGS,
    UPLOAD_SINGLE_IMAGE,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    postApi,
    handleApiError,
    getApi,
    patchApi,
} from '~/services/api-services';

/*----------------Customer List ---------------- */

interface StateListParams {
    page: number;
    pageSize: number;
}

interface CityListParams {
    page: number;
    pageSize: number;
    stateId: string;
    cityId: string;
    search: string;
}

export const CountryList: any = createAsyncThunk(
    'country-list',
    async ({ page = 1, pageSize = 50 }: StateListParams, { getState }) => {
        try {
            const response = await postApi(COUNTRY_LIST, {
                page,
                pageSize,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const CityList: any = createAsyncThunk(
    'city-list',
    async (
        { page = 1, pageSize = 50, stateId, search, cityId }: CityListParams,
        { getState }
    ) => {
        try {
            const response = await postApi(`${CITY_LIST}`, {
                page,
                pageSize,
                search,
                stateId,
                cityId,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const BillingCityList: any = createAsyncThunk(
    'billing-city-list',
    async (
        { page = 1, pageSize = 50, stateId, search, cityId }: CityListParams,
        { getState }
    ) => {
        try {
            const response = await postApi(`${CITY_LIST}`, {
                page,
                pageSize,
                search,
                stateId,
                cityId,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const UploadImage = createAsyncThunk(
    'common/UploadSingleImage',
    async ({ file }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const formData = new FormData();
            formData.append('image', file);
            const res = await postApi(UPLOAD_SINGLE_IMAGE, formData, {
                'Content-Type': 'multipart/form-data',
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*------------------- City Name ------------------ */

export const CityById: any = createAsyncThunk(
    'CityById',
    async ({ cityId }: any) => {
        try {
            const response = await getApi(`${CITY_LIST_BY_ID}/${cityId}/name`);
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

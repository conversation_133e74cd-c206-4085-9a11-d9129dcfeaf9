import { createAsyncThunk } from '@reduxjs/toolkit';
import { CLIENT_LEAD_DETAILS, CLIENT_LEAD_LIST, ADD_CLIENT_MINOR, CLIENT_MINOR_LIST } from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { getApi, postApi } from '~/services/api-services';
interface CustomerLeadListParams {
    page: number;
    pageSize: number;
    search: string;
    facilityId: string[];
}
interface AddClientMinorPayload {
    clientId: string;
    minor: {
        firstName: string;
        lastName: string;
        dob: string;
        relation?: string;
        gender?: string;
        notes?: string;
    }[];
    wavierSourceId?: any;
}

export const CustomerLeadList: any = createAsyncThunk(
    'customer-list',
    async (
        {
            page,
            pageSize,
            search,
            facilityId
        }: CustomerLeadListParams,

    ) => {
        try {
            const response = await postApi(CLIENT_LEAD_LIST, {
                page,
                pageSize,
                search,
                facilityId

            });
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

export const customerLeadDetail = createAsyncThunk(
    'customer-lead-detail',
    async (id: string) => {
        try {
            const response = await getApi(`${CLIENT_LEAD_DETAILS}/${id}`)
            return response;
        } catch (error) {
            console.error('Error fetching client lead detail API', error)
        }
    }
)
export const addClientMinor = createAsyncThunk(
    'add-client-minor',
    async (data: AddClientMinorPayload) => {
        try {
            Alertify.success("Minor added to client successfully");
            return await postApi(ADD_CLIENT_MINOR, data);
        } catch (error) {
            Alertify.error("Failed to add minor. Please try again.");
            return Promise.reject(error);
        }
    }
);
export const customerMinorList = createAsyncThunk(
    'customer-Minor-list',
    async (id: string) => {
        try {
            const response = await getApi(`${CLIENT_MINOR_LIST}/${id}`)
            return response;
        } catch (error) {
            console.error('Error fetching client lead detail API', error)
        }
    }
)

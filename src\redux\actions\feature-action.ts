import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CREATE_FEATURE,
    FEATRUE_LISTING,
    FEATURE_DETAIL,
    UPDATE_FEATURE,
    UPDATE_FEATURE_STATUS,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';
interface featureListParams {
    page: number;
    pageSize: number;
    search?: string;
}
export const createFeature: any = createAsyncThunk(
    'createFeature',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_FEATURE, reqData);
            console.log(response);
            Alertify.success('Feature Created Successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const featureListing: any = createAsyncThunk(
    'Feature-list',
    async ({ isActive, page, pageSize, search }: any) => {
        try {
            const response = await postApi(FEATRUE_LISTING, {
                page,
                pageSize,
                search,
                isActive,
            });
            console.log(response);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const getFeatureById: any = createAsyncThunk(
    'Feature-Detail',
    async (featureId: any, { rejectWithValue }) => {
        try {
            const response = await getApi(
                `${FEATURE_DETAIL}/${featureId.featureId}`
            );
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch Feature detail'
            );
        }
    }
);
export const updateFeatureDetail = createAsyncThunk(
    'updateFeature',
    async ({ reqData, featureId }: any, { dispatch, rejectWithValue }) => {
        try {
            console.log(featureId);
            const response = await patchApi(`${UPDATE_FEATURE}/${featureId}`, {
                ...reqData,
            });
            Alertify.success('Feature updated successfully');
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response.data || 'Failed To update the Feature'
            );
        }
    }
);
export const deleteFeature = createAsyncThunk(
    'Delete Feature',
    async (id: string, { dispatch, rejectWithValue }) => {
        try {
            await deleteApi(`${FEATURE_DETAIL}/${id}`);
            Alertify.success('Feature deleted successfully');
            dispatch(featureListing({}));
            return id;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to delete Feature'
            );
        }
    }
);
export const updateFeatureStatus = createAsyncThunk(
    'Update status',
    async ({ isActive, featureId }: any, { dispatch, rejectWithValue }) => {
        try {
            const response = await patchApi(
                `${UPDATE_FEATURE_STATUS}/${featureId}`,
                {
                    isActive,
                }
            );
            dispatch(featureListing({}));
            Alertify.success('Feature  Status updated successfully');
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data ||
                    'Failed to update the status of the Feature'
            );
        }
    }
);

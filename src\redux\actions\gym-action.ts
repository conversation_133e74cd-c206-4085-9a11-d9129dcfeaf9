import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ADMIN_GYM_LIST,
    CREATE_GYM_ORBOARDING,
    GYM_DETAILS,
    UPDATE_GYM_ORBOARDING,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { getApi, patchApi, postApi } from '~/services/api-services';

/*----------------Customer List ---------------- */

interface GymListParams {
    page: number;
    pageSize: number;
    search: string;
}

export const GymList: any = createAsyncThunk(
    'gym-list',
    async ({ page, pageSize,search }: GymListParams,) => {
        try {
            const response = await postApi(ADMIN_GYM_LIST, {
                page,
                pageSize,
                search
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Gym List ------------------ */

export const CreateGymOnboard: any = createAsyncThunk(
    'CreateGymOnboard',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_GYM_ORBOARDING, reqData);
            dispatch(GymList({ page: 1, pageSize: 10 }));
            Alertify.success('Gym created successfully')
            return response;
        } catch (error:any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Gym Details ------------------ */

export const GymDetails: any = createAsyncThunk(
    'GymDetails',
    async ({ gymId }: any,) => {
        try {
            const response = await getApi(`${GYM_DETAILS}/${gymId}`);
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Gym ------------------ */

export const UpdateGymOnboarding: any = createAsyncThunk(
    'UpdateGymOnboarding',
    async ({ reqData }:any, { dispatch }) => {
        try {
            const response = await patchApi(
                `${UPDATE_GYM_ORBOARDING}`,
                {
                    ...reqData,
                }
            );
            dispatch(GymList({ page: 1, pageSize: 10 }));
            Alertify.success('Gym updated successfully')
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);



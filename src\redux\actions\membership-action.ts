import { createAsyncThunk } from "@reduxjs/toolkit";
import { CREATE_MEMBERSHIP, MEMBERSHIP_LISTING, UPDATE_MEMBERSHIP_STATUS, MEMBERSHIP_DETAIL, UPDATE_MEMBERSHIP } from "~/constants/api-constants";
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';
import Alertify from '~/services/alertify';


export const CreateMembership: any = createAsyncThunk(
    'createMembership',
    async (payload: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_MEMBERSHIP, payload);
            Alertify.success('Membership created successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const MembershipList: any = createAsyncThunk(
    'membershipList',
    async ({ isActive, page, pageSize, search }: any,) => {

        try {
            const response = await postApi(MEMBERSHIP_LISTING, {
                page,
                pageSize,
                search,
                isActive,
            });
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }

)
export const updateMembershipStatus = createAsyncThunk(
    'Update status',
    async ({ isActive, membershipId }: any, { dispatch, rejectWithValue }) => {
        try {
            const response = await patchApi(
                `${UPDATE_MEMBERSHIP_STATUS}/${membershipId}`,
                {
                    isActive,
                }
            );
            dispatch(MembershipList({}));
            Alertify.success('Membership  Status updated successfully');
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data ||
                'Failed to update the status of the Feature'
            );
        }
    })
export const getMembershipById: any = createAsyncThunk(
    'Membership-Detail',
    async (membershipId: any, { rejectWithValue }) => {
        try {
            const response = await getApi(
                `${MEMBERSHIP_DETAIL}/${membershipId.memberShipId}`
            );
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch Feature detail'
            );
        }
    }

);
export const updateMembership: any = createAsyncThunk(
    'Update-Membership',
    async ({ reqData, membershipId }: any, { dispatch, rejectWithValue }) => {
        try {
            const response = await patchApi(`${UPDATE_MEMBERSHIP}/${membershipId}`, {
                ...reqData,
            });
            Alertify.success('Feature updated successfully');
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch Feature detail'
            );
        }
    }
)
import { createAsyncThunk, isRejectedWithValue } from '@reduxjs/toolkit';
import {
    MEMBERSHIP_SUSPENSION,MEMBERSHIP_SUSPENSION_REVOKE
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    postApi,
    handleApiError
} from '~/services/api-services';

/* Membership Suspension */
export const MembershipSuspension: any = createAsyncThunk(
    'Membership suspension',
    async ({ payload }: any, { rejectWithValue }: any) => {
        try {

            const response = await postApi(MEMBERSHIP_SUSPENSION, {
                ...payload
            })

            Alertify.success('Membership Suspended')
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
)
export const revokeMembershipSuspension: any = createAsyncThunk(
    'RevokeMembership Suspension',
    async ({ payload }: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(MEMBERSHIP_SUSPENSION_REVOKE, {
                ...payload
            })

            Alertify.success('Membership Suspension Revoke')
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
)

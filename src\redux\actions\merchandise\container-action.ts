interface Attribute {
    name: string;
    type?: 'optional' | 'variant' | 'mandatory'; // Make type optional for safety
    multiple: boolean;
    inputType: string;
}

interface MappedAttributes {
    optional: Attribute[];
    variant: Attribute[];
    mandatory: Attribute[];
}

export function _ManipulateMappingAttribute(data: any): MappedAttributes {
    return Object.entries(data).reduce<MappedAttributes>(
        (acc, [key, value]) => {

            // Ensure value is a valid object
            if (!value || typeof value !== 'object') {
                console.warn(`Skipping invalid attribute: ${key}`, value);
                return acc; // Skip invalid entries
            }

            // Ensure `type` exists and is valid
            if (!value.type || !['optional', 'variant', 'mandatory'].includes(value.type)) {
                console.warn(`Skipping attribute with missing or invalid type: ${key}`, value);
                return acc; // Skip this entry
            }

            // Construct attribute object
            const mappedAttribute: Attribute = {
                name: value.name, // ✅ Use `name` for consistency
                multiple: value.multiple,
                inputType: value.inputType,
                label: value.name,
                value: value.name,
            };

            acc[value.type].push(mappedAttribute);
            return acc;
        },
        {
            optional: [],
            variant: [],
            mandatory: [],
        }
    );
}

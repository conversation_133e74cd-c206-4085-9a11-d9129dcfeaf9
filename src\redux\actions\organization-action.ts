import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CREATE_ORGANIZATION,
    GET_SETTING_ACTIVE_STATUS,
    ORGANI<PERSON>ATION_DETAILS,
    ORGANIZATION_LIST,
    PERMISSIONS_LIST,
    SEND_MAIL,
    UPDATE_ORGANIZATION,
    UPDATE_ORGANIZATION_STATUS,
    UPDATE_SUPER_ADMIN_PERMISSIONS,
} from '~/constants/api-constants';

import Alertify from '~/services/alertify';
import { getApi, patchApi, postApi } from '~/services/api-services';

interface OrganizationParams {
    page?: number;
    pageSize?: number;
    search?: string;
    stateId?: string;
    cityId?: string;
}

/*----------------Organization List ---------------- */
export const OrganizationList: any = createAsyncThunk(
    'Organization-list',
    async ({ page, pageSize, search, stateId, cityId }: OrganizationParams) => {
        try {
            const response = await postApi(ORGANIZATION_LIST, {
                page,
                pageSize,
                search,
                stateId,
                cityId,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Organization ------------------ */

export const CreateNewOrganization: any = createAsyncThunk(
    'CreateOrganization',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_ORGANIZATION, reqData);
            dispatch(OrganizationList({ page: 1, pageSize: 10 }));
            Alertify.success('Organization created successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Organization Details ------------------ */

export const OrganizationDetails: any = createAsyncThunk(
    'OrganizationDetails',
    async ({ organizationId }: any) => {
        try {
            const response = await postApi(`${ORGANIZATION_DETAILS}`, {
                organizationId,
            });
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update organization ------------------ */

export const UpdateOrganization: any = createAsyncThunk(
    'UpdateOrganization',
    async ({ reqData, organizationId }: any, { dispatch }) => {
        try {
            const response = await patchApi(
                `${UPDATE_ORGANIZATION}/${organizationId}`,
                {
                    ...reqData,
                }
            );
            // dispatch(FacilitiesList({ page: 1, pageSize: 10 }));
            Alertify.success('Organization updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const sendPasswordMail = createAsyncThunk(
    'GeneratePin',
    async ({ id }: any) => {
        try {
            const res = await postApi(SEND_MAIL, { id });
            Alertify.success('Email sent successfully');
            return { res };
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

export const UpdateOrganizationStatus: any = createAsyncThunk(
    'UpdateOrganizationStatus',
    async ({ reqData, organizationId }: any) => {
        try {
            const response = await patchApi(
                `${UPDATE_ORGANIZATION_STATUS}/${organizationId}`,
                {
                    ...reqData,
                }
            );
            Alertify.success(
                `Organization ${
                    !reqData.status ? 'deactivated' : 'activated'
                } successfully`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// PERMISSIONS FOR ORGANIZATIONS ON SUPER ADMIN PANEL
export const PermissionsList: any = createAsyncThunk(
    'super-admin-organization-permissions-list',
    async (reqData: any) => {
        try {
            const response: any = await postApi(PERMISSIONS_LIST, reqData);
            return response;
        } catch (error: any) {
            console.log('Error fetch permissions API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// UPDATE PERMISSIONS FOR ORGANIZATIONS ON SUPER ADMIN PANEL
export const UpdatePermissions: any = createAsyncThunk(
    'update-organization-permissions-on-super-admin-panel',
    async (reqData: any) => {
        try {
            const { organizationId, ...restData } = reqData;
            const response: any = await postApi(
                `${UPDATE_SUPER_ADMIN_PERMISSIONS}${organizationId}`,
                restData.permissionsData
            );
            console.log(response);
            return response;
        } catch (error: any) {
            console.log('Error fetch permissions API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// CHECK IF A PERMISSION(SETTING) IS ALLOWED TO AN ORGANIZATION OR NOT
export const GetSettingActiveStatus: any = createAsyncThunk(
    'get-setting-active-status-for-logged-in-organization',
    async ({ settingKey }: any) => {
        try {
            // const {settingKey,...restData} = reqData;
            const response: any = await getApi(
                `${GET_SETTING_ACTIVE_STATUS}${settingKey}/get-setting`
            );
            // console.log('GetSettingActiveStatus',response);
            return response;
        } catch (error: any) {
            console.log('Error GetSettingActiveStatus API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

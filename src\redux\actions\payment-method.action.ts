import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ALL_PAYMENT_LIST, ADD_PAYMENT_METHOD, ADDED_PAYMENT_METHOD_LIST,UPDATE_PAYMENT_METHOD_STATUS,
    SUPER_ADMIN_PAYMENT_METHOD_LIST,
    CREATE_PAYMENT_METHOD,
    EDIT_ADMIN_PAYMENT_METHOD,
    GET_OR_DELETE_ADMIN_PAYMENT_METHOD
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';



export const AllPaymentMethodList: any = createAsyncThunk(
    'payment-method-list',
    async () => {
        try {
            const response = await postApi(ALL_PAYMENT_LIST);
            return response;
        } catch (error: any) {
            console.log('Error fetch All Payment Methods API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const AddPaymentMethod: any = createAsyncThunk(
    'add-payment-method',
    async (reqData: any) => {
        try {
            const response: any = await postApi(ADD_PAYMENT_METHOD, reqData)
            console.log(response)
            Alertify.success(response.message || "Payment Method Added Successfully")
        } catch (error: any) {
            console.log('Error fetch Add Payment Method API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
// CREATE PAYMENT METHOD AT SUPER ADMIN PANEL
export const CreatePaymentMethod: any = createAsyncThunk(
    'create-payment-method',
    async (reqData: any) => {
        try {
            const response: any = await postApi(CREATE_PAYMENT_METHOD, reqData)
            console.log(response)
            Alertify.success(response.message || "Payment Method Created Successfully")
        } catch (error: any) {
            console.log('Error creating Payment Method', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)

// EDIT SUPER ADMIN PAYMENT METHOD
export const EditAdminPaymentMethod: any = createAsyncThunk(
    'edit-admin-payment-method',
    async (reqData: any) => {
        try {
            const { paymentMethodId, ...restData } = reqData;
            const response: any = await patchApi(`${EDIT_ADMIN_PAYMENT_METHOD}/${paymentMethodId}`, restData)
            // console.log(response)
            Alertify.success(response.message || "Payment Method Edited Successfully")
        } catch (error: any) {
            console.log('Error editing Payment Method', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
// GET ADMIN PAYMENT METHOD DETAILS
export const DetailsAdminPaymentMethod: any = createAsyncThunk(
    'details-admin-payment-method',
    async (reqData: any) => {
        try {
            const { paymentMethodId } = reqData;
            const response: any = await getApi(`${GET_OR_DELETE_ADMIN_PAYMENT_METHOD}/${paymentMethodId}`)
            // console.log(response);
            return response;
            // Alertify.success(response.message || "Payment Method Edited Successfully")
        } catch (error: any) {
            console.log('Error getting details Payment Method', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
// DELETE ADMIN PAYMENT METHOD DETAILS
export const DeleteAdminPaymentMethod: any = createAsyncThunk(
    'delete-admin-payment-method',
    async (reqData: any) => {
        try {
            const { paymentMethodId } = reqData;
            const response: any = await deleteApi(`${GET_OR_DELETE_ADMIN_PAYMENT_METHOD}/${paymentMethodId}`)
            // console.log("response is:", response);
            return response;
            // Alertify.success(response.message || "Payment Method Edited Successfully")
        } catch (error: any) {
            console.log('Error getting details Payment Method', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
export const AddedPaymentMethodList: any = createAsyncThunk(
    'added-paymentMethod-list',
    async (reqData: any) => {
        try {
            const response: any = await postApi(ADDED_PAYMENT_METHOD_LIST, reqData)
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
// PAYMENT METHOD LIST FOR SUPER ADMIN
export const SuperAdminPaymentMethodList: any = createAsyncThunk(
    'super-admin-paymentMethod-list',
    async (reqData: any) => {
        try {
            const response: any = await postApi(SUPER_ADMIN_PAYMENT_METHOD_LIST, reqData)
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
export const updatePaymentStatus: any = createAsyncThunk(
    'update-paymentMethod-status',
    async (reqData: any) => {
        try {
            const response: any = await patchApi(UPDATE_PAYMENT_METHOD_STATUS, reqData)
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
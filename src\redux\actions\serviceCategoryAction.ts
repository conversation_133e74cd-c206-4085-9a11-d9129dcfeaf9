import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ACTIVE_SERVICE_CATEGORY_LIST,
    APPOINTMENT_TYPE_DETAILS,
    CREATE_SERVICE_CATEGORY,
    CRETAE_APPOINTMENT_TYPE,
    SERVICE_CATEGORY_DETAILS,
    SERVICE_CATEGORY_LIST,
    SERVICE_CATEGORY_LIST_PACKAGE_ID,
    SERVICE_CATEGORY_LIST_STAFF_ID,
    UPDATE_APPOINTMENT_TYPE,
    UPDATE_APPOINTMENT_TYPE_STATUS,
    UPDATE_SERVICE_CATEGORY_DETAILS,
    ACTIVE_SERVICE_CATEGORY,
    DELETE_APPOINTMENT_TYPE,
    ACTIVE_SERVICE_CATEGORY_V1,
    ACTIVE_SERVICE_CATEGORY_FOR_PRICING,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { getApi, patchApi, postApi } from '~/services/api-services';

interface ServiceCategoryParams {
    page?: number;
    pageSize?: number;
    search?: string;
    classType?: string;
    packageId?: string;
    organizationId?: string;
    facilityIds?: string[];
    trainerIds?: string[];
    serviceCategoryId?: string;
    appointmentTypes?: string[];
}

/*------------------- Service Category List ------------------ */
export const ServiceCategoryList: any = createAsyncThunk(
    'ServiceCategoryList',
    async ({
        page,
        pageSize,
        search,
        classType,
        serviceCategoryId,
        appointmentTypes,
    }: ServiceCategoryParams) => {
        try {
            const response = await postApi(ACTIVE_SERVICE_CATEGORY_LIST, {
                page,
                pageSize,
                search,
                classType,
                serviceCategoryId,
                appointmentTypes,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch Services List API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const AllServiceCategoryList: any = createAsyncThunk(
    'AllServiceCategoryList',
    async ({ page, pageSize, search, classType }: ServiceCategoryParams) => {
        try {
            const response = await postApi(SERVICE_CATEGORY_LIST, {
                page,
                pageSize,
                search,
                classType,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch Services List API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Service Category List ------------------ */
export const ServiceCategoryListByPackageId: any = createAsyncThunk(
    'ServiceCategoryListByPackageId',
    async ({ packageId }: ServiceCategoryParams) => {
        try {
            const response = await getApi(
                `${SERVICE_CATEGORY_LIST_PACKAGE_ID}/${packageId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch Services List API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Service Category List ------------------ */
export const ServiceCategoryListByStaffId: any = createAsyncThunk(
    'ServiceCategoryListByStaffId',
    async (
        { facilityIds, trainerIds, classType }: ServiceCategoryParams,
        { getState }: any
    ) => {
        try {
            const { organizationId } = getState().auth_store;
            const obj: any = { organizationId, facilityIds, trainerIds };
            if (classType) obj.classType = classType;
            const response = await postApi(SERVICE_CATEGORY_LIST_STAFF_ID, obj);
            return response;
        } catch (error: any) {
            console.log('Error fetch Services List API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Service Category ------------------ */
export const CreateNewServiceCategory: any = createAsyncThunk(
    'CreateNewServiceCategory',
    async (reqData: any) => {
        try {
            const response = await postApi(CREATE_SERVICE_CATEGORY, reqData);
            Alertify.success('Service Category created successfully');
            return response;
        } catch (error: any) {
            console.log('Error CreateNewServiceCategory API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Appointment Type ------------------ */
export const CreateAppointmentType: any = createAsyncThunk(
    'CreateAppointmentType',
    async (reqData: any) => {
        try {
            const response = await postApi(CRETAE_APPOINTMENT_TYPE, reqData);
            Alertify.success('Sub type added successfully');
            return response;
        } catch (error: any) {
            console.log('Error CreateAppointmentType API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Service Category Details ------------------ */
export const ServiceCategoryDetail: any = createAsyncThunk(
    'ServiceCategoryDetail',
    async ({ serviceId }: any) => {
        try {
            const response = await getApi(
                `${SERVICE_CATEGORY_DETAILS}/${serviceId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch Services Details API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Appointment Service ------------------ */
export const UpdateServiceCategory: any = createAsyncThunk(
    'UpdateServiceCategory',
    async (reqData: any) => {
        try {
            const response = await patchApi(
                UPDATE_SERVICE_CATEGORY_DETAILS,
                reqData
            );
            Alertify.success('Service category has been updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error UpdateServiceCategory API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const UpdateAppointmentTypeStatus: any = createAsyncThunk(
    'UpdateAppointmentTypeStatus',
    async ({ reqData }: any) => {
        try {
            const response = await patchApi(
                `${UPDATE_APPOINTMENT_TYPE_STATUS}`,
                {
                    ...reqData,
                }
            );
            Alertify.success(
                `Sub type ${
                    !reqData.isActive ? 'deactivated' : 'activated'
                } successfully`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Appointment Type Details ------------------ */
export const AppointmentTypeDetail: any = createAsyncThunk(
    'AppointmentTypeDetail',
    async ({ serviceId, appointmentId }: any) => {
        try {
            const response = await getApi(
                `${APPOINTMENT_TYPE_DETAILS}/${serviceId}/appointmentType/${appointmentId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch Appointment Type Details API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Appointment Type ------------------ */
export const UpdateAppointmentType: any = createAsyncThunk(
    'UpdateAppointmentType',
    async (reqData: any) => {
        try {
            const response = await patchApi(UPDATE_APPOINTMENT_TYPE, reqData);
            Alertify.success('Sub type has been updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error UpdateAppointmentType API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const DeleteAppointmentType: any = createAsyncThunk(
    'DeleteAppointmentType',
    async (reqData: any) => {
        try {
            const response = await patchApi(DELETE_APPOINTMENT_TYPE, reqData);
            Alertify.success('Sub type deleted successfully');
            return response;
        } catch (error: any) {
            console.log('Error UpdateAppointmentType API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const activeServiceCategoryList: any = createAsyncThunk(
    'ActiveServiceCategory',
    async (reqData: any) => {
        try {
            const response = await postApi(ACTIVE_SERVICE_CATEGORY, {
                ...reqData,
            });
            return response;
        } catch (error) {
            console.log(error);
        }
    }
);
export const activeServiceCategoyListv1: any = createAsyncThunk(
    'ActiveServiceCategoryV1',
    async ({ classType }: any, { dispatch, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(ACTIVE_SERVICE_CATEGORY_V1, {
                classType,
                organizationId,
            });
            return response;
        } catch (error: any) {
            console.log('Error UpdateAppointmentType API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const activeServiceCategoyListPricing: any = createAsyncThunk(
    'activeServiceCategoyListPricing',
    async ({ packageId }: any) => {
        try {
            const response = await postApi(
                ACTIVE_SERVICE_CATEGORY_FOR_PRICING,
                {
                    packageId,
                }
            );
            return response;
        } catch (error: any) {
            console.log('Error UpdateAppointmentType API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

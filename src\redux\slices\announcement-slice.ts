import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    AnnouncementList,
    GetAnnouncementById,
    DeleteAnnouncement,
    UpdateAnnouncementStatus,
} from '~/redux/actions/announcement-action';

interface Announcement {
    _id?: string;
    title: string;
    subtitle: string;
    description: string;
    imageUrl: string;
    isActive: string;
}
interface InitialState {
    announcementList: Announcement[];
    announcementDetails: Announcement | null;
    announcementCount: number;
    loading: boolean;
    error: string | null;
    listingLoading: boolean;
}

const initialState: InitialState = {
    announcementList: [],
    announcementDetails: null,
    announcementCount: 0,
    loading: false,
    error: null,
    listingLoading: false,
};

const announcementSlice = createSlice({
    name: 'announcement',
    initialState,
    reducers: {
        resetAnnouncementState: () => initialState,
        clearAnnouncementDetails: (state) => {
            state.announcementDetails = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(AnnouncementList.pending, (state) => {
                state.listingLoading = true;
                state.error = null;
            })
            .addCase(
                AnnouncementList.fulfilled,
                (
                    state,
                    action: PayloadAction<{
                        announcement: Announcement[];
                        count: number;
                        data: any;
                    }>
                ) => {
                    state.listingLoading = false;
                    state.announcementList = action.payload.data?.data;
                    state.announcementCount = action.payload.data.total;
                    state.error = null;
                }
            )
            .addCase(AnnouncementList.rejected, (state, action) => {
                state.listingLoading = false;
                state.error =
                    action.error.message || 'Failed to fetch Announcement';
            })
            .addCase(
                GetAnnouncementById.fulfilled,
                (state, action: PayloadAction<Announcement>) => {
                    state.announcementDetails = action.payload?.data?.data;
                }
            )
            .addCase(DeleteAnnouncement.fulfilled, (state, action: any) => {
                state.announcementList = state.announcementList.filter(
                    (announcement: Announcement) =>
                        announcement._id !== action.meta?.arg?.announcementId
                );
            })
            .addCase(
                UpdateAnnouncementStatus.fulfilled,
                (state, action: any) => {
                    state.announcementList = state.announcementList.map(
                        (announcement: Announcement) => {
                            if (
                                announcement._id ===
                                action.meta?.arg?.announcementId
                            )
                                announcement.isActive =
                                    action.meta?.arg?.isActive;

                            return announcement;
                        }
                    );
                }
            );
    },
});

export const { resetAnnouncementState, clearAnnouncementDetails } =
    announcementSlice.actions;
export default announcementSlice.reducer;

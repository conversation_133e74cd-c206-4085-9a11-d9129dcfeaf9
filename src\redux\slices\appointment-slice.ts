import { createSlice } from '@reduxjs/toolkit';
import {
    BookedAppointmentList,
    ManipulateBookAppointmentData,
    StaffAvailabilityDetails,
    StaffAvailabilityList,
    TrainerListing,
    BookedAppointmentDetails,
    CreateBookAppointment,
    DeleteBookedAppointment,
    UpdateBookedAppointment,
    PricingListing,
    PricingListingByUserAndType,
    CanceledBookedAppointment,
    TrainerListingForPayRate,
    PricingListingByUserAndSubType,
} from '../actions/appointment-action';

interface AppointmentState {
    staffAvailabilityList: Array<any>;
    trainerList: Array<any>;
    trainerListPayRate: Array<any>;
    pricingList: Array<any>;
    trainerListForForms: Array<any>;
    StaffAvailabilityDetails: any;
    facilityId: string;
    bookedAppointmentData: Array<any>;
    bookedAppointmentDetails: any;
    pricingListUserAndSubType: Array<any>;
}

const initialState: AppointmentState = {
    staffAvailabilityList: [],
    trainerList: [],
    trainerListPayRate: [],
    pricingList: [],
    trainerListForForms: [],
    StaffAvailabilityDetails: [] as any,
    facilityId: '',
    bookedAppointmentData: [],
    bookedAppointmentDetails: {},
    pricingListUserAndSubType: [] as any,
};

const appointmentSlice = createSlice({
    name: 'appointmentSlice',
    initialState: initialState,
    reducers: {
        SetFacilityId: (state, { payload }) => {
            state.facilityId = payload;
        },
        ClearStaffFields: () => {
            return initialState;
        },
    },
    extraReducers(builder) {
        builder.addCase(
            StaffAvailabilityList.fulfilled,
            (state, { payload }) => {
                state.staffAvailabilityList = payload?.data?.data;
            }
        );
        builder.addCase(
            TrainerListing.fulfilled,
            (state, { payload, meta }) => {
                if (meta.arg.isForFilter)
                    state.trainerList = payload?.data?.data;
                else state.trainerListForForms = payload?.data?.data;
            }
        );
        builder.addCase(
            TrainerListingForPayRate.fulfilled,
            (state, { payload }) => {
                state.trainerListPayRate = payload?.data?.data;
            }
        );
        builder.addCase(PricingListing.fulfilled, (state, { payload }) => {
            state.pricingList = payload?.data?.data;
        });
        builder.addCase(
            PricingListingByUserAndType.fulfilled,
            (state, { payload }) => {
                state.pricingList = payload?.data?.data;
            }
        );
        builder.addCase(
            StaffAvailabilityDetails.fulfilled,
            (state, { payload }) => {
                // console.log('Payload----------', payload);
                state.StaffAvailabilityDetails = payload?.data?.data[0];
            }
        );
        builder.addCase(
            BookedAppointmentList.fulfilled,
            (state, { payload }) => {
                // console.log('Payload----------', payload);
                state.bookedAppointmentData = payload?.data?.data.map(
                    (item: any) => ManipulateBookAppointmentData(item)
                );
            }
        );
        builder.addCase(
            CreateBookAppointment.fulfilled,
            (state, { payload }) => {
                // Check if payload is valid and handle the case where payload might be null or undefined
                if (payload?.data?.data) {
                    const manipulatedData = ManipulateBookAppointmentData(
                        payload.data.data[0]
                    );
                    state.bookedAppointmentData = [
                        ...state.bookedAppointmentData,
                        manipulatedData,
                    ];
                } else {
                    console.error(
                        'Invalid payload received in CreateBookAppointment.'
                    );
                }
            }
        );

        builder.addCase(
            BookedAppointmentDetails.fulfilled,
            (state, { payload }) => {
                // Ensure payload is valid and handle the data safely
                if (payload?.data?.data) {
                    const manipulatedData = payload.data.data;

                    state.bookedAppointmentDetails = manipulatedData;
                } else {
                    console.error(
                        'Invalid payload received in BookedAppointmentDetails.'
                    );
                }
            }
        );

        builder.addCase(
            CanceledBookedAppointment.fulfilled,
            (state, { meta }) => {
                // Safely check if ID exists in payload before performing filtering
                console.log("CanceledBookedAppointment---------------", meta)
                if (meta?.arg?.id) {
                    state.bookedAppointmentData =
                        state.bookedAppointmentData.filter(
                            (item) => item.id !== meta.arg.id
                        );
                } else {
                    console.error(
                        'Invalid payload received in DeleteBookedAppointment.'
                    );
                }
            }
        );

        builder.addCase(
            UpdateBookedAppointment.fulfilled,
            (state, { payload, meta }) => {
                // Ensure the payload is valid before performing the update
                if (meta?.arg?.payload?.id && payload?.data?.data) {
                    const manipulatedData = ManipulateBookAppointmentData(
                        payload.data.data
                    );
                    state.bookedAppointmentData =
                        state.bookedAppointmentData.map((item) =>
                            item.id === meta.arg.id ? manipulatedData : item
                        );
                    state.bookedAppointmentDetails = {};
                } else {
                    console.error(
                        'Invalid payload received in UpdateBookedAppointment.'
                    );
                }
            }
        );
        builder.addCase(
            PricingListingByUserAndSubType.fulfilled,
            (state, { payload }) => {
                state.pricingListUserAndSubType = payload?.data?.data;
            }
        );
    },
});

export const { ClearStaffFields, SetFacilityId } = appointmentSlice.actions;

export default appointmentSlice.reducer;

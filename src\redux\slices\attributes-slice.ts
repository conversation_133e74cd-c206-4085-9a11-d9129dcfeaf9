import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    AttributeDetails,
    AttributeList,
    AttributeParentList,
    AttributeTypeList,
    AttributeTypeListForDropdown,
    DeleteAttributeData,
    UpdateAttributeStatus,
} from '../actions/attribute-action';

interface AuthState {
    attributeList: [];
    parentAttributeList: [];
    attributeListCount: number;
    attributeDetails: [];
    attributeTypeList: [];
    attributeTypeDropdownList: [];
    attributeTypeListCount: number;
}

const initialState: AuthState = {
    attributeList: [],
    parentAttributeList: [],
    attributeListCount: 0,
    attributeDetails: [],
    attributeTypeList: [],
    attributeTypeDropdownList: [],
    attributeTypeListCount: 0,
};

const attributeSlice = createSlice({
    name: 'attribute_store',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(
            AttributeList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log("Payload------------", payload)
                state.attributeList = payload?.data?.data?.list;
                state.attributeListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            AttributeParentList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.parentAttributeList = payload?.data?.data;
            }
        );
        builder.addCase(
            AttributeTypeList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.attributeTypeList = payload?.data?.data;
                state.attributeTypeListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            AttributeTypeListForDropdown.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload?.data?.data);
                state.attributeTypeDropdownList = payload?.data?.data?.map(
                    (data: any) => ({ label: data.name, value: data._id })
                );
            }
        );
        builder.addCase(
            AttributeDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.attributeDetails = payload?.data;
            }
        );
        builder.addCase(
            UpdateAttributeStatus.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.attributeTypeList = state.attributeTypeList.map(
                    (attribute: any) => {
                        if (attribute._id === payload.data.data._id)
                            attribute.isActive = payload.data.data.isActive;
                        return attribute;
                    }
                );
            }
        );
        builder.addCase(
            DeleteAttributeData.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.attributeTypeList = state.attributeTypeList.filter(
                    (attribute: any) => attribute._id !== payload.data.data._id
                );
            }
        );
    },
});

// export const { logout } = attributeSlice.actions;

export default attributeSlice.reducer;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    DeleteFacilityUnavailability,
    UpdateFacilityUnavailability,
    FacilitiesList,
    FacilityDetails,
    FacilityUnavailabilityDetails,
    GetFacilityListByStaffId,
    UpdateAllFacilityUnavailabilities,
    UpdateFacilityStatus,
    UpdateFacilityStoreStatus,
} from '../actions/facility-action';

interface FacilityState {
    facilityList: any;
    facilityListByStaffId: [];
    facilityListCount: number;
    facilityDetails: any;
    facilityUnavailabilityDetails: any;
}

const initialState: FacilityState = {
    facilityList: [],
    facilityListByStaffId: [],
    facilityListCount: 0,
    facilityDetails: [],
    facilityUnavailabilityDetails: [],
};

const facilitySlice = createSlice({
    name: 'facility_store',
    initialState,
    reducers: {
        clearFacilityUnavailabilityDetails(state) {
            state.facilityUnavailabilityDetails = [];
        },
    },
    extraReducers: (builder) => {
        builder.addCase(
            FacilitiesList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload----------', payload);
                state.facilityList = payload?.data?.data?.list;
                state.facilityListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            FacilityDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.facilityDetails = payload?.data?.data;
            }
        );
        builder.addCase(
            GetFacilityListByStaffId.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.facilityListByStaffId = payload?.data?.data;
            }
        );
        builder.addCase(
            FacilityUnavailabilityDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload-----------------', payload);
                state.facilityUnavailabilityDetails = payload?.data?.data;
            }
        );

        builder.addCase(
            DeleteFacilityUnavailability.fulfilled,
            (state, { meta }) => {
                if (meta?.arg?._id) {
                    state.facilityUnavailabilityDetails.unAvailabilities =
                        state.facilityUnavailabilityDetails.unAvailabilities.filter(
                            (item: any) => item._id !== meta.arg._id
                        );
                } else {
                    console.error(
                        'Invalid payload received in DeleteBookedAppointment.'
                    );
                }
            }
        );

        builder.addCase(
            UpdateFacilityUnavailability.fulfilled,
            (state, { payload, meta }) => {
                if (meta?.arg?.payload?._id && payload?.data?.data) {
                    state.facilityUnavailabilityDetails = [];
                    // state.facilityUnavailabilityDetails.unAvailabilities.map(
                    //     (item: any) =>
                    //         item._id === meta.arg._id
                    //             ? { ...item, time: meta.arg.time }
                    //             : item
                    // );
                } else {
                    console.error(
                        'Invalid payload received in UpdateFacilityUnavailability.'
                    );
                }
            }
        );
        builder.addCase(
            UpdateAllFacilityUnavailabilities.fulfilled,
            (state, { payload }) => {
                if (payload?.data?.data) {
                    state.facilityUnavailabilityDetails = [];
                    // state.facilityUnavailabilityDetails.unAvailabilities.map(
                    //     (item: any) =>
                    //         item._id === meta.arg._id
                    //             ? { ...item, time: meta.arg.time }
                    //             : item
                    // );
                } else {
                    console.error(
                        'Invalid payload received in UpdateAllFacilityUnavailabilities.'
                    );
                }
            }
        );
        builder.addCase(
            UpdateFacilityStatus.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.facilityList = state.facilityList.map((facility: any) => {
                    if (facility._id === payload.data.data?.facility?._id)
                        facility.isActive =
                            payload.data.data?.facility?.isActive;
                    return facility;
                });
            }
        );
        builder.addCase(
            UpdateFacilityStoreStatus.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.facilityList = state.facilityList.map((facility: any) => {
                    if (facility._id === payload.data.data?.facility?._id)
                        facility.isStoreActive =
                            payload.data.data?.facility?.isStoreActive;
                    return facility;
                });
            }
        );
    },
});

export const { clearFacilityUnavailabilityDetails } = facilitySlice.actions;

export default facilitySlice.reducer;

import { createSlice, PayloadAction, } from "@reduxjs/toolkit";
import { GymDetails, GymList } from "../actions/gym-action";

interface AuthState {
  gymList : [],
  gymListCount : number,
  gymDetails : [],
}


const initialState: AuthState = {
    gymList: [],
    gymListCount: 0,
    gymDetails : [],
};

const gymSlice = createSlice({
  name: "gym_store",
  initialState,
  reducers: {
  },
  extraReducers: builder => {
    builder.addCase(GymList.fulfilled, (state, { payload }: PayloadAction<any>) => {
        state.gymList = payload?.data?.data;
        state.gymListCount = payload?.data?.pagination
    });
    builder.addCase(GymDetails.fulfilled, (state, { payload }: PayloadAction<any>) => {
      // console.log("Payload----------", payload)
      state.gymDetails = payload?.data?.data;
  });
  },
});

// export const { logout } = customerSlice.actions;

export default gymSlice.reducer;

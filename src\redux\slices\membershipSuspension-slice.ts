import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  MembershipSuspension, revokeMembershipSuspension
} from '~/redux/actions/membershipSuspension-action'

interface MembershipSuspensionState {
  loading: boolean;
  success: boolean;
  error: string | null;
}

const initialState: MembershipSuspensionState = {
  loading: false,
  success: false,
  error: null,
};
const membershipSuspensionSlice = createSlice({
  name: 'membershipSuspension',
  initialState,
  reducers: {
    resetSuspensionState: (state) => {
      state.loading = false;
      state.success = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(MembershipSuspension.pending, (state) => {
        state.loading = true;
        state.success = false;
        state.error = null;
      })
      .addCase(MembershipSuspension.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
        state.error = null;
      })
      .addCase(MembershipSuspension.rejected, (state, action) => {
        state.loading = false;
        state.success = false;
        state.error = action.payload || 'Something went wrong';
      })
      // Revoke Membership Suspension
      .addCase(revokeMembershipSuspension.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(revokeMembershipSuspension.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(revokeMembershipSuspension.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

// Export Actions
export const { resetSuspensionState } = membershipSuspensionSlice.actions;

// Export Reducer
export default membershipSuspensionSlice.reducer;
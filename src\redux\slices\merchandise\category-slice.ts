import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PrimaryCategoryListData } from '../../actions/merchandise/category-action'

interface CategoryState {
    parentCategoryList: any[]; 
    parentCategoryCount:any;
    loading: boolean;
    error: string | null;
}

// Initial state
const initialState: CategoryState = {
    parentCategoryList: [],
    parentCategoryCount:null,
    loading: false,
    error: null,
};

// Create the Redux slice
const categorySlice = createSlice({
    name: 'category',
    initialState,
    reducers: {}, // No additional reducers for now
    extraReducers: (builder) => {
        builder
            .addCase(PrimaryCategoryListData.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(
                PrimaryCategoryListData.fulfilled,
                (state, action: PayloadAction<{ data:any }>) => {
                    console.log(action.payload.data)
                    state.loading = false;
                    state.parentCategoryList = action.payload.data.data.list;
                    state.parentCategoryCount = action.payload.data.data.count;
                }
            )
            .addCase(PrimaryCategoryListData.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string || 'Failed to fetch categories';
            });
    },
});

export default categorySlice.reducer;

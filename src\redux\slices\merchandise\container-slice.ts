import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ActiveDynamicAttributeList, AttributeListData } from '../../actions/merchandise/attribute-action';
import { _ManipulateMappingAttribute } from '../../actions/merchandise/container-action';

// Define Attribute Type
interface Attribute {
    name: string;
    type: 'optional' | 'variant' | 'mandatory';
    multiple: boolean;
    inputType: string;
}

// Define State Type
interface ContainerState {
    attributes: Record<string, Attribute>;
    attributes_mapping: {
        optional: Attribute[];
        mandatory: Attribute[];
        variant: Attribute[];
    };
    active_attributes_mapping: {
        optional: Attribute[];
        mandatory: Attribute[];
        variant: Attribute[];
    };
    categories: any[];
    sub_category: any[];
    sub_child_category: any[];
    brands_data: any[];
}

const initialState: ContainerState = {
    attributes: {},
    attributes_mapping: {
        optional: [],
        mandatory: [],
        variant: [],
    },
    active_attributes_mapping: {
        optional: [],
        mandatory: [],
        variant: [],
    },
    categories: [],
    sub_category: [],
    sub_child_category: [],
    brands_data: [],
};

const containerSlice = createSlice({
    name: 'container_store',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(AttributeListData.fulfilled, (state, { payload }: PayloadAction<Record<string, Attribute>>) => {
            if (payload && typeof payload === 'object') {
                state.attributes = payload;
                state.attributes_mapping = _ManipulateMappingAttribute(payload.data);
            } else {
                console.error('Invalid payload structure:', payload);
            }
        })
        builder.addCase(ActiveDynamicAttributeList.fulfilled, (state, { payload }: PayloadAction<Record<string, Attribute>>) => {
            if (payload && typeof payload === 'object') {
                state.attributes = payload;
                state.active_attributes_mapping = _ManipulateMappingAttribute(payload.data);
            } else {
                console.error('Invalid payload structure:', payload);
            }
        });
    },
});

export default containerSlice.reducer;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ProductListData, ManipulateProductLists } from '~/redux/actions/merchandise/product-action';


interface ProductState {
    productList: any[];
    productListCount: any;
    loading: boolean;
    error: string | null;
}

// Initial state
const initialState: ProductState = {
    productList: [],
    productListCount: null,
    loading: false,
    error: null,
};
const productSlice = createSlice({
    name: 'category',
    initialState,
    reducers: {}, // No additional reducers for now
    extraReducers: (builder) => {
        builder
            .addCase(ProductListData.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(
                ProductListData.fulfilled,
                (state, action: PayloadAction<{ data: any }>) => {
                    console.log(action.payload.data)
                    state.loading = false;
                    state.productList = ManipulateProductLists(action.payload.data.list);
                    state.productListCount = action.payload.data.count;
                }
            )
            .addCase(ProductListData.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string || 'Failed to fetch categories';
            });
    },
})
export default productSlice.reducer;

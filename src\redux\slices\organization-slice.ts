import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CityList, CountryList } from '../actions/common-action';
import {
    OrganizationDetails,
    OrganizationList,
    UpdateOrganizationStatus,
} from '../actions/organization-action';

interface OrganizationState {
    organizationList: [];
    organizationListCount: number;
    organizationDetails: any;
}

const initialState: OrganizationState = {
    organizationList: [],
    organizationListCount: 0,
    organizationDetails: [],
};

const organizationSlice = createSlice({
    name: 'organization_store',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(
            OrganizationList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.organizationList = payload?.data?.data?.list;
                state.organizationListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            OrganizationDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload-----------', payload);
                state.organizationDetails = payload?.data;
            }
        );
        builder.addCase(
            UpdateOrganizationStatus.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.organizationList = state.organizationList.map(
                    (organization: any) => {
                        if (
                            organization._id ===
                            payload.data.data?.organization?._id
                        )
                            organization.status =
                                payload.data.data?.organization?.isActive;
                        return organization;
                    }
                );
            }
        );
    },
});

// export const { setSearchValue, clearSearchValue } = organizationSlice.actions;

export default organizationSlice.reducer;

import { createSlice } from '@reduxjs/toolkit';
import {
    createPayRate,
    deletePayRate,
    getAllClassTypeByStaffId,
    getAllPayRates,
    getAllServiceCategories,
    getPayRateDetails,
    updatePayRate,
} from '~/redux/actions/pay-rate-actions';

interface PayRate {
    _id?: string;
    serviceType: string;
    serviceCategory: string;
    appointmentType: string;
    payRate: number;
}

interface InitialState {
    payRateList: PayRate[];
    serviceCategoryList: [];
    classTypes: string[];
    payRateDetails: PayRate | null;
    payRateListCount: number;
    error: any;
}

const initialState: InitialState = {
    payRateList: [],
    classTypes: [],
    serviceCategoryList: [],
    payRateDetails: null,
    payRateListCount: 0,
    error: null,
};

const payRateSlice = createSlice({
    name: 'service',
    initialState,
    reducers: {
        resetPayRate: () => initialState,
        clearPayRateDetails: (state) => {
            state.payRateDetails = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(getAllPayRates.fulfilled, (state, { payload }) => {
                // console.log("Payload------------------", payload);
                state.payRateList = payload.data;
                state.payRateListCount = payload.total
            })
            .addCase(getAllPayRates.rejected, (state, action) => {
                state.error = action.payload;
            })
            .addCase(getPayRateDetails.fulfilled, (state, { payload }) => {
                state.payRateDetails = payload.data.data;
            })
            .addCase(createPayRate.fulfilled, (state) => {
                state.payRateDetails = null;
            })
            .addCase(updatePayRate.fulfilled, (state) => {
                state.payRateDetails = null;
            })
            .addCase(deletePayRate.fulfilled, (state, { payload }) => {
                state.payRateList = state.payRateList.filter(
                    (service) => service._id !== payload
                );
            })
            .addCase(
                getAllClassTypeByStaffId.fulfilled,
                (state, { payload }) => {
                    state.classTypes = payload.data;
                }
            )
            .addCase(
                getAllServiceCategories.fulfilled,
                (state, { payload }) => {
                    state.serviceCategoryList = payload.data || [];
                }
            );
    },
});

export const { resetPayRate, clearPayRateDetails } = payRateSlice.actions;
export default payRateSlice.reducer;

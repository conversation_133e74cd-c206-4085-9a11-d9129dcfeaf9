import { AllPaymentMethodList,AddedPaymentMethodList, SuperAdminPaymentMethodList } from "../actions/payment-method.action";
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    paymentMethodsList: [],
    addPaymenthodsList:[],
    superAdminPaymentMethodsList:[],
    loading: false,
    error: null,
};

const paymentSlice = createSlice({
    name: 'payment',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(AllPaymentMethodList.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(AllPaymentMethodList.fulfilled, (state, action) => {
                state.loading = false;
                state.paymentMethodsList = action.payload.data.list;
            })
            .addCase(AllPaymentMethodList.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(AddedPaymentMethodList.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(AddedPaymentMethodList.fulfilled, (state, action) => {
                state.loading = false;
                state.addPaymenthodsList = action.payload.data.data;
            })
            .addCase(AddedPaymentMethodList.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
// Slice for super admin payment method list
            .addCase(SuperAdminPaymentMethodList.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(SuperAdminPaymentMethodList.fulfilled, (state, action) => {
                state.loading = false;
                // console.log("store.superAdminPaymentMethodsList22222222222222222222222", action.payload.data.list)
                state.superAdminPaymentMethodsList = action.payload.data.list;
            })
            .addCase(SuperAdminPaymentMethodList.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
    }
});

export default paymentSlice.reducer;

import { createSlice } from '@reduxjs/toolkit';
import {
    roomListing,
    createRoom,
    getRoomDetails,
    roomListingByServiceCategory,
    roomListingByFacilityId,
    roomListingByScheduling,
} from '~/redux/actions/room-action';

interface Room {
    _id?: string;
    name: string;
    capacity: number;
    facilityId: string;
    amenities: string[];
}

interface InitialState {
    roomList: Room[];
    roomListByFacilityId: Room[];
    roomDetails: Room | null;
    roomListCount: number;
    loading: boolean;
    error: string | null;
    roomListScheduling:Room[];
}

const initialState: InitialState = {
    roomList: [],
    roomListByFacilityId: [],
    roomDetails: null,
    roomListCount: 0,
    loading: false,
    error: null,
    roomListScheduling:[]
};

const roomSlice = createSlice({
    name: 'room',
    initialState,
    reducers: {
        resetRoomState: () => initialState,
        clearRoomDetails: (state) => {
            state.roomDetails = null;
        },
    },
    extraReducers: (builder) => {
        builder
            // Handle roomListing
            .addCase(roomListing.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(roomListing.fulfilled, (state, { payload }) => {
                state.loading = false;
                state.roomList = payload.data.list; // Assuming API returns { data, total }
                state.roomListCount = payload.data.count;
            })
            .addCase(roomListing.rejected, (state, { error }) => {
                state.loading = false;
                state.error = error.message || 'Failed to fetch room list';
            })

            .addCase(roomListingByFacilityId.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(
                roomListingByFacilityId.fulfilled,
                (state, { payload }) => {
                    state.loading = false;
                    state.roomListByFacilityId = payload.data;
                }
            )
            .addCase(roomListingByFacilityId.rejected, (state, { error }) => {
                state.loading = false;
                state.error = error.message || 'Failed to fetch room list';
            })
            .addCase(roomListingByServiceCategory.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(
                roomListingByServiceCategory.fulfilled,
                (state, { payload }) => {
                    state.loading = false;
                    state.roomList = payload.data;
                }
            )
            .addCase(
                roomListingByServiceCategory.rejected,
                (state, { error }) => {
                    state.loading = false;
                    state.error = error.message || 'Failed to fetch room list';
                }
            )
            // Handle createRoom
            .addCase(createRoom.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createRoom.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(createRoom.rejected, (state, { error }) => {
                state.loading = false;
                state.error = error.message || 'Failed to create room';
            })
            .addCase(getRoomDetails.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getRoomDetails.fulfilled, (state, { payload }) => {
                console.log('Room Details:', payload);
                state.loading = false;
                state.roomDetails = payload; // Assuming API returns the room details directly
            })
            .addCase(getRoomDetails.rejected, (state, { error }) => {
                state.loading = false;
                state.error = error.message || 'Failed to fetch room details';
            })
            .addCase(roomListingByScheduling.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(
                roomListingByScheduling.fulfilled,
                (state, { payload }) => {
                    console.log(payload,"room payload")
                    state.loading = false;
                    state.roomListScheduling = payload.data.data;
                }
            )
            .addCase(roomListingByScheduling.rejected, (state, { error }) => {
                state.loading = false;
                state.error = error.message || 'Failed to fetch room details';
            })
    },
});

export const { resetRoomState, clearRoomDetails } = roomSlice.actions;
export default roomSlice.reducer;

import { createSlice } from '@reduxjs/toolkit';
import { GetSettings, SaveSettings } from '~/redux/actions/settings-actions';

interface SettingState {
    clientOnboarding: any,
    staffOnboarding: any,
}

const initialState : SettingState = {
    clientOnboarding: {},
    staffOnboarding: {},
};

const settingsSlice = createSlice({
    name: 'settingsSlice',
    initialState,
    reducers: {},
    extraReducers(builder) {
        builder.addCase(SaveSettings.fulfilled, (state, action) => {});
        builder.addCase(GetSettings.fulfilled, (state, { payload }) => {
            // console.log('Payload--------------', payload);
            state.clientOnboarding = payload?.data?.data?.clientOnboarding;
            state.staffOnboarding = payload?.data?.data?.staffOnboarding;
        });
    },
});

export default settingsSlice.reducer;

import { createSlice } from '@reduxjs/toolkit';
import {
    __ManipulateStaffList,
    GetStaffList,
    GetStaffProfileDetails,
    UpdateStaffStatus,
} from '~/redux/actions/staff-action';

const initialState = {
    staff: [],
    personalInfo: {
        profilePicture: '',
        role: '',
        firstName: '',
        email: '',
        mobile: '',
        gender: '',
        lastName: '',
        dateOfBirth: '',
        address: {
            street: '',
            city: '',
            state: '',
            country: '',
        },
    },
    additionalInfo: {
        staffId: '',
        userId: '',
        experience: '',
        experienceYears: '',
        experirenceMonths: '',
    },
    facilityInfo: {},
    staffList: [],
    staffListForDropdown: [],
    staffListCount: 0,
};

const staffSlice = createSlice({
    name: 'staffSlice',
    initialState: initialState,
    reducers: {
        ClearStaffFields: () => {
            return initialState;
        },
    },
    extraReducers(builder) {
        builder.addCase(GetStaffList.fulfilled, (state, action) => {
            const staffList = __ManipulateStaffList(
                action.payload.res.data.data
            );
            if (action.payload.forDropdown)
                state.staffListForDropdown = staffList;
            else state.staffList = staffList;
            state.staffListCount =
                action.payload.res.data.pagination.totalCount;
        });
        builder.addCase(GetStaffProfileDetails.fulfilled, (state, action) => {
            state.personalInfo = action.payload.data.data.personalInfo;
            state.additionalInfo = action.payload.data.data.additionalInfo;
            state.facilityInfo = action.payload.data.data.facilityInfo;
        });
        builder.addCase(UpdateStaffStatus.fulfilled, (state: any, action) => {
            state.staffList = state.staffList.map((staff: any) => {
                if (staff.userId === action.payload.data?._id)
                    staff.status = action.payload.data.isActive;
                return staff;
            });
        });
    },
});

export const { ClearStaffFields } = staffSlice.actions;

export default staffSlice.reducer;

import { Button, Form, Input, Modal, Select } from 'antd';
import React, { useEffect } from 'react';
import { RoleType, Staff_Roles } from '~/types/enums';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    AddedPaymentMethodList,
    AddPaymentMethod,
    AllPaymentMethodList,
} from '~/redux/actions/payment-method.action';
import { useLoader } from '~/hooks/useLoader';

const { Option } = Select;

interface AddPaymentModalProps {
    visible: boolean;
    onClose: () => void;
    facilityList: any[];
    selectedFacilityId: any;
}

const AddPaymentModal: React.FC<AddPaymentModalProps> = ({
    visible,
    onClose,
    facilityList,
    selectedFacilityId,
}) => {
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        paymentMethodList: state.paymentmethod_store.paymentMethodsList,
    }));

    const handleSubmit = async (values: any) => {
        startLoader();
        const payload = {
            ...values,
        };
        try {
            await dispatch(AddPaymentMethod(payload)).unwrap();
            dispatch(
                AddedPaymentMethodList({
                    facilityId: selectedFacilityId,
                })
            );
            onClose();
        } catch (error) {
            console.error('Error submitting form:', error);
        } finally {
            endLoader();
        }
    };

    const handleFacilityChange = (value: string) => {
        dispatch(
            AllPaymentMethodList({
                facilityId: value,
            })
        ).unwrap();
    };
    return (
        <Modal
            title={
                <p className="w-fit border-b-2 border-primary text-2xl text-[#1A3353]">
                    Add New
                </p>
            }
            className="lg:w-[35%]"
            footer={null}
            open={visible}
            onCancel={onClose}
        >
            <Form
                layout="vertical"
                size="normal"
                autoComplete="off"
                onFinish={handleSubmit}
                initialValues={{ facilityId: selectedFacilityId }}
            >
                <Form.Item
                    label="Branch Name"
                    name="facilityId"
                    rules={[
                        {
                            required: true,
                            message: 'Please select branch name(s)',
                        },
                    ]}
                >
                    <Select
                        placeholder="Select the branch name"
                        onChange={handleFacilityChange}
                    >
                        {facilityList?.map((facility: any) => (
                            <Option key={facility._id} value={facility._id}>
                                {facility.facilityName}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item
                    label="Payment Method"
                    name="paymentMethodId"
                    className="pt-5"
                    rules={[
                        {
                            required: true,
                            message: 'Please enter payment method',
                        },
                    ]}
                >
                    <Select placeholder="Select the branch name">
                        {store?.paymentMethodList?.map((pay: any) => (
                            <Option key={pay._id} value={pay._id}>
                                {pay.name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                <div className="flex items-end justify-end gap-5  lg:flex-row">
                    <Form.Item>
                        <Button
                            // type="default "

                            className="h-12 w-32 border-1 border-[#1A3353] text-xl  text-[#1A3353]"
                            onClick={onClose}
                        >
                            Cancel
                        </Button>
                    </Form.Item>
                    <Form.Item>
                        <Button
                            type="primary"
                            className="h-12 w-32 bg-purpleLight text-xl  "
                            htmlType="submit"
                            disabled={loader}
                        >
                            Save
                        </Button>
                    </Form.Item>
                </div>
            </Form>
        </Modal>
    );
};

export default AddPaymentModal;

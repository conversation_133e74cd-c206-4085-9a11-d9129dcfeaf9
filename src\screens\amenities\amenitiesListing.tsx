import { Pagination } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { capitalizeFirstLetter } from '~/components/common/function';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { AmenitiesList, DeleteAmenity } from '~/redux/actions/amenities-action';
import { setSearchValue } from '~/redux/slices/common-slice';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';

const columns = [
    {
        title: 'Amenities Name',
        dataIndex: 'name',
        // width: "25%",
    },
    {
        title: 'Amenities Type',
        dataIndex: '',
        render: (record: any) => {
            // console.log("Record--------", record);
            return <div>{capitalizeFirstLetter(record.amenityType)}</div>;
        },
    },
];

const AmenitiesListing: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();

    const { amenitiesList, amenitiesListCount } = useSelector(
        (state: any) => state.amenity_store
    );

    const params = getQueryParams();
    const [loader, startLoader, endLoader] = useLoader();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const searchValue = useSelector(
        (state: any) => state.common_store.searchValue
    );

    const debouncedRequest = useDebounce((callback) => callback(), 300);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    function deleteSubAttribute(id: any) {
        dispatch(DeleteAmenity({ id })).unwrap();
    }

    useEffect(() => {
        startLoader();
        if (searchParam) {
            debouncedRequest(() => {
                dispatch(
                    AmenitiesList({
                        page: currentPage,
                        pageSize: pageSizes,
                        search: searchParam,
                    })
                )
                    .unwrap()
                    .then(() => {})
                    .finally(endLoader);
            });
        } else {
            dispatch(AmenitiesList({ page: currentPage, pageSize: pageSizes }))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [currentPage, pageSizes, searchParam]);

    const selectColumn = {
        title: 'Action',
        dataIndex: '',
        width: '90px',
        key: 'action',
        render: (record: any) => {
            console.log('Record: ', record);
            return (
                <>
                    <span className="flex gap-2">
                        <Link
                            to={`/amenities/create-amenities/${record?._id}`}
                            // onClick={() => selectAttribute(record)}
                        >
                            <img
                                src="/icons/common/edit.svg"
                                alt="edit"
                                className="h-[20px] cursor-pointer"
                            />
                        </Link>
                        <div
                            className="cursor-pointer"
                            onClick={() => deleteSubAttribute(record._id)}
                        >
                            <img
                                src="/icons/common/delete.svg"
                                alt="delete"
                                className="h-[20px]"
                            />
                        </div>
                    </span>
                </>
            );
        },
    };

    const combinedColumns = [...columns, selectColumn];

    const handleSearch = (value: string) => {
        dispatch(setSearchValue(value));
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    };

    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={combinedColumns}
                dataSource={amenitiesList}
                addNewLink="/amenities/create-amenities/0"
                bulkAction={false}
                addNewTitle="Create Amenities"
                heading="Amenities"
                onSearch={handleSearch}
                showSearch={true}
                search={searchParam}
                backButton={true}
            />
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={amenitiesListCount}
                    pageSize={pageSizes}
                    pageSizeOptions={['10', '20', '50']} 
                    onChange={paginate}
                    // hideOnSinglePage
                />
            </div>
        </>
    );
};

export default AmenitiesListing;

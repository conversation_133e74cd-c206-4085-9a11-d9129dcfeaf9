import {
    But<PERSON>,
    Col,
    ConfigProvider,
    Form,
    Input,
    Row,
    FormProps,
    Typography,
    Select,
} from 'antd';
import React, { useEffect, useState } from 'react';
import 'react-quill/dist/quill.snow.css';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import FullLoader from '~/components/library/loader/full-loader';
import { useLoader } from '~/hooks/useLoader';
import {
    AmenityDetails,
    CreateAmenitiesData,
    UpdateAmenitiesData,
} from '~/redux/actions/amenities-action';
import { AppDispatch } from '~/redux/store';

const { Title } = Typography;
function goBack() {
    window.history.back();
}

const CreateAmenities: React.FC = () => {
    // For banner upload
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [loader, startLoader, endLoader] = useLoader();
    const { amenitiesDetails } = useSelector(
        (state: any) => state.amenity_store
    );

    const [location, setLocation] = useLocation();
    const amenityId = location.split('/').pop();

    useEffect(() => {
        if (amenitiesDetails && amenityId !== '0') {
            const { name, amenityType } = amenitiesDetails;
            console.log('amenitiesDetails---------', amenitiesDetails);
            form.setFieldsValue({
                name,
                amenityType,
            });
        }
    }, [amenitiesDetails]);

    useEffect(() => {
        if (amenityId !== '0') {
            startLoader();
            dispatch(AmenityDetails({ amenityId: amenityId }))
                .unwrap()
                .finally(endLoader);
        }
    }, [amenityId]);

    const onFinish: FormProps['onFinish'] = (values) => {
        console.log('Success:', values);
        startLoader();
        const payload = {
            name: values.name,
            amenityType: values.amenityType,
        };
        // console.log('Data---------', payload);

        if (amenityId === '0') {
            dispatch(CreateAmenitiesData(payload))
                .unwrap()
                .then((res: any) => {
                    console.log('Res----------', res);
                    if (res?.status === 200 || res?.status === 201) {
                        setLocation('/amenities');
                    }
                })
                .finally(endLoader);
        } else if (amenityId !== '0' || undefined || null) {
            dispatch(
                UpdateAmenitiesData({ reqData: payload, amenityId: amenityId })
            )
                .unwrap()
                .then((res: any) => {
                    console.log('Res----------', res);
                    if (res?.status === 200 || res?.status === 201) {
                        setLocation('/amenities');
                    }
                })
                .finally(endLoader);
        }
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                },
            }}
        >
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <>
                    <div className="flex items-center gap-4">
                        <img
                            src="/icons/back.svg"
                            alt="edit"
                            className="h-[10px] cursor-pointer"
                            onClick={goBack}
                        />
                        <Title className="text-[#1A3353]" level={4}>
                            {amenityId === '0'
                                ? `Create Amenities`
                                : 'Edit Amenities'}
                        </Title>
                    </div>
                    <div className="mt-16">
                        <div className="rounded-3xl border lg:w-[80%] lg:p-16 @sm:p-5">
                            <Form
                                name="amenitiesCreate"
                                layout="vertical"
                                size="large"
                                form={form}
                                initialValues={{ remember: true }}
                                onFinish={onFinish}
                                autoComplete="off"
                            >
                                <Form.Item
                                    label="Amenity Type"
                                    name="amenityType"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select amenity type!',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        placeholder="Select Type"
                                        filterOption={(input, option) =>
                                            (option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={[
                                            {
                                                label: 'GENERAL',
                                                value: 'general',
                                            },
                                            {
                                                label: 'PARKING & TRANSPORT',
                                                value: 'parking & transport',
                                            },
                                            // {
                                            //     label: 'SUSTAINABILITY',
                                            //     value: 'sustainability',
                                            // },
                                            {
                                                label: 'FAMILY',
                                                value: 'family',
                                            },
                                            // {
                                            //     label: 'AQUATICS',
                                            //     value: 'aquatics',
                                            // },
                                        ]}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label="Amenity Name"
                                    name="name"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter amenity name!',
                                        },
                                    ]}
                                >
                                    <Input
                                        maxLength={50}
                                        placeholder="Enter Amenity Name"
                                    />
                                </Form.Item>

                                <Form.Item className="mt-8 flex justify-end">
                                    <Button
                                        htmlType="submit"
                                        loading={loader}
                                        className="bg-purpleLight text-white"
                                    >
                                        Submit
                                    </Button>
                                </Form.Item>
                            </Form>
                        </div>
                    </div>
                </>
            )}
        </ConfigProvider>
    );
};

export default CreateAmenities;

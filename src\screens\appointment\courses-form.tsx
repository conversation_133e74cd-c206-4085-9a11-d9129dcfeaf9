import React, { useEffect, useState } from 'react';
import {
    Button,
    ConfigProvider,
    DatePicker,
    Form,
    FormProps,
    Input,
    Radio,
    Select,
    Switch,
    TimePicker,
} from 'antd';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { ClassType, RoleType } from '~/types/enums';
import {
    FacilitiesList,
    GetFacilityListByStaffId,
} from '~/redux/actions/facility-action';
import {
    activeServiceCategoyListPricing,
    activeServiceCategoyListv1,
    ServiceCategoryList,
} from '~/redux/actions/serviceCategoryAction';
import {
    PricingListingByUserAndSubType,
    StaffAvailabilityListV1,
} from '~/redux/actions/appointment-action';
import { capitalizeFirstLetter } from '~/components/common/function';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useLocation } from 'wouter';
import { roomListingByScheduling } from '~/redux/actions/room-action';
import {
    createCoursesScheduling,
    updateCoursesScheduling,
} from '~/redux/actions/courses-action';
import { useLoader } from '~/hooks/useLoader';
import debounce from 'lodash.debounce';
import WeeklySchedule from '~/components/staff/weeklySchedule';

const { TextArea } = Input;
const { Option, OptGroup } = Select;

dayjs.extend(utc);
dayjs.extend(timezone);

interface CoursesFormProps {
    onClose?: any;
    title?: string;
    scheduleId?: string;
    isEdit?: boolean;
    facilityId?: string;
    slotSelectedInfo?: any;
    courseId?: string;
}

interface TimeSlot {
    from: Dayjs | null;
    to: Dayjs | null;
}

interface WorkingHours {
    [key: string]: TimeSlot[];
}

const CoursesForm: React.FC<CoursesFormProps> = ({
    onClose,
    scheduleId,
    courseId,
    isEdit,
    facilityId,
}) => {
    const [value, setValue] = useState<string>('single');
    const [_, setLocation] = useLocation();
    const [selectedDay, setSelectedDay] = useState<string>('mon');
    const [workingHours, setWorkingHours] = useState<WorkingHours>({
        mon: [],
        tue: [],
        wed: [],
        thu: [],
        fri: [],
        sat: [],
        sun: [],
    });
    const daysOfWeek = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

    const { role, user, organizationId } = useSelector(
        (state: any) => state.auth_store
    );
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    const [selectedLocation, setSelectedLocation] = useState(
        facilityId ? facilityId : null
    );
    const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(dayjs());
    const [startTime, setStartTime] = useState<dayjs.Dayjs | null>(null);
    const [endTime, setEndTime] = useState<dayjs.Dayjs | null>(null);
    const [startTimeMinuteStep, setStartTimeMinuteStep] = useState<any>(1);
    const [durationOption, setDurationOption] = useState<any>([]);
    const [serviceCategoryId, setServiceCategoryId] = useState<any>(null);
    const [subCategoryId, setSubcategoryId] = useState<any>(null);
    const [staffOption, setStaffOption] = useState<any>(null);
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [roomData, setRoomData] = useState<any>([]);
    const [dateRange, setDateRange] = useState<'Single' | 'Multiple'>('Single');
    const [isChecked, setIsChecked] = useState(false);

    const store = useAppSelector((state) => ({
        roomList: state.room_store.roomList,
        facilityList: state.facility_store.facilityList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
        trainerList: state.appointment_store.trainerList,
        ServiceCategoryListData:
            state.service_category_store.ServiceCategoryListData,
        ActiveServiceCategoryListV1:
            state.service_category_store.ActiveServiceCategoryListPricing,
        courseSchedulingDetails: state.course_store.courseSchedulingDetails,
    }));

    console.log('store--------------', store.courseSchedulingDetails);

    const transformServiceData = (apiData: any) => {
        return apiData.map((service: any) => ({
            category: service.name,
            options: service.appointmentType.map((appointment: any) => ({
                label: `${appointment.name} - ${appointment.durationInMinutes} mins`,
                value: JSON.stringify({
                    serviceId: service._id,
                    appointmentId: appointment._id,
                    duration: appointment.durationInMinutes,
                }),
            })),
        }));
    };

    useEffect(() => {
        if (!scheduleId) {
            const currentLocation = form.getFieldValue('location');
            form.resetFields();
            form.setFieldValue('location', currentLocation);
            setSelectedLocation(currentLocation);
        }
    }, [scheduleId, isEdit]);

    const serviceOptions = transformServiceData(
        store.ActiveServiceCategoryListV1
    );

    useEffect(() => {
        if (courseId) {
            dispatch(
                activeServiceCategoyListPricing({
                    packageId: courseId,
                })
            );
        }
    }, [courseId, scheduleId, isEdit]);

    useEffect(() => {
        if (scheduleId && store.courseSchedulingDetails && form) {
            const data = store.courseSchedulingDetails;
            console.log('UseEffect is triggered');

            const serviceOptions = transformServiceData(
                store.ActiveServiceCategoryListV1
            );

            const selectedServiceValue = serviceOptions
                .flatMap((group: any) => group.options)
                .find((option: any) => {
                    const parsed = JSON.parse(option.value);
                    return (
                        parsed.serviceId === data.serviceCategoryId &&
                        parsed.appointmentId === data.subTypeId
                    );
                })?.value;

            form.setFieldsValue({
                location: data.facilityId,
                serviceType: selectedServiceValue,
                startDate: dayjs(data.date).startOf('day'),
                staff: data.trainerId,
                room: data.roomId,
                duration: data.duration,
                classCapacity: data.classCapacity?.toString(),
                Notes: data.notes,
            });

            setSelectedLocation(data.facilityId);
            setServiceCategoryId(data.serviceCategoryId);
            setSubcategoryId(data.subTypeId);
            setStartTime(dayjs(`${data.date?.split('T')[0]}T${data.from}`));
            setEndTime(dayjs(`${data.date?.split('T')[0]}T${data.to}`));
            setStartTimeMinuteStep(data.duration);
        }
    }, [store.courseSchedulingDetails, scheduleId, isEdit, form]);

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const handleFormClose = () => {
        form.resetFields();
        onClose();
        setLocation('');
        setServiceCategoryId('');
    };

    // useEffect(() => {
    //     if (scheduleId) {
    //         dispatch(getCoursesSchedulingDetails({ schedulingId: scheduleId }));
    //     }
    // }, [scheduleId]);

    useEffect(() => {
        if (role === RoleType.TRAINER || role === RoleType.WEBMASTER) {
            dispatch(GetFacilityListByStaffId({ staffId: user._id }));
        } else {
            dispatch(FacilitiesList({ page: 1, pageSize: 10 })).unwrap();
        }
    }, []);

    useEffect(() => {
        // startLoaderClass();
        if (facilityId) form.setFieldValue('location', facilityId);
        dispatch(
            ServiceCategoryList({
                page: 1,
                pageSize: 10,
                classType: ClassType.COURSES,
            })
        )
            .unwrap()
            .then(() => {})
            .catch(() => {});
        // .finally(endLoaderClass);
    }, []);

    const onDateChange = (date: Dayjs | null) => {
        if (!date) return;

        const day = dayjs(date).format('ddd').toLowerCase();
        const isToday = dayjs().isSame(date, 'day');

        const newTimeSlot: TimeSlot = {
            from: null,
            to: null,
        };
        setSelectedDay(day);

        setWorkingHours((prev) => {
            const updatedHours = { ...prev };

            // if (value === 'single') {
            // if (isToday) {
            //     updatedHours.thu = [newTimeSlot]; // Add to "thu" for today
            // } else {
            updatedHours[day] = [newTimeSlot]; // Add to specific day
            // }
            // }

            return updatedHours;
        });
    };

    const handleStartTimeChange = (time: Dayjs | null) => {
        setStartTime(time);

        const duration =
            form.getFieldValue('duration') || startTimeMinuteStep || 30;

        if (time && duration) {
            const newEnd = dayjs(time).add(duration, 'minute');
            setEndTime(newEnd);
        }
    };

    const getNextTimeSlot = (step: number) => {
        const now = dayjs(startDate);
        const minutes = now.minute();
        const nextMinutes = Math.ceil(minutes / step) * step;
        return now.minute(nextMinutes).second(0).millisecond(0);
    };

    const handleServiceTypeChange = async (value: string) => {
        form.resetFields(['roomId', 'staff']);
        const selectedOption = JSON.parse(value);

        const serviceId = selectedOption.serviceId;
        const appointmentId = selectedOption.appointmentId;
        const duration = selectedOption.duration;

        if (!serviceId || !appointmentId) return;

        setServiceCategoryId(serviceId);
        setSubcategoryId(appointmentId);

        const service = store.ActiveServiceCategoryListV1.find(
            (item: any) => item._id === serviceId
        ) || { appointmentType: [] };

        const appointmentType: any = service.appointmentType?.find(
            (item: any) => item._id === appointmentId
        );

        // Step 1: Auto-set duration and time slots
        const fallbackStep = appointmentType?.durationInMinutes || 30;
        const autoStart = getNextTimeSlot(fallbackStep);
        const autoEnd = dayjs(autoStart).add(fallbackStep, 'minute');

        setStartTime(autoStart);
        setEndTime(autoEnd);
        setStartTimeMinuteStep(fallbackStep);

        form.setFieldsValue({
            duration: fallbackStep,
        });

        // Step 2: Update duration options
        const durationOptions = Array.from({ length: 5 }, (_, i) => {
            const multiple = fallbackStep * (i + 1);
            return { label: `${multiple} Minutes`, value: multiple };
        });
        setDurationOption(durationOptions);
    };

    const fetchAvailableRooms = debounce((params) => {
        dispatch(roomListingByScheduling(params))
            .unwrap()
            .then((res: any) => {
                const roomOptions = res?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item.roomName,
                    id: item._id,
                }));
                setRoomData(roomOptions);
                form.setFieldsValue({
                    roomId: roomOptions?.[0]?.value,
                });
            });
    }, 300);

    useEffect(() => {
        const canCallAPI =
            serviceCategoryId &&
            (selectedLocation || facilityId) &&
            startTime &&
            endTime &&
            startDate;

        console.log(
            'payload before api call -----------',
            serviceCategoryId,
            selectedLocation || facilityId,
            startTime,
            endTime,
            startDate
        );

        if (canCallAPI) {
            fetchAvailableRooms({
                serviceId: serviceCategoryId,
                facilityId: selectedLocation || facilityId,
                classType: ClassType.COURSES,
                date: dayjs(startDate).format('YYYY-MM-DD'),
                startTime: dayjs(startTime).format('HH:mm'),
                endTime: dayjs(endTime).format('HH:mm'),
            });
        }
    }, [serviceCategoryId, selectedLocation, startTime, endTime, startDate]);

    // useEffect(() => {
    //     if (selectedLocation) {
    //         dispatch(
    //             TrainerListing({
    //                 facilityId: selectedLocation,
    //                 isActive: true,
    //             })
    //         )
    //             .unwrap()
    //             .then((res: any) => {
    //                 const staffOptions = res?.data?.data?.map((item: any) => ({
    //                     value: item.userId,
    //                     label: capitalizeFirstLetter(
    //                         `${item.firstName} ${item.lastName}`
    //                     ),
    //                 }));
    //                 setStaffOption(staffOptions || []);
    //             });
    //     }
    // }, [selectedLocation]);

    useEffect(() => {
        if (!scheduleId && store.ActiveServiceCategoryListV1?.length > 0) {
            const today = dayjs();

            // Get first service and first appointment
            const firstService: any = store.ActiveServiceCategoryListV1?.[0];
            const firstAppointment = firstService?.appointmentType?.[0];

            if (firstService && firstAppointment) {
                const fallbackStep = firstAppointment.durationInMinutes || 30;
                const autoStart = getNextTimeSlot(fallbackStep);
                const autoEnd = dayjs(autoStart).add(fallbackStep, 'minute');

                const serviceTypeValue = JSON.stringify({
                    serviceId: firstService._id,
                    appointmentId: firstAppointment._id,
                    duration: fallbackStep,
                });

                // Update state
                setServiceCategoryId(firstService._id);
                setSubcategoryId(firstAppointment._id);
                setStartTime(autoStart);
                setEndTime(autoEnd);
                setStartTimeMinuteStep(fallbackStep);

                // Duration dropdown options
                const durationOptions = Array.from({ length: 5 }, (_, i) => {
                    const multiple = fallbackStep * (i + 1);
                    return { label: `${multiple} Minutes`, value: multiple };
                });
                setDurationOption(durationOptions);

                form.setFieldsValue({
                    startDate: today,
                    serviceType: serviceTypeValue,
                    duration: fallbackStep,
                });

                onDateChange(today); // update selectedDay and workingHours
            }
        }
    }, [store.ActiveServiceCategoryListV1, scheduleId]);

    useEffect(() => {
        if (!scheduleId && staffOption?.length) {
            form.setFieldValue('staff', staffOption[0].value);
        }
        if (!scheduleId && roomData?.length) {
            form.setFieldValue('room', roomData[0].value);
        }
    }, [staffOption, scheduleId, roomData]);

    useEffect(() => {
        const fetchAvailableStaff = async () => {
            if (
                (selectedLocation || facilityId) &&
                serviceCategoryId &&
                subCategoryId &&
                startDate &&
                startTime &&
                endTime
            ) {
                const reqData = {
                    facilityId: selectedLocation || facilityId,
                    classType: ClassType.COURSES,
                    serviceId: serviceCategoryId,
                    subTypeId: subCategoryId,
                    date: dayjs(startDate).format('YYYY-MM-DDT00:00:00[Z]'),
                    startTime: dayjs(startTime).format('HH:mm'),
                    endTime: dayjs(endTime).format('HH:mm'),
                };

                try {
                    const res: any = await dispatch(
                        StaffAvailabilityListV1({ reqData })
                    ).unwrap();

                    const staffOptions = res?.data?.data?.map((item: any) => ({
                        value: item._id,
                        label: capitalizeFirstLetter(
                            `${item.firstName} ${item.lastName}`
                        ),
                    }));

                    setStaffOption(staffOptions || []);
                } catch (error) {
                    console.error('Error fetching available staff:', error);
                    setStaffOption([]);
                }
            }
        };

        fetchAvailableStaff();
    }, [
        selectedLocation,
        serviceCategoryId,
        subCategoryId,
        startDate,
        startTime,
        endTime,
    ]);

    const roomOptions = store.roomList?.map((item: any) => ({
        value: item._id,
        label: item.roomName,
        id: item._id,
    }));

    const onFinish: FormProps['onFinish'] = (values: any) => {
        console.log('Received values of form: ', values);
        startSubmitLoader();
        const payload: any = {
            organizationId: organizationId,
            facilityId: values.location,
            trainerId: values.staff,
            courseId: scheduleId
                ? store.courseSchedulingDetails?.packageId
                : courseId,
            classType: ClassType.COURSES,
            subType: subCategoryId,
            serviceCategory: serviceCategoryId,
            duration: values.duration,
            roomId: values.room,
            dateRange: value === 'single' ? 'Single' : 'Multiple',
            date: dayjs(values.startDate)
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
            from: startTime?.format('HH:mm') || '',
            to: endTime?.format('HH:mm') || '',
            notes: values.Notes || '',
            classCapacity: parseInt(values.classCapacity, 10),
        };
        console.log('Payload------------------------', payload);

        if (isEdit && scheduleId) {
            payload.schedulingId = scheduleId;
            dispatch(updateCoursesScheduling({ payload: payload }))
                .then((res: any) => {
                    console.log('Res---------------', res);
                    if (
                        res?.payload?.status === 200 ||
                        res?.payload?.status === 201
                    ) {
                        handleFormClose();
                        // setLocation('/classes-listing');
                    }
                })
                .finally(endSubmitLoader);
        } else {
            dispatch(createCoursesScheduling({ payload: payload }))
                .then((res: any) => {
                    console.log('Res---------------', res);
                    if (
                        res?.payload?.status === 200 ||
                        res?.payload?.status === 201
                    ) {
                        handleFormClose();
                        // setLocation('/classes-listing');
                    }
                })
                .finally(endSubmitLoader);
        }
    };

    const handleDayClick = (day: string) => {
        setSelectedDay(day);
    };
    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        verticalLabelMargin: '-10px',
                    },
                },
            }}
        >
            <Form
                layout="horizontal"
                className=" flex flex-col  "
                name="book-classes"
                size="large"
                variant="borderless"
                form={form}
                disabled={!isEdit && !!scheduleId}
                onFinish={onFinish}
                initialValues={
                    {
                        // startDate: dayjs(new Date()).format('DD/MM/YYYY'),
                    }
                }
            >
                <div className="flex flex-row items-start">
                    <div className="w-[40%] ">
                        <div className="flex w-[100%] justify-between  ">
                            <div className="  lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left  lg:w-[55px]">
                                            Location
                                        </p>
                                    }
                                    name="location"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select location',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        className="border-b-1"
                                        allowClear
                                        value={selectedLocation}
                                        onChange={(value) => {
                                            setSelectedLocation(value);
                                        }}
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={
                                            role === RoleType.TRAINER
                                                ? LocationOptionByStaff
                                                : FacilityOptions
                                        }
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className=" flex w-[100%] justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left @sm:w-full">
                                            Service
                                        </p>
                                    }
                                    name="serviceType"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select a service',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b-1 "
                                        showSearch
                                        placeholder="Select Service"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={handleServiceTypeChange}
                                    >
                                        {serviceOptions.map((group: any) => (
                                            <OptGroup
                                                key={group.category}
                                                label={group.category}
                                            >
                                                {group.options.map(
                                                    (service: any) => (
                                                        <Option
                                                            key={service.value}
                                                            value={
                                                                service.value
                                                            }
                                                        >
                                                            {service.label}
                                                        </Option>
                                                    )
                                                )}
                                            </OptGroup>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>
                        <div className=" flex justify-between lg:mt-5 lg:w-[95%] lg:flex-row lg:items-center @sm:mt-7 @sm:flex-col">
                            <Form.Item
                                className="w-full"
                                label={
                                    <p className="text-start">
                                        Class
                                        <span className="text-2xl text-red-400">
                                            *
                                        </span>
                                        <br />
                                        Capacity
                                    </p>
                                }
                                name="classCapacity"
                            >
                                <Input
                                    type="text"
                                    className="border-b"
                                    placeholder="Enter Class Capacity"
                                    style={{
                                        borderBottom: '1px solid #e5e7eb',
                                        borderRadius: '0px',
                                    }}
                                />
                            </Form.Item>
                        </div>
                    </div>
                    <div className="w-[60%] border-l-2 ps-9 ">
                        <div className="mb-8 flex w-[100%] justify-between">
                            <div className="flex items-center ps-2 lg:w-[100%]">
                                <p className="text-[13px] font-medium text-[#1A3353] lg:w-[20%]">
                                    Date Range
                                </p>
                                <Radio.Group
                                    value={dateRange}
                                    onChange={(e) =>
                                        setDateRange(e.target.value)
                                    }
                                >
                                    <Radio
                                        className="text-[#455560]"
                                        value="Single"
                                    >
                                        Single
                                    </Radio>
                                    <Radio
                                        className="text-[#455560]"
                                        value="Multiple"
                                    >
                                        Multiple
                                    </Radio>
                                </Radio.Group>
                            </div>
                        </div>

                        {dateRange === 'Single' ? (
                            <div>
                                {' '}
                                <div className="flex w-[100%] flex-row justify-between ">
                                    <div className=" lg:w-[100%] @sm:w-full ">
                                        <Form.Item
                                            label="Select Date"
                                            name="startDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please select start date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                onChange={onDateChange}
                                                format="DD/MM/YYYY"
                                                className="w-[95%]"
                                                style={{
                                                    borderBottom:
                                                        '0.5px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                }}
                                            />
                                        </Form.Item>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <>
                                <div className="flex w-[100%] flex-col items-center gap-5">
                                    <div className="lg:w-[100%] @sm:w-full">
                                        <Form.Item
                                            label="Start Date"
                                            name="startDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter start date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                placeholder="DD/MM/YYYY"
                                                format="DD/MM/YYYY"
                                                className="w-[95%]"
                                                // style={{ width: '100%' }}
                                                style={{
                                                    borderBottom:
                                                        '0.5px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                }}
                                            />
                                        </Form.Item>
                                    </div>
                                    <div className="lg:w-[100%] @sm:w-full">
                                        <Form.Item
                                            label="End Date"
                                            name="endDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter end date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                placeholder="DD/MM/YYYY"
                                                format="DD/MM/YYYY"
                                                // style={{ width: '100%' }}

                                                className="w-[95%]"
                                                // style={{ width: '100%' }}
                                                style={{
                                                    borderBottom:
                                                        '0.5px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                }}
                                            />
                                        </Form.Item>
                                    </div>
                                </div>
                                <div className="flex flex-row items-center justify-between  pb-9 text-[13px] font-medium text-[#1A3353]">
                                    Select Days
                                    <div className=" flex flex-wrap lg:w-[80%] lg:gap-3  @xl:gap-8">
                                        {daysOfWeek?.map((day) => (
                                            <div className="">
                                                <Button
                                                    shape="circle"
                                                    className={`p-2   ${
                                                        selectedDay?.includes(
                                                            day
                                                        )
                                                            ? 'bg-[#455560] text-white'
                                                            : 'bg-white'
                                                    }`}
                                                    onClick={() =>
                                                        handleDayClick(day)
                                                    }
                                                >
                                                    {capitalizeFirstLetter(
                                                        day.slice(0, 3)
                                                    )}
                                                </Button>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                <div className="mb-8 flex flex-row">
                                    <div className="w-[20%]"></div>
                                    <Switch
                                        checked={isChecked}
                                        onChange={() =>
                                            setIsChecked((prev) => !prev)
                                        }
                                    />
                                    <span className="ml-3 text-[14px] text-[#1A3353]">
                                        Duplicate for all days
                                    </span>
                                </div>
                            </>
                        )}

                        <div className="  ">
                            <div className=" flex flex-row items-center   ">
                                <div className="flex justify-between  lg:flex-row  lg:items-center @sm:flex-col @lg:w-[21%] @xl:w-[19%] @2xl:w-[19%]">
                                    <p className="  text-[13px] font-medium text-[#1A3353] lg:ms-2   lg:w-[65px] @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                        Start Time
                                    </p>
                                </div>
                                <div className="flex flex-row items-center @lg:w-[75%] @xl:w-[76%] @2xl:w-[76%]">
                                    <div className="w-[20%]">
                                        <TimePicker
                                            style={{
                                                borderBottom:
                                                    '1px solid #e5e7eb',
                                                borderRadius: '0px',
                                            }}
                                            format="HH:mm"
                                            value={startTime}
                                            minuteStep={5}
                                            // className="lg:w-[80%] @sm:w-[100%]"
                                            onChange={handleStartTimeChange}
                                        />
                                    </div>

                                    <div className=" w-[40%] lg:translate-y-3 @xl:translate-x-3  ">
                                        <Form.Item
                                            className="PA-form-duration-input"
                                            label={
                                                <p className=" text-left  @sm:w-full">
                                                    Duration
                                                </p>
                                            }
                                            name="duration"
                                            rules={[
                                                {
                                                    required: false,
                                                    message:
                                                        'Please select duration',
                                                },
                                            ]}
                                        >
                                            <Select
                                                showSearch
                                                className="ms-2 border-b-1"
                                                placeholder="Select Duration"
                                                filterOption={(input, option) =>
                                                    String(option?.label ?? '')
                                                        .toLowerCase()
                                                        .includes(
                                                            input.toLowerCase()
                                                        )
                                                }
                                                // onChange={handleDuration}
                                                onChange={(value) => {
                                                    const duration = value;
                                                    const currentStart =
                                                        startTime ||
                                                        getNextTimeSlot(
                                                            duration
                                                        );
                                                    const updatedEnd = dayjs(
                                                        currentStart
                                                    ).add(duration, 'minute');

                                                    setStartTime(currentStart);
                                                    setEndTime(updatedEnd);
                                                    form.setFieldValue(
                                                        'duration',
                                                        duration
                                                    );
                                                }}
                                                options={durationOption}
                                            />
                                        </Form.Item>
                                    </div>
                                    <div className=" flex w-[40%] justify-end lg:flex-row   lg:items-center @sm:flex-col @xl:gap-0 ">
                                        <p className="translate-x-3 font-medium text-[#1A3353] lg:w-[40%] @lg:text-[12px] @xl:w-[35%] @xl:text-[13px]">
                                            End Time
                                        </p>
                                        <TimePicker
                                            format="HH:mm"
                                            disabled
                                            value={endTime}
                                            style={{
                                                borderBottom:
                                                    '1px solid #e5e7eb',
                                                borderRadius: '0px',
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="flex w-[100%] justify-between pt-5 ">
                                <div className="lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left ">Staff</p>
                                        }
                                        name="staff"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select staff',
                                            },
                                        ]}
                                    >
                                        <Select
                                            className="border-b"
                                            showSearch
                                            // allowClear
                                            // defaultValue={selectedInstructor[0]}
                                            // value={selectedInstructor}
                                            // onChange={(value) =>
                                            //     setSelectedInstructor(value)
                                            // }
                                            // onSearch={(value) =>
                                            //     setFacilitySearch(value)
                                            // }
                                            placeholder="Select instructor"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={staffOption}
                                        />
                                    </Form.Item>
                                </div>
                            </div>

                            <div className="mt-5 flex w-[100%] justify-between ">
                                <div className="lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left ">Room</p>
                                        }
                                        name="room"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select room',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="Select Room"
                                            className="border-b"
                                            showSearch
                                            filterOption={(
                                                input: any,
                                                option: any
                                            ) =>
                                                (option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={roomData}
                                        />
                                    </Form.Item>
                                </div>
                            </div>

                            {/* <div className=" flex justify-between lg:mt-5 lg:w-[95%] lg:flex-row lg:items-center @sm:mt-7 @sm:flex-col">
                                <Form.Item
                                    className="w-full"
                                    label="Class Capacity"
                                    name="classCapacity"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please enter capacity',
                                        },
                                    ]}
                                >
                                    <Input
                                        type="text"
                                        className="border-b"
                                        placeholder="Enter Class Capacity"
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                        }}
                                    />
                                </Form.Item>
                            </div> */}

                            <div className=" flex justify-between lg:mt-5 lg:w-[95%] lg:flex-row lg:items-center @sm:flex-col">
                                <Form.Item
                                    className="w-full "
                                    label="Notes"
                                    name="Notes"
                                >
                                    <TextArea
                                        autoSize={{ minRows: 1, maxRows: 2 }}
                                        placeholder="Enter a Note"
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                        }}
                                    />
                                </Form.Item>
                            </div>

                            {(!scheduleId || (scheduleId && isEdit)) && (
                                <div className="flex flex-row gap-5 lg:justify-end @sm:justify-center">
                                    <Form.Item>
                                        <div
                                            className="mt-10"
                                            style={{ display: 'flex' }}
                                        >
                                            <Button
                                                onClick={handleFormClose}
                                                className="border border-[#1A3353] px-20 py-7 text-2xl"
                                            >
                                                Cancel
                                            </Button>
                                        </div>
                                    </Form.Item>
                                    <Form.Item>
                                        <div
                                            className="mt-10"
                                            style={{
                                                display: 'flex',
                                                gap: '10px',
                                            }}
                                        >
                                            <Button
                                                className="bg-purpleLight px-20 py-7 text-2xl"
                                                type="primary"
                                                htmlType="submit"
                                                loading={submitLoader}
                                            >
                                                Save
                                            </Button>
                                        </div>
                                    </Form.Item>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </Form>
        </ConfigProvider>
    );
};

export default CoursesForm;

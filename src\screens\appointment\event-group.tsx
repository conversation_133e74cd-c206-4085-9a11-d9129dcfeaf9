import React, { useState } from 'react';
import { Popover } from 'antd';
import dayjs from 'dayjs';

const EventGroup = ({ events, CustomTimeSlot }:any) => {
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);

  // Show first event directly
  const visibleEvent = events[0];
  const remainingCount = Math.max(0, events.length - 1);

  const popoverContent = (
    <div className="max-h-96 overflow-y-auto">
      {events.map((event:any, index:number) => (
        <div key={index} className="mb-4 border-b border-gray-200 pb-4 last:border-b-0">
          <div className="p-2">
            <div className="flex items-center justify-between">
              <div className="font-semibold">{event.title}</div>
              <div className="text-sm text-gray-500">
                {dayjs(event.start).format('HH:mm')} - {dayjs(event.end).format('HH:mm')}
              </div>
            </div>
            <div className="mt-1 text-sm text-gray-600">
              {event.name || 'Unnamed Event'}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="absolute inset-0 flex flex-col">
      {/* Main event display */}
      <div className="flex-1">
        <CustomTimeSlot event={visibleEvent}>
          <div className="h-full w-full p-1">
            <div className="text-sm font-semibold">{visibleEvent.title}</div>
            <div className="text-xs text-gray-600">
              {visibleEvent.name || 'Unnamed Event'}
            </div>
          </div>
        </CustomTimeSlot>
      </div>
      
      {/* "+X more" indicator */}
      {remainingCount > 0 && (
        <Popover
          content={popoverContent}
          title={`All Events (${events.length})`}
          trigger="click"
          open={isPopoverVisible}
          onOpenChange={setIsPopoverVisible}
          placement="bottom"
          overlayClassName="w-80"
        >
          <div 
            className="absolute bottom-0 left-0 cursor-pointer bg-white/80 px-1 py-0.5 text-xs text-blue-600 hover:text-blue-800"
            onClick={(e) => {
              e.stopPropagation();
              setIsPopoverVisible(true);
            }}
          >
            +{remainingCount} more at this time
          </div>
        </Popover>
      )}
    </div>
  );
};

export default EventGroup;
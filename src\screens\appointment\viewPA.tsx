import { DownOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    <PERSON><PERSON>,
    Col,
    ConfigProvider,
    DatePicker,
    Dropdown,
    Form,
    FormProps,
    Input,
    Menu,
    Row,
    Select,
    TimePicker,
    Typography,
    Upload,
} from 'antd';
import TextArea from 'antd/es/input/TextArea';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import { useSelector } from 'react-redux';
import { useLocation, useParams } from 'wouter';
import { capitalizeFirstLetter, goBack } from '~/components/common/function';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    BookedAppointmentDetails,
    TrainerListing,
    UpdateBookedAppointment,
} from '~/redux/actions/appointment-action';
import { UploadImage } from '~/redux/actions/common-action';
import { CustomerList } from '~/redux/actions/customer-action';
import {
    FacilitiesList,
    GetFacilityListByStaffId,
} from '~/redux/actions/facility-action';
import {
    ServiceCategoryList,
    ServiceCategoryListByPackageId,
} from '~/redux/actions/serviceCategoryAction';
import { RoleType } from '~/types/enums';
const { Title, Text, Paragraph } = Typography;

const ViewPA = () => {
    const [form] = Form.useForm();
    const [imageUrl, setImageUrl] = useState<string>();
    const [loading, setLoading] = useState(false);

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
        trainerListForForms: state.appointment_store.trainerListForForms,
        customerListForForms: state.customer_store.customerListForForms,
        ServiceCategoryListData:
            state.service_category_store.ServiceCategoryListData,
        bookedAppointmentDetails:
            state.appointment_store.bookedAppointmentDetails,
    }));

    const { role, user } = useSelector((state: any) => state.auth_store);
    const [selectedClient, setSelectedClient] = useState<any>(null);
    const [selectedLocation, setSelectedLocation] = useState<string>();
    const [appointmentTypeOption, setAppointmentTypeOption] = useState<any>([]);
    const dispatch = useAppDispatch();
    const location = useLocation();
    const isEdit = location[0].split('/')[1] === 'edit-appointment';
    const [loader, startLoader, endLoader] = useLoader();

    const handleImageUpload = (file: any) => {
        console.log('Uploaded file:', file);
        dispatch(UploadImage({ file: file.file })).then((res: any) => {
            console.log('Res------------------', res);
            setImageUrl(res?.payload?.res?.data?.data);
        });
    };
    const { id } = useParams();

    useEffect(() => {
        dispatch(
            ServiceCategoryList({
                page: 1,
                pageSize: 10,
                classType: 'personalAppointment',
            })
        );
    }, []);

    useEffect(() => {
        if (role === RoleType.TRAINER) {
            dispatch(GetFacilityListByStaffId({ staffId: user._id })).then(
                (res: any) => {
                    console.log('Res staffby facility---------------', res);
                }
            );
        } else dispatch(FacilitiesList({ page: 1, pageSize: 30 }));
    }, []);

    useEffect(() => {
        if (role !== RoleType.TRAINER && selectedLocation) {
            dispatch(
                TrainerListing({ facilityId: selectedLocation, isActive: true })
            );
        }
        if (selectedLocation) {
            startLoader();
            const requestParams = {
                page: 1,
                pageSize: 20,
            } as any;

            requestParams.locationId = [selectedLocation];
            dispatch(
                CustomerList({
                    ...requestParams,
                })
            )
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [selectedLocation]);

    const ClientOptions = store.customerListForForms?.map((item: any) => ({
        value: item.userId,
        label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
        id: item._id,
    }));

    const TrainerOptions = store.trainerListForForms?.map((item: any) => ({
        value: item.userId,
        label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
        id: item._id,
    }));

    const ServiceTypeOption = store.ServiceCategoryListData?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const handleClientChange = (clientId: string) => {
        const client = store.customerListForForms.find(
            (item: any) => item.userId === clientId
        );
        setSelectedClient(client);
    };

    const handleServiceTypeChange = (serviceId: string) => {
        const service = store.ServiceCategoryListData.find(
            (item: any) => item._id === serviceId
        ) || { appointmentType: [], noOfSessions: null };
        const appointmentType = service.appointmentType?.map((item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
            ...item,
        }));
        form.setFieldsValue({
            noOfSessions: service.noOfSessions || null,
        });
        setAppointmentTypeOption(appointmentType);
    };
    const handleAppointmentTypeChange = (appointmentId: string) => {
        const appointmentData = appointmentTypeOption.find(
            (item: any) => item._id === appointmentId
        );
        form.setFieldsValue({
            durationInMinutes: appointmentData.durationInMinutes,
        });
    };

    const handleStartTimeChange = (time: any) => {
        if (time) {
            const durationInMinutes =
                form.getFieldValue('durationInMinutes') || 0;
            const calculatedEndTime = dayjs(time).add(
                Number(durationInMinutes),
                'minute'
            );
            form.setFieldValue('endTime', calculatedEndTime);
        }
    };

    useEffect(() => {
        if (!id) return;

        const fetchAppointmentDetails = async () => {
            try {
                const res = await dispatch(BookedAppointmentDetails({ id }));
                const data = res.payload?.data?.data;
                if (data) {
                    dispatch(
                        ServiceCategoryListByPackageId({
                            packageId: data.packageId,
                        })
                    ).then((res: any) => {
                        const categories = res.payload?.data?.data;
                        const serviceCategory = categories?.find(
                            (item: any) => item._id === data.serviceId
                        );
                        const appointmentType =
                            serviceCategory?.appointmentType?.map(
                                (item: any) => ({
                                    value: item._id,
                                    label: item.name,
                                    id: item._id,
                                    ...item,
                                })
                            );
                        const durationInMinutes = appointmentType.find(
                            (item: any) => item._id === data.appointmentId
                        )?.durationInMinutes;
                        form.setFieldsValue({ durationInMinutes });
                        setAppointmentTypeOption(appointmentType);
                    });
                    setSelectedLocation(data.facilityId);
                    const formattedData = {
                        location: data.facilityId,
                        client: data.clientId,
                        instructor: data.trainerId,
                        serviceType: data.serviceId,
                        appointmentType: data.appointmentId,
                        durationInMinutes: data.durationInMinutes,
                        noOfSessions: data.noOfSessions,
                        startTime: dayjs(data.from, 'HH:mm'),
                        endTime: dayjs(data.to, 'HH:mm'),
                        date: dayjs(data.date, 'YYYY-MM-DD'),
                        notes: data.notes,
                    };
                    setSelectedClient({
                        userId: data.clientId,
                        email: data.clientEmail,
                        mobile: data.clientMobile,
                    });
                    form.setFieldsValue(formattedData);
                } else {
                    console.error('No data found in response', res);
                }
            } catch (error) {
                console.error('Error fetching appointment details:', error);
            }
        };

        fetchAppointmentDetails();
    }, [id]);
    console.log(form.getFieldsValue());

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );

    const onFinish: FormProps['onFinish'] = (values: any) => {
        console.log('Form Values:', values);

        const payload = {
            id: store.bookedAppointmentDetails._id,
            facilityId: values.location,
            trainerId: values.instructor,
            clientId: values.client,
            notes: values.notes,
            // packageId: store.bookedAppointmentDetails?.packageId,
            serviceId: values.serviceType,
            //    sendConfirmation:false,
            noOfSessions: Number(values.noOfSessions),
            appointmentId: values.appointmentType,
            duration: String(values.durationInMinutes),
            timeSlots: [
                {
                    date: dayjs(values.date)
                        .startOf('day')
                        .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                    from: dayjs(values.startTime).format('HH:mm'),
                    to: dayjs(values.endTime).format('HH:mm'),
                },
            ],
        };
        // console.log('Edit payload--------------', payload);

        dispatch(UpdateBookedAppointment({ payload: payload })).then(
            (res: any) => {
                console.log('Booking res----------', res);
            }
        );
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                },
                token: {
                    borderRadius: 4,
                },
            }}
        >
            <div className="flex items-center gap-4 lg:mb-16 @sm:mb-10 ">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1A3353]" level={4}>
                    {isEdit ? 'Edit' : 'View'} Appointment
                </Title>
            </div>
            <Row className="justify-around @sm:gap-10 ">
                <Col className="rounded-lg @sm:w-full" lg={15}>
                    <Form
                        name="gymCreate"
                        layout="vertical"
                        size="large"
                        form={form}
                        initialValues={{
                            amenities: [],
                        }}
                        onFinish={onFinish}
                        autoComplete="off"
                        disabled={!isEdit}
                    >
                        <div className="rounded-lg border p-5 lg:p-10">
                            <Form.Item
                                label={
                                    <p className="text-left  lg:w-[55px]">
                                        Location
                                    </p>
                                }
                                name="location"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select location',
                                    },
                                ]}
                            >
                                <Select
                                    showSearch
                                    allowClear
                                    onChange={(value) => {
                                        setSelectedLocation(value);
                                        form.setFieldsValue({
                                            staff: '',
                                            client: '',
                                        });
                                    }}
                                    placeholder="Select facility"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={
                                        role === RoleType.TRAINER
                                            ? LocationOptionByStaff
                                            : FacilityOptions
                                    }
                                />
                            </Form.Item>
                            <Form.Item
                                label="Client Name"
                                name="client"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select client name',
                                    },
                                ]}
                            >
                                <Select
                                    className=""
                                    showSearch
                                    placeholder="Select Client Name"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            ?.toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={ClientOptions}
                                    onChange={handleClientChange}
                                />
                            </Form.Item>
                            <Paragraph className="mb-0">
                                <Text>Phone:</Text>
                                <Text className="ml-3">
                                    {selectedClient?.mobile}
                                </Text>
                            </Paragraph>
                            <Paragraph className="mb-0">
                                <Text>Email:</Text>
                                <Text className="ml-3">
                                    {selectedClient?.email}
                                </Text>
                            </Paragraph>
                        </div>
                        <div className="mt-5 rounded-lg border p-5 lg:p-10">
                            <Form.Item
                                label="Instructor"
                                name="instructor"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select Instructor',
                                    },
                                ]}
                            >
                                <Select
                                    className=""
                                    showSearch
                                    placeholder="Select instructor"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={TrainerOptions}
                                />
                            </Form.Item>

                            <Form.Item
                                label={
                                    <p className=" text-left lg:w-[55px] @sm:w-full">
                                        Service Type
                                    </p>
                                }
                                name="serviceType"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select service type',
                                    },
                                ]}
                            >
                                <Select
                                    className=""
                                    showSearch
                                    placeholder="Select Service Type"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    onChange={handleServiceTypeChange}
                                    options={ServiceTypeOption}
                                />
                            </Form.Item>

                            <Form.Item
                                className="w-full"
                                label="Remaining number of sessions"
                                name="noOfSessions"
                            >
                                <Input
                                    type="text"
                                    disabled
                                    placeholder="Remaining number of sessions"
                                />
                            </Form.Item>

                            <Form.Item
                                label="Appointment Type"
                                name="appointmentType"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please select Appointment Type',
                                    },
                                ]}
                            >
                                <Select
                                    className=""
                                    showSearch
                                    placeholder="Select appointment type"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            ?.toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={appointmentTypeOption}
                                    onChange={handleAppointmentTypeChange}
                                />
                            </Form.Item>
                            <Form.Item label="Date" name="date">
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    className="w-full"
                                />
                            </Form.Item>
                            <Form.Item label="Start Time" name="startTime">
                                <TimePicker
                                    onChange={handleStartTimeChange}
                                    format="HH:mm"
                                    className="w-full"
                                    defaultOpenValue={dayjs('00:00', 'HH:mm')}
                                />
                            </Form.Item>
                            <Form.Item
                                className="w-full"
                                label="Duration In Minutes"
                                name="durationInMinutes"
                            >
                                <Input
                                    type="text"
                                    disabled
                                    placeholder="Duration In Minutes"
                                />
                            </Form.Item>
                            <Form.Item label="End Time" name="endTime">
                                <TimePicker
                                    disabled
                                    format="HH:mm"
                                    className="w-full"
                                    defaultOpenValue={dayjs('00:00', 'HH:mm')}
                                />
                            </Form.Item>
                            <Form.Item label="Notes" name="notes">
                                <TextArea />
                            </Form.Item>

                            {isEdit && (
                                <Form.Item className="mt-8 flex justify-end gap-8">
                                    <Button
                                        // loading={loader}
                                        onClick={() => goBack()}
                                        htmlType="button"
                                        className="w-[110px] border-[#1A3353] bg-[#fff]  text-black "
                                    >
                                        <p> Cancel </p>
                                    </Button>
                                    <Button
                                        // loading={loader}
                                        htmlType="submit"
                                        className="w-[110px] border-[#3E79F7] bg-[#3E79F7]  text-white lg:ms-7"
                                    >
                                        <p> Save </p>
                                    </Button>
                                </Form.Item>
                            )}
                        </div>
                    </Form>
                </Col>
                <Col
                    className="rounded-lg border lg:p-10 @sm:w-full @sm:p-5 "
                    lg={8}
                >
                    <div className="">
                        <Typography.Title level={5}>
                            <span className="text-[#3E79F7] ">
                                UPLOAD IMAGE
                            </span>
                        </Typography.Title>
                        <Upload
                            id="banner-upload"
                            name="avatar"
                            listType="picture-card"
                            className="avatar-uploader overflow-hidden lg:mt-8 @sm:mt-2"
                            showUploadList={false}
                            // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                            // beforeUpload={beforeUpload}
                            // onChange={handleChange}
                            customRequest={handleImageUpload}
                            disabled={!isEdit}
                        >
                            {imageUrl ? (
                                <img
                                    src={imageUrl}
                                    className="lg:object-contain @sm:rounded-3xl @sm:object-cover"
                                    alt="avatar"
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                    }}
                                />
                            ) : (
                                uploadButton
                            )}
                        </Upload>
                    </div>
                    <Form.Item className="mt-8 flex justify-center  lg:hidden ">
                        <Button
                            onClick={() => goBack()}
                            htmlType="button"
                            className="me-6 w-[110px] border-[#1A3353]  bg-[#fff]  text-[#1A3353] "
                        >
                            Cancel
                        </Button>
                        <Button
                            // loading={loader}
                            htmlType="submit"
                            className=" w-[110px]  bg-purpleLight  text-white"
                        >
                            Save
                        </Button>
                    </Form.Item>
                </Col>
            </Row>
        </ConfigProvider>
    );
};

export default ViewPA;

import { ConfigProvider, Pagination, Switch } from 'antd';
import clsx from 'clsx';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { capitalizeFirstLetter } from '~/components/common/function';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import {
    AttributeList,
    AttributeParentList,
} from '~/redux/actions/attribute-action';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';

const columns = [
    {
        title: 'ATTRIBUTE TYPE',
        dataIndex: '',
        render: (record: any) => {
            // console.log('Record--------', record);

            return (
                <Link
                    to={`${'/attribute-type-list'}?type=${
                        record.attributeName
                    }`}
                >
                    {capitalizeFirstLetter(record.attributeRealType)}
                </Link>
            );
        },
    },
    // {
    //     title: 'ATTRIBUTE NAME',
    //     dataIndex: 'name',
    //     // width: "25%",
    // },
];

const AttributeListing: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();
    const [loader, startLoader, endLoader] = useLoader();
    const { parentAttributeList } = useSelector(
        (state: any) => state.attribute_store
    );

    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);

    // const searchValue = useSelector((state:any)=>state.common_store.searchValue);

    // console.log('customerList------------', attributeList);

    // const params = getQueryParams();

    // const pageParam = Number(params.page);
    // const pageSizeParam = Number(params.pageSize);

    // const [currentPage, setCurrentPage] = useState(
    //     !isNaN(pageParam) ? pageParam : 1
    // );
    // const [pageSizes, setPageSize] = useState(
    //     !isNaN(pageSizeParam) ? pageSizeParam : 10
    // );
    // const debouncedRequest = useDebounce((callback) => callback(), 300);

    // function paginate(page: number, pageSize: number) {
    //     setCurrentPage(page);
    //     setPageSize(pageSize);
    //     navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    // }

    useEffect(() => {
        // if(searchValue){
        //     debouncedRequest(()=>{
        //         dispatch(AttributeParentList());
        //     })
        // }else{
        // }
        startLoader();
        dispatch(AttributeParentList())
            .unwrap()
            .then(() => {
                console.log('first');
            })
            .finally(endLoader);
    }, []);

    const handleStatusChange = (record: any) => {
        // setCurrentRecord(record);
        setConfirmationModalVisible(true);
    };

    const selectColumn = [
        {
            title: 'STATUS',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    // colorPrimaryBorder: '#8143d1',
                                    // colorPrimary: '#8143d1',
                                    colorTextQuaternary: 'gray',
                                    colorFillQuaternary: 'gray',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.status ? 'bg-switch-on' : 'bg-switch-off'
                            )}
                            id="swtich-off"
                            checkedChildren="ON"
                            // className="bg-[#D0D4D7]"
                            unCheckedChildren="OFF"
                            onChange={() => handleStatusChange(record)}
                            checked={record.status || false}
                        />
                    </ConfigProvider>
                );
            },
        },
        // {
        //     title: 'ACTIONS',
        //     dataIndex: '',
        //     width: '90px',
        //     key: '',
        //     render: (record:any) => {
        //         console.log('Record: ', record);
        //         return (
        //             <>
        //                 <span className="flex gap-2">
        //                     <Link
        //                         to={`/attribute/create-attribute/${record?._id}`}
        //                         // onClick={() => selectAttribute(record)}
        //                     >
        //                         <img
        //                             src="/icons/common/edit.svg"
        //                             alt="edit"
        //                             className="h-[20px]"
        //                         />
        //                     </Link>
        //                     <div
        //                         className="cursor-pointer"
        //                         // onClick={() => deleteSubAttribute(record.key)}
        //                     >
        //                         <img
        //                             src="/icons/common/delete.svg"
        //                             alt="delete"
        //                             className="h-[20px]"
        //                         />
        //                     </div>
        //                 </span>
        //             </>
        //         );
        //     },
        // }
    ];

    const combinedColumns = [...columns, ...selectColumn];

    const transformedData = Object.keys(parentAttributeList || {})?.map(
        (key) => ({
            attributeRealType: key === 'PLAN_TYPE' ? 'Plan Type' : key,
            attributeType: key,
            attributeName: parentAttributeList[key],
        })
    );

    const handleConfirmStatusChange = () => {
        // Handle the status change logic here
        // console.log('Status changed for record:', currentRecord);
        setConfirmationModalVisible(false);
    };

    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };

    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={combinedColumns}
                dataSource={transformedData}
                // addNewLink="/"
                loading={loader}
                bulkAction={false}
                // addNewTitle="Create Attribute"
                heading="Attribute"
            />
            {/* <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={attributeListCount}
                    pageSize={pageSizes}
                    onChange={paginate}
                    // hideOnSinglePage
                />
            </div> */}

            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
        </>
    );
};

export default AttributeListing;

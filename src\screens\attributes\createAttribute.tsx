import {
    <PERSON><PERSON>,
    Col,
    Config<PERSON><PERSON><PERSON>,
    Form,
    Input,
    Row,
    FormProps,
    Typography,
    Select,
} from 'antd';
import React, { useEffect, useState } from 'react';
import 'react-quill/dist/quill.snow.css';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import {
    AttributeDetails,
    AttributeTypeListForDropdown,
    CreateAttributeData,
    UpdateAttributeData,
} from '~/redux/actions/attribute-action';
import { AppDispatch } from '~/redux/store';

const { Title } = Typography;

function goBack() {
    window.history.back();
}

const CreateAttribute: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [loader, startLoader, endLoader] = useLoader();
    const { attributeDetails, attributeTypeDropdownList } = useSelector(
        (state: any) => state.attribute_store
    );

    const [location, setLocation] = useLocation();
    const attributeId = location.split('/').pop();

    const [attributes, setAttributes] = useState<any>([]);

    const handleAttributeTypeChange = async (attributeType: string) => {
        if (attributeType)
            await dispatch(
                AttributeTypeListForDropdown({ attributeType })
            ).then((res: any) => {
                setAttributes(
                    res.payload?.data?.data?.map((data: any) => ({
                        label: data.name,
                        value: data._id,
                    }))
                );
            });
    };
    useEffect(() => {
        if (attributeDetails && attributeId !== '0') {
            const { name, attributeType } = attributeDetails;
            handleAttributeTypeChange(attributeType);
            form.setFieldsValue({ name, attributeType });
        }
    }, [attributeDetails]);

    useEffect(() => {
        if (attributeId !== '0') {
            dispatch(AttributeDetails({ attributeId }));
        }
    }, [attributeId]);

    const handleCreateOption = (inputValue: string) => {
        if (!attributes.find((option: any) => option.value === inputValue)) {
            const newOption = { value: inputValue, label: inputValue };
            setAttributes([newOption, ...attributeTypeDropdownList]);
            form.setFieldValue('attributeId', inputValue);
        }
    };

    const onFinish: FormProps['onFinish'] = async (values) => {
        startLoader();
        const attributeData: any = attributeTypeDropdownList.find(
            (type: any) => type.value === values.attributeId
        );
        const payload = {
            ...(attributeId === '0'
                ? attributeData
                    ? {
                          name: attributeData?.label,
                      }
                    : { name: values.attributeId }
                : { name: values.name }),
            attributeType: values.attributeType,
        };

        try {
            const response =
                attributeId === '0'
                    ? await dispatch(CreateAttributeData(payload)).unwrap()
                    : await dispatch(
                          UpdateAttributeData({ reqData: payload, attributeId })
                      ).unwrap();

            if (response?.status === 200 || response?.status === 201) {
                setLocation(
                    `/attribute-type-list?type=${values.attributeType}`
                );
            }
        } catch (error) {
            console.error('API Error:', error);
        } finally {
            endLoader();
        }
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                },
            }}
        >
            <div className="flex items-center gap-4">
                <img
                    src="/icons/back.svg"
                    alt="Back"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1A3353]" level={4}>
                    {attributeId === '0'
                        ? 'Create Attribute'
                        : 'Edit Attribute'}
                </Title>
            </div>
            <Row className="mt-16">
                <Col
                    className="w-[100%] rounded-3xl border p-16 sm:w-[80%]"
                    span={30}
                >
                    <Form
                        name="attributeCreate"
                        layout="vertical"
                        size="large"
                        form={form}
                        initialValues={{ remember: true }}
                        onFinish={onFinish}
                        autoComplete="off"
                    >
                        <Form.Item
                            label="Attribute Type"
                            name="attributeType"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select attribute type!',
                                },
                            ]}
                        >
                            <Select
                                showSearch
                                placeholder="Select Type"
                                filterOption={(input, option) =>
                                    (option?.label ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                onChange={handleAttributeTypeChange}
                                options={[
                                    { value: 'category', label: 'Category' },
                                    { value: 'tier', label: 'Tier' },
                                    { value: 'planType', label: 'Plan Type' },
                                ]}
                            />
                        </Form.Item>
                        {attributeId === '0' ? (
                            <Form.Item
                                label="Attribute Name"
                                name="attributeId"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter or select attribute name!',
                                    },
                                ]}
                            >
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    placeholder="Enter or select attribute name"
                                    options={attributes}
                                    filterOption={(input, option) =>
                                        (option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    onInputKeyDown={(e: any) => {
                                        if (
                                            e.key === 'Enter' &&
                                            e.target.value
                                        ) {
                                            handleCreateOption(e.target.value);
                                        }
                                    }}
                                    onSelect={(value) => {
                                        form.setFieldValue(
                                            'attributeId',
                                            value
                                        );
                                    }}
                                />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                label="Attribute Name"
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter attribute name!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter Attribute Name" />
                            </Form.Item>
                        )}
                        <Form.Item className="mt-8">
                            <Button
                                htmlType="submit"
                                loading={loader}
                                className=" bg-purpleLight text-white @md:h-auto @md:py-[5px] @lg:h-auto @lg:py-[4px]"
                            >
                                Submit
                            </Button>
                        </Form.Item>
                    </Form>
                </Col>
            </Row>
        </ConfigProvider>
    );
};

export default CreateAttribute;

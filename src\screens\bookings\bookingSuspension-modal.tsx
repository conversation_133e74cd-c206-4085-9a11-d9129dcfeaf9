import { Modal, DatePicker, Form, FormProps, Button, Input } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import {
    MembershipSuspension,
    revokeMembershipSuspension,
} from '~/redux/actions/membershipSuspension-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
const { RangePicker } = DatePicker;
const { TextArea } = Input;
dayjs.extend(utc);
dayjs.extend(timezone);
interface Suspension {
    fromDate: string;
    endDate: string;
}

interface BookingSuspensionModalProps {
    visible: boolean;
    onClose: (refresh?: any) => void;
    purchaseId?: string;
    purchaseData?: any;
}

const BookingSuspensionModal: React.FC<BookingSuspensionModalProps> = ({
    visible,
    onClose,
    purchaseId,
    purchaseData,
}) => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const activeSuspension = useMemo(() => {
        if (purchaseData?.suspensions && purchaseData.suspensions.length > 0) {
            return purchaseData.suspensions[0];
        }
        return null;
    }, [purchaseData]);

    const suspensionStart = activeSuspension
        ? dayjs(activeSuspension.fromDate).utc().startOf('day')
        : null;

    const suspensionEnd = activeSuspension
        ? dayjs(activeSuspension.endDate).utc().endOf('day')
        : null;

    useEffect(() => {
        if (visible && activeSuspension) {
                const from = dayjs.utc(activeSuspension.fromDate).startOf('day').local();
                const to = dayjs.utc(activeSuspension.endDate).subtract(1, 'second').startOf('day').local();

            form.setFieldsValue({
                startDate: from,
                endDate: to,
            });
        }

        if (!visible) {
            form.resetFields();
        }
    }, [visible, activeSuspension]);

    const handleChangeDateRange = (dates: [Dayjs, Dayjs] | null) => {
        if (dates) {
            form.setFieldsValue({ startDate: dates[0], endDate: dates[1] });
        }
    };

    const handleDisabledDate = (current: Dayjs) => {
        if (!purchaseData?.startDate || !purchaseData?.endDate) return true;

        const today = dayjs().startOf('day');
        const startDate = dayjs(purchaseData.startDate).startOf('day');
        const endDate = dayjs(purchaseData.endDate).startOf('day');

        const effectiveStart = startDate.isBefore(today) ? today : startDate;
        const currentDate = current.startOf('day');

        if (purchaseData.isSuspended && purchaseData.suspensions?.length > 0) {
            const suspension = purchaseData.suspensions[0];
          const suspensionStart = dayjs.utc(suspension.fromDate).startOf('day').local();
          const suspensionEnd = dayjs.utc(suspension.endDate).startOf('day').local();

          return !currentDate.isBetween(suspensionStart, suspensionEnd, 'day', '[]');
        }

        // ✅ Suspend: allow only between startDate and endDate (inclusive)
        const isOutOfRange =
          currentDate.isBefore(effectiveStart) || currentDate.isAfter(endDate);

        return isOutOfRange;
    };

      

    const onFinish: FormProps['onFinish'] = async (values) => {
        const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        const fromDate = values.startDate
            ? dayjs(values.startDate)
                  .tz(userTimeZone)
                  .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
            : null;

        const endDate = values.endDate
            ? dayjs(values.endDate)
                  .tz(userTimeZone)
                  .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
            : null;

        const notes = values.notes || '';
        let payload = {};

        if (!purchaseData.isSuspended) {
            payload = {
                fromDate,
                endDate,
                notes,
                purchaseId: purchaseId || undefined,
            };
        } else {
            payload = {
                fromDate,
                purchaseId: purchaseId || undefined,
                suspensionId: activeSuspension?._id || undefined,
            };
        }

        try {
            if (!purchaseData.isSuspended) {
                await dispatch(MembershipSuspension({ payload })).unwrap();
            } else {
                await dispatch(
                    revokeMembershipSuspension({ payload })
                ).unwrap();
            }
            onClose(true);
        } catch (error) {
            console.error('Error in suspension', error);
        }
    };

    return (
        <Modal
            title={
                <div className="border-b pb-2 text-[#1A3353] ">
                    {activeSuspension
                        ? 'Do you want to Revoke Suspension Period?'
                        : 'Change Suspension Period'}
                </div>
            }
            open={visible}
            centered
            onCancel={onClose}
            footer={null}
            className="lg:w-[55%]"
            style={{ top: 10 }}
        >
            <div className="    ">
                <Form
                    layout="vertical"
                    className="w-full space-y-4"
                    form={form}
                    onFinish={onFinish}
                >
                    <Form.Item
                        label={
                            <span className="font-medium ">
                                Select Date Range
                            </span>
                        }
                        name="dateRange"
                        rules={[
                            {
                                required: true,
                                message: 'Please select a date range',
                            },
                        ]}
                    >
                        <RangePicker
                            onChange={handleChangeDateRange}
                            disabledDate={handleDisabledDate}
                            className="w-full rounded-md border p-2"
                        />
                    </Form.Item>
                    <Form.Item name="startDate" hidden>
                        <input type="hidden" />
                    </Form.Item>
                    <Form.Item name="endDate" hidden>
                        <input type="hidden" />
                    </Form.Item>
                    {!purchaseData.isSuspended && (
                        <Form.Item
                            label={<span className="font-medium ">Reason</span>}
                            name="notes"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter a reason',
                                },
                            ]}
                        >
                            <TextArea
                                rows={4}
                                className="rounded-md border p-2"
                            />
                        </Form.Item>
                    )}

                    <div className="flex justify-end space-x-4 pt-6">
                        <Button
                            onClick={onClose}
                            className="rounded-md border border-gray-500 px-6 py-2"
                        >
                            Cancel
                        </Button>
                        <Button
                            className="rounded-md bg-[#B79DF9] px-6 py-2 text-white"
                            type="primary"
                            htmlType="submit"
                        >
                            Save
                        </Button>
                    </div>
                </Form>
            </div>
        </Modal>
    );
};

export default BookingSuspensionModal;

// MyModal.tsx
import React, { useEffect, useState } from 'react';
import {
    Button,
    Checkbox,
    Collapse,
    ConfigProvider,
    DatePicker,
    Form,
    FormProps,
    GetProp,
    Input,
    message,
    Modal,
    Select,
    Space,
    Upload,
    UploadProps,
} from 'antd';
import Title from 'antd/es/typography/Title';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    BillingCityList,
    CityList,
    UploadImage,
} from '~/redux/actions/common-action';
import { useDebounce, useEmailMobileValidator } from '~/hooks/useDebounce';
import {
    LoadingOutlined,
    MinusCircleOutlined,
    PictureOutlined,
    PlusOutlined,
} from '@ant-design/icons';

import {
    ClientsDetails,
    CreateClient,
    UpdateClient,
} from '~/redux/actions/customer-action';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import dayjs from 'dayjs';
import { formatStringWithSpaces } from '~/components/common/function';
import { useLocation } from 'wouter';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import { PhoneNumberUtil, PhoneNumberType } from 'google-libphonenumber';
import Alertify from '~/services/alertify';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { GetSettings } from '~/redux/actions/settings-actions';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';

const phoneUtil = PhoneNumberUtil.getInstance();

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const getBase64 = (img: FileType, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
};

const beforeUpload = (file: FileType) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
        message.error('You can only upload JPG/PNG file!');
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
        message.error('Image must smaller than 2MB!');
    }
    return isJpgOrPng && isLt2M;
};

interface AddClientModalProps {
    open: boolean | undefined;
    onClose: () => void;
    isEdit?: boolean | undefined;
    clientId?: string;
    onClientAdded?: any;
    isAdded?: boolean;
}

const AddClientModal: React.FC<AddClientModalProps> = ({
    open,
    onClose,
    isEdit,
    clientId,
    onClientAdded,
    isAdded,
}) => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const { countryList, cityList, billingCityList } = useSelector(
        (state: any) => state.common_store
    );
    const { role } = useSelector((state: any) => state.auth_store);
    const [selectedState, setSelectedState] = useState<string | null>(null);
    const [b2BselectedState, setB2bSelectedState] = useState<string | null>(
        null
    );
    const [activeTab, setActiveTab] = useState('address');
    const [selectedPolicies, setSelectedPolicies] = useState<string[]>([]);
    const [_, setLocation] = useLocation();
    const [imageUrl, setImageUrl] = useState<string>();
    const [loading, setLoading] = useState(false);
    const [isSelectedState, setIsSelectedState] = useState<boolean>(false);
    const [isB2BSelectedState, setIsB2BSelectedState] =
        useState<boolean>(false);
    const debouncedRequest = useDebounce((callback) => callback(), 200);
    const [citySearchText, setCitySearchText] = useState<string>('');
    const [b2BcitySearchText, setB2bCitySearchText] = useState<string>('');
    const [age, setAge] = useState<number | null>(0);
    const [loader, startLoader, endLoader] = useLoader();
    const [assignloader, assignStartLoader, assignEndLoader] = useLoader();
    const [facilitySearch, setFacilitySearch] = useState<string>('');
    const [phone, setPhone] = useState('');
    const { facilityList } = useSelector((state: any) => state.facility_store);
    const [phoneError, setPhoneError] = useState<string | null>(null);
    const [dialCode, setDialCode] = useState<string>('');
    const [countryCode, setCountryCode] = useState<string>('IN');
    const [minLength, setMinLength] = useState<number>(0); // Default min length
    const [maxLength, setMaxLength] = useState<number>(0); // Default max length
    const [addressData, setAddressData] = useState({});
    const [b2bAddressData, setB2bAddressData] = useState({});
    const [formData, setFormData] = useState<any>({});
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(true);
    const [requirePinModal, setRequirePinModal] = useState<boolean>(false);

    const updatePhoneLengthLimits = async (country: string) => {
        try {
            const exampleNumber = await phoneUtil.getExampleNumberForType(
                country,
                PhoneNumberType.MOBILE
            );
            setMinLength(0);
            setMaxLength(0);
            if (exampleNumber) {
                console.log(exampleNumber.getNationalNumber());
                const exampleNumberLength = exampleNumber
                    .getNationalNumber()
                    .toString().length;
                console.log(exampleNumber);

                setMinLength(exampleNumberLength);
                setMaxLength(exampleNumberLength);
                return;
            }
        } catch (error) {
            console.error('Error fetching phone number metadata:', error);
        }
    };

    const validateEmailAndPhone = useEmailMobileValidator();

    const removeCountryCode = (fullNumber: string, dialCode: string) => {
        if (fullNumber.startsWith(dialCode)) {
            return fullNumber.slice(dialCode.length);
        }
        return fullNumber;
    };

    const validatePhoneNumber = async (value: string) => {
        console.log(value);
        if (!value) {
            setPhoneError('Please enter a phone number');
            return false;
        }

        await updatePhoneLengthLimits(countryCode);
        const phoneNumberWithoutCountryCode = removeCountryCode(
            value,
            dialCode
        );

        if (phoneNumberWithoutCountryCode.length < minLength) {
            setPhoneError(
                `Phone number is too short. Minimum length: ${minLength}`
            );
            return false;
        }
        if (phoneNumberWithoutCountryCode.length > maxLength) {
            setPhoneError(
                `Phone number is too long. Maximum length: ${maxLength}`
            );
            return false;
        }

        setPhoneError(null);
        return true;
    };

    const handlePhoneChange = async (value: string, country: any) => {
        const newCountryCode = country?.countryCode?.toUpperCase() || 'IN';
        const newDialCode = country?.dialCode || '91';

        if (newCountryCode !== countryCode) {
            setPhone(newDialCode);
            form.setFieldsValue({ mobile: '', countryCode: `+${newDialCode}` });
            setPhoneError(null);
            setCountryCode(newCountryCode);
            await updatePhoneLengthLimits(newCountryCode);
        } else {
            if (value.length <= maxLength + newDialCode.length) {
                setPhone(value);
                form.setFieldsValue({
                    mobile: removeCountryCode(value, newDialCode),
                    countryCode: `+${newDialCode}`,
                });
            }
            setDialCode(newDialCode);
            validatePhoneNumber(value);
        }
    };

    const store = useAppSelector((state) => ({
        clientDetails: state.customer_store.customerDetails,
        clientOnboarding: state.settings_store.clientOnboarding,
    }));

    const {
        policies,
        proficiencyLevel = [],
        showProficiencyLevel,
        showPolicies,
    } = store.clientOnboarding || {};

    useEffect(() => {
        if (clientId && isEdit) {
            dispatch(ClientsDetails({ clientId: clientId }));
        } else {
            setImageUrl('');
        }
    }, [clientId, isEdit]);

    useEffect(() => {
        console.log('0');
        if (store.clientDetails && isEdit && clientId) {
            const stateId = store.clientDetails.address?.state;
            const cityId = store.clientDetails.address?.city;
            const policies = store.clientDetails?.policies?.map(
                (policy: any) => policy.policyType
            );
            if (stateId) {
                dispatch(CityList({ page: 1, pageSize: 50, cityId: cityId }));
            }
            console.log('1', store.clientDetails.dob);
            form.setFieldsValue({
                firstName: store.clientDetails.firstName,
                lastName: store.clientDetails.lastName,
                Gender: store.clientDetails.gender,
                facilityId: store.clientDetails.facilityId,
                email: store.clientDetails.email,
                activityLevel: store.clientDetails.activityLevel,
                mobile: store.clientDetails.mobile,
                street: store.clientDetails.address?.street,
                addressLine1: store.clientDetails.address?.addressLine1,
                addressLine2: store.clientDetails.address?.addressLine2,
                city: store.clientDetails.address?.city,
                postalCode: store.clientDetails.address?.postalCode,
                isPrimaryAddress: store.clientDetails?.address?.isDefault,
                state: stateId,
                policies: policies,
                country: store.clientDetails.address?.country,
                emergencyContactPhone:
                    store.clientDetails.emergencyContactPhone,
                emergencyContactPerson:
                    store.clientDetails.emergencyContactPerson,
                B2bGST: store.clientDetails?.businessAddress?.gstNumber,
                B2bBusinessName:
                    store.clientDetails?.businessAddress?.businessName,
                B2bAddress1: store.clientDetails?.businessAddress?.addressLine1,
                B2bAddress2: store.clientDetails?.businessAddress?.addressLine2,
                B2bcity: store.clientDetails?.businessAddress?.city,
                B2bstate: store.clientDetails?.businessAddress?.state,
                B2bpostalCode: store.clientDetails?.businessAddress?.postalCode,
                isPrimaryB2BAddress:
                    store.clientDetails?.businessAddress?.isDefault,
            });
            form.setFieldsValue({
                dob: dayjs(store.clientDetails.dob),
            });
            setImageUrl(store.clientDetails.photo);
        } else {
            form.resetFields();
        }
    }, [store.clientDetails, form, isEdit, clientId]);

    useEffect(() => {
        dispatch(GetSettings({})).unwrap();
    }, []);

    // console.log("facilityList--------------", facilityList);

    const handleDobChange = (date: null, dateString: any) => {};

    useEffect(() => {
        try {
            if (facilitySearch) {
                dispatch(
                    FacilitiesList({
                        page: 1,
                        pageSize: 30,
                        search: facilitySearch,
                    })
                        .unwrap()
                        .then((res: any) => {
                            form.setFieldsValue({
                                facilityId: res?.data?.data?.list?.[0]?._id,
                            });
                        })
                );
            } else {
                dispatch(FacilitiesList({ page: 1, pageSize: 30 }))
                    .unwrap()
                    .then((res: any) => {
                        form.setFieldsValue({
                            facilityId: res?.data?.data?.list?.[0]?._id,
                        });
                    });
            }
        } catch (error) {
            console.log('first error', error);
        }
    }, [facilitySearch]);

    const CountryOptions = countryList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const FacilityOptions = facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const handlePolicyChange = (checkedValues: string[]) => {
        setSelectedPolicies(checkedValues);
        form.setFieldsValue({ policies: checkedValues });
    };

    const handleImageUpload = (file: any) => {
        console.log('Uploaded file:', file);
        setLoading(true);
        dispatch(UploadImage({ file: file.file }))
            .then((res: any) => {
                console.log('Res------------------', res);
                setImageUrl(res?.payload?.res?.data?.data);
            })
            .finally(() => setLoading(false));
    };

    const handleChange: UploadProps['onChange'] = (info) => {
        if (info.file.status === 'uploading') {
            setLoading(true);
            return;
        }
        if (info.file.status === 'done') {
            getBase64(info.file.originFileObj as FileType, (url) => {
                setLoading(false);
                setImageUrl(url);
            });
        }
    };

    const handleStateChange = (value: string) => {
        setSelectedState(value);
        setIsSelectedState(true);
        form.setFieldValue('city', null);
        dispatch(CityList({ stateId: value, page: 1, pageSize: 50 }));
    };

    const handleBillingStateChange = (value: string) => {
        setB2bSelectedState(value);
        setIsB2BSelectedState(true);
        form.setFieldValue('B2bcity', null);
        dispatch(BillingCityList({ stateId: value, page: 1, pageSize: 50 }));
    };

    useEffect(() => {
        if (citySearchText) {
            debouncedRequest(() => {
                dispatch(
                    CityList({
                        stateId: selectedState,
                        page: 1,
                        pageSize: 50,
                        search: citySearchText,
                    })
                );
            });
        }
    }, [citySearchText]);

    useEffect(() => {
        if (b2BcitySearchText) {
            debouncedRequest(() => {
                dispatch(
                    BillingCityList({
                        stateId: b2BselectedState,
                        page: 1,
                        pageSize: 50,
                        search: b2BcitySearchText,
                    })
                );
            });
        }
    }, [b2BcitySearchText]);

    const CityOptions = cityList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const BillingCityOptions = Array.isArray(billingCityList)
        ? billingCityList?.map((item: any) => ({
              value: item._id,
              label: item.name,
              id: item._id,
          }))
        : [];

    const handleAddClientModalClose = () => {
        form.resetFields();
        setImageUrl('');
        setPhone('');
        setPhoneError(null);
        setSelectedPolicies([]);
        setSelectedState(null);
        setB2bSelectedState(null);
        setActiveTab('address');
        onClose();
    };

    const saveClientDetails = async (values: any, redirect = false) => {
        console.log('values in save details-------------', values);
        try {
            const formattedPolicies = selectedPolicies.map((policyName) => ({
                policyType: policyName,
                isEnabled: true,
            }));

            const formValues = { ...addressData, ...b2bAddressData, ...values };
            const hasBusinessAddress =
                formValues.B2bGST ||
                formValues.B2bBusinessName ||
                formValues.B2bAddress1 ||
                formValues.B2bAddress2 ||
                formValues.B2bcity ||
                formValues.B2bstate ||
                formValues.B2bpostalCode;

            const payload = {
                firstName: formValues.firstName,
                lastName: formValues.lastName,
                dob: formValues.dob?.toISOString(),
                gender: formValues.Gender,
                activityLevel: formValues.activityLevel,
                countryCode: formValues.countryCode,
                mobile: formValues.mobile,
                email: formValues.email,
                address: {
                    addressLine1: formValues.addressLine1,
                    addressLine2: formValues.addressLine2,
                    ...(formValues.postalCode && {
                        postalCode: Number(formValues.postalCode),
                    }),
                    city: formValues.city,
                    state: formValues.state,
                    country: 'India',
                    isDefault: formValues.isPrimaryAddress,
                },
                businessAddress: {
                    gstNumber: formValues.B2bGST,
                    businessName: formValues.B2bBusinessName,
                    addressLine1: formValues.B2bAddress1,
                    addressLine2: formValues.B2bAddress2,
                    city: formValues.B2bcity,
                    state: formValues.B2bstate,
                    ...(formValues.B2bpostalCode && {
                        postalCode: Number(formValues.B2bpostalCode),
                    }),

                    isDefault: formValues.isPrimaryB2BAddress,
                },
                isBusiness: hasBusinessAddress ? true : false,
                emergencyContactPerson: formValues.emergencyContactPerson,
                emergencyContactPhone: formValues.emergencyContactPhone,
                facilityId: formValues.facilityId,
                photo: imageUrl,
                policies: formattedPolicies,
                minor: formValues.minors,
            };

            // console.log('Payload------------', payload);

            let response;
            if (isEdit && clientId) {
                response = await dispatch(
                    UpdateClient({ reqData: payload, clientId })
                ).unwrap();
            } else {
                response = await dispatch(CreateClient(payload)).unwrap();
            }

            if (response?.status === 200 || response?.status === 201) {
                if (redirect) {
                    setLocation(
                        `/point-of-sales/${response?.data?.data?.userId}/${response?.data?.data?._id}`
                    );
                    handleAddClientModalClose();
                    form.resetFields();
                    setImageUrl('');
                } else {
                    if (isAdded) {
                        console.log(
                            'Data for prepopulate --------',
                            response?.data?.data
                        );
                        onClientAdded({
                            ...response?.data?.data,
                            mobile: payload.mobile,
                        });
                    }
                    handleAddClientModalClose();
                    form.resetFields();
                    setFormData(null);
                    setImageUrl('');
                }
            }
        } catch (error) {
            console.error('Save error:', error);
            throw error;
        } finally {
            endLoader();
            assignEndLoader();
        }
    };

    useEffect(() => {
        if (!open || role === RoleType.ORGANIZATION) return;

        dispatch(
            GetSettingActiveStatus({
                settingKey: 'settings_pin',
            })
        ).then((response: any) => {
            const settingData = response?.payload?.data?.data;
            const isEnabled = settingData?.isEnabled;
            const isActive = settingData?.isActive;

            if (isEnabled && isActive) {
                setRequirePinModal(true);
            } else {
                setRequirePinModal(false);
                setPinModalVisible(false);
            }
        });
    }, [open, role, dispatch]);

    const onFinish: FormProps['onFinish'] = async (values) => {
        startLoader();
        try {
            console.log(phone);
            if (!(await validatePhoneNumber(phone))) {
                return;
            }
            const formValues = { ...values, ...formData };
            if (!formValues?.state) {
                Alertify.error('Please select the state in address tab.');
                return;
            }
            await saveClientDetails(formValues);
            form.resetFields();
            setPhone('');
            setPhoneError(null);
            onClose();
        } catch (error) {
            console.error('Error saving client details:', error);
        } finally {
            endLoader();
        }
    };

    const handleSaveAndRedirect = async () => {
        try {
            assignStartLoader();

            const values = await form.validateFields();
            if (!(await validatePhoneNumber(phone))) {
                return; // Prevent form submission if phone is invalid
            }
            await saveClientDetails(values, true);
        } catch (error) {
            console.error('Error saving client details:', error);
        } finally {
            assignEndLoader();
        }
    };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? (
                <LoadingOutlined className="text-4xl text-[#455560]" />
            ) : (
                <PictureOutlined className="text-4xl text-[#455560]" />
            )}
            <div className="text-lg text-[#455560]">
                Drop your jpg, png here
            </div>
        </button>
    );

    const handleTabChange = (tab: string) => {
        const formValues = form.getFieldsValue();
        setFormData(formValues);
        if (activeTab === 'address') {
            setAddressData({
                addressLine1: formValues.addressLine1,
                addressLine2: formValues.addressLine2,
                postalCode: formValues.postalCode,
                city: formValues.city,
                state: formValues.state,
                isPrimaryAddress: formValues.isPrimaryAddress,
            }); // Store Address Data
        } else if (activeTab === 'billing') {
            setB2bAddressData({
                B2bBusinessName: formValues.B2bBusinessName,
                B2bGST: formValues.B2bGST,
                B2bAddress1: formValues.B2bAddress1,
                B2bAddress2: formValues.B2bAddress2,
                B2bcity: formValues.B2bcity,
                B2bstate: formValues.B2bstate,
                B2bpostalCode: formValues.B2bpostalCode,
                isPrimaryB2BAddress: formValues.isPrimaryB2BAddress,
            }); // Store B2B Address Data
        }

        setActiveTab(tab); // Change the tab

        // Restore previous values when switching tabs
        if (tab === 'address') {
            form.setFieldsValue(addressData);
        } else if (tab === 'billing') {
            form.setFieldsValue(b2bAddressData);
        }
    };

    if (requirePinModal && pinModalVisible && role !== RoleType.ORGANIZATION) {
        return (
            <ModulePinConfirmationModal
                visible={true}
                onConfirm={() => setPinModalVisible(false)}
                onCancel={onClose}
                module={SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING}
                subModule={PERMISSIONS_ENUM.CLIENTS_WRITE}
            />
        );
    }

    if ((!open || pinModalVisible) && role !== RoleType.ORGANIZATION) {
        return null;
    }
    return (
        <Modal
            className="w-[90%]"
            title={
                <Title className="text-[#1A3353]" level={4}>
                    {isEdit ? 'Edit Client' : 'Add Client'}
                </Title>
            }
            open={open}
            onOk={handleAddClientModalClose}
            onCancel={handleAddClientModalClose}
            style={{ top: '5px' }}
            footer={false}
        >
            {/* ==================info form==================== */}

            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            itemMarginBottom: 22,
                            verticalLabelMargin: -5,
                        },
                    },
                    token: {
                        borderRadius: 5,
                    },
                }}
            >
                <Form
                    name="Add Client"
                    layout="vertical"
                    id="custom-lead-form"
                    className="custom-lead-form"
                    form={form}
                    size="large"
                    autoComplete="off"
                    initialValues={{ remember: true, countryCode: 'IN' }}
                    onFinish={onFinish}
                >
                    <div>
                        <div className="flex lg:flex-row   lg:gap-10 @sm:flex-col">
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label="First Name"
                                    name="firstName"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please enter first name',
                                        },
                                    ]}
                                >
                                    <Input
                                        type="text"
                                        placeholder="Enter first name"
                                    />
                                </Form.Item>
                            </div>
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label="Last Name"
                                    name="lastName"
                                    // rules={[
                                    //     {
                                    //         required: true,
                                    //         message: 'Please enter last name',
                                    //     },
                                    // ]}
                                >
                                    <Input placeholder="Enter last name" />
                                </Form.Item>
                            </div>
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label={
                                        <span>
                                            <span className="font-medium text-black">
                                                D.O.B
                                            </span>{' '}
                                            <span className="text-gray-400">
                                                ( Optional )
                                            </span>
                                        </span>
                                    }
                                    name="dob"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please enter DOB',
                                        },
                                    ]}
                                >
                                    <DatePicker
                                        placeholder="DD/MM/YYYY"
                                        format="DD/MM/YYYY"
                                        style={{ width: '100%' }}
                                        onChange={handleDobChange}
                                        maxDate={dayjs(new Date()).endOf('day')}
                                        popupClassName="custom-datepicker"
                                    />
                                </Form.Item>
                            </div>
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label="Gender"
                                    name="Gender"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select gender',
                                        },
                                    ]}
                                >
                                    <Select
                                        placeholder="Select gender"
                                        showSearch
                                        filterOption={(input, option) =>
                                            (option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={[
                                            { label: 'MALE', value: 'male' },
                                            {
                                                label: 'FEMALE',
                                                value: 'female',
                                            },

                                            { label: 'OTHER', value: 'other' },
                                        ]}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex lg:flex-row   lg:gap-10 @sm:flex-col">
                            <div className=" w-full px-2 md:w-1/2">
                                {/* <Form.Item
                                label="Age"
                                name="age"
                                rules={[
                                    {
                                        required: false,
                                        message: 'Please enter age',
                                    },
                                ]}
                            >
                                <Input disabled placeholder="Enter your age" />
                            </Form.Item> */}
                                <Form.Item
                                    label="Facility"
                                    name="facilityId"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select facility!',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        onSearch={(value) =>
                                            setFacilitySearch(value)
                                        }
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={FacilityOptions}
                                    />
                                </Form.Item>
                            </div>
                            {showProficiencyLevel && (
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Proficiency Level"
                                        name="activityLevel"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select proficiency level',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            placeholder="Select proficiency level"
                                            filterOption={(
                                                input: any,
                                                option: any
                                            ) =>
                                                (option?.label ?? '')
                                                    ?.toLowerCase()
                                                    ?.includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={proficiencyLevel?.map(
                                                (obj: any) => ({
                                                    label: obj.value,
                                                    value: obj.value,
                                                })
                                            )}
                                        />
                                    </Form.Item>
                                </div>
                            )}
                            {/* <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label="Country Code"
                                    name="countryCode"
                                    rules={[
                                        {
                                            required: false,
                                            message:
                                                'Please enter Country Code',
                                        },
                                    ]}
                                >
                                    <Input
                                        type="text"
                                        // maxLength={10}
                                        onInput={(e: any) => {
                                            e.target.value = e.target.value.replace(/[^0-9+]/g, '');
                                        }}
                                        placeholder="Enter  Country  Code"
                                    />
                                </Form.Item>
                            </div> */}
                            <div className=" flex w-full flex-col  px-2  md:w-1/2">
                                {/* <Form.Item
                                    label="Mobile Number"
                                    name="mobile"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter mobile number',
                                        },
                                    ]}
                                >
                                    <Input
                                        type="text"
                                        // maxLength={10}
                                        onInput={(e: any) => {
                                            e.target.value =
                                                e.target.value.replace(
                                                    /[^0-9]/g,
                                                    ''
                                                );
                                        }}
                                        placeholder="Enter  mobile number"
                                    />
                                </Form.Item> */}
                                {/* <p className="text-[13px] font-medium text-[#1A3353]">
                                    Phone Number
                                </p>
                                <PhoneInput
                                    country={'in'}
                                    inputStyle={{ height: '34px' }}
                                    enableSearch={true}
                                    value={phone}
                                    onChange={(phone) => setPhone(phone)}
                                /> */}
                                <Form.Item
                                    label={
                                        <div className="flex gap-2">
                                            Phone Number
                                            <span className="text-10 text-[red]">
                                                *
                                            </span>
                                        </div>
                                    }
                                    validateTrigger={['onBlur', 'onSubmit']}
                                    help={phoneError}
                                    validateStatus={phoneError ? 'error' : ''}
                                    // rules={[
                                    //     {
                                    //         required: true,
                                    //         message:
                                    //             'Please enter mobile number',
                                    //     },
                                    // ]}
                                >
                                    <PhoneInput
                                        inputProps={{ required: true }}
                                        country={'in'}
                                        inputStyle={{
                                            width: '100%',
                                            height: '34px',
                                        }}
                                        enableSearch={true}
                                        value={phone}
                                        onChange={handlePhoneChange}
                                        isValid={(value: any, country: any) => {
                                            const phoneNumberWithoutCountryCode =
                                                removeCountryCode(
                                                    value,
                                                    country?.dialCode
                                                );
                                            return (
                                                phoneNumberWithoutCountryCode.length >=
                                                    minLength &&
                                                phoneNumberWithoutCountryCode.length <=
                                                    maxLength
                                            );
                                        }}
                                        onBlur={() =>
                                            validatePhoneNumber(phone)
                                        }
                                    />
                                </Form.Item>

                                {/* Hidden Fields for Country Code and Mobile Number */}
                                <Form.Item name="countryCode" hidden>
                                    <Input />
                                </Form.Item>
                                <Form.Item name="mobile" hidden>
                                    <Input />
                                </Form.Item>
                            </div>
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label="Email"
                                    name="email"
                                    rules={[
                                        {
                                            required: false,

                                            message:
                                                'Please enter email address',
                                        },
                                        {
                                            type: 'email',
                                            message: 'Please enter valid email',
                                        },
                                        {
                                            validator: (_, value) =>
                                                value
                                                    ? validateEmailAndPhone(
                                                          value,
                                                          true
                                                      )
                                                    : Promise.resolve(),
                                        },
                                    ]}
                                >
                                    <Input
                                        type="Email"
                                        placeholder="Enter email address"
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>

                    {/* ==================tab buttons==================== */}

                    <div className="flex flex-row items-center gap-10 pb-7 ps-5 pt-3">
                        <p
                            className={`w-fit cursor-pointer border-b-2 pb-1 text-2xl font-semibold ${
                                activeTab === 'address'
                                    ? 'border-primary '
                                    : 'border-transparent'
                            }`}
                            onClick={() => handleTabChange('address')}
                        >
                            Address
                        </p>
                        <p
                            className={`w-fit cursor-pointer border-b-2 pb-1 text-2xl font-semibold ${
                                activeTab === 'billing'
                                    ? 'border-primary '
                                    : 'border-transparent'
                            }`}
                            onClick={() => handleTabChange('billing')}
                        >
                            B2B Billing (Optional)
                        </p>
                    </div>
                    {/* ========================= address form=======================*/}
                    {activeTab === 'address' && (
                        <div>
                            <Form.Item
                                name="isPrimaryAddress"
                                valuePropName="checked"
                            >
                                <ConfigProvider
                                    theme={{
                                        token: {
                                            // colorBorder: '#8143D1',
                                            colorPrimary: '#8143D1',
                                            colorPrimaryHover: '#8143D1',
                                        },
                                    }}
                                >
                                    <Checkbox
                                        onChange={(e) => {
                                            if (e.target.checked) {
                                                form.setFieldsValue({
                                                    isPrimaryAddress: true,
                                                    isPrimaryB2BAddress: false,
                                                });
                                            } else {
                                                form.setFieldsValue({
                                                    isPrimaryAddress: false,
                                                });
                                            }
                                        }}
                                    >
                                        Set as Primary Address
                                    </Checkbox>
                                </ConfigProvider>
                            </Form.Item>

                            <div className="flex lg:flex-row   lg:gap-10 @sm:flex-col">
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Address 1"
                                        name="addressLine1"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter address 1',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="Enter address 1" />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Address 2"
                                        name="addressLine2"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter address 2',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="Enter address 2" />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Postal Code"
                                        name="postalCode"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter postal code',
                                            },
                                            {
                                                pattern: /^[0-9]{6}$/,
                                                message:
                                                    'Postal Code must be exactly 6 digits and contain only numbers',
                                            },
                                        ]}
                                    >
                                        <Input
                                            placeholder="Enter postal code"
                                            maxLength={6}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="State"
                                        name="state"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select state',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            placeholder="Select state"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    ?.toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={CountryOptions}
                                            onChange={handleStateChange}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="flex lg:flex-row   lg:gap-10 @sm:flex-col">
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="City"
                                        name="city"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select city',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            disabled={!isSelectedState}
                                            onSearch={(value) =>
                                                setCitySearchText(value)
                                            }
                                            placeholder="Select city"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={CityOptions}
                                        />
                                    </Form.Item>
                                </div>

                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Emergency Contact Person"
                                        name="emergencyContactPerson"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter emergency contact person name',
                                            },
                                        ]}
                                    >
                                        <Input
                                            type="text"
                                            placeholder="Emergency contact person"
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Emergency Mobile Number"
                                        name="emergencyContactPhone"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter emergency mobile number',
                                            },
                                        ]}
                                    >
                                        <Input
                                            type="text"
                                            // maxLength={10}
                                            onInput={(e: any) => {
                                                e.target.value =
                                                    e.target.value.replace(
                                                        /[^0-9]/g,
                                                        ''
                                                    );
                                            }}
                                            placeholder="Emergency mobile number"
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2"></div>
                            </div>
                        </div>
                    )}

                    {/* =========================billing details form===================== */}

                    {activeTab === 'billing' && (
                        <div>
                            <Form.Item
                                name="isPrimaryB2BAddress"
                                valuePropName="checked"
                            >
                                <ConfigProvider
                                    theme={{
                                        token: {
                                            // colorBorder: '#8143D1',
                                            colorPrimary: '#8143D1',
                                            colorPrimaryHover: '#8143D1',
                                        },
                                    }}
                                >
                                    <Checkbox
                                        onChange={(e) => {
                                            if (e.target.checked) {
                                                form.setFieldsValue({
                                                    isPrimaryB2BAddress: true,
                                                    isPrimaryAddress: false,
                                                });
                                            } else {
                                                form.setFieldsValue({
                                                    isPrimaryB2BAddress: false,
                                                });
                                            }
                                        }}
                                    >
                                        Set as Primary Address
                                    </Checkbox>
                                </ConfigProvider>
                            </Form.Item>

                            <div className="flex lg:flex-row   lg:gap-10 @sm:flex-col">
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="GST No."
                                        name="B2bGST"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please enter GST No.',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="Enter GST No." />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Business Name"
                                        name="B2bBusinessName"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter business name.',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="Enter business name" />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Address"
                                        name="B2bAddress1"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter address 1',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="Enter address 1" />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Address 2"
                                        name="B2bAddress2"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter address 2',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="Enter address 2" />
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="flex lg:flex-row   lg:gap-10 @sm:flex-col">
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="State"
                                        name="B2bstate"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select state',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            placeholder="Select state"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    ?.toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={CountryOptions}
                                            onChange={handleBillingStateChange}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="City"
                                        name="B2bcity"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select city',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            disabled={!isB2BSelectedState}
                                            onSearch={(value) =>
                                                setB2bCitySearchText(value)
                                            }
                                            placeholder="Select city"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={BillingCityOptions}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Postal Code"
                                        name="B2bpostalCode"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please enter postal code',
                                            },
                                            {
                                                pattern: /^[0-9]{6}$/,
                                                message:
                                                    'Postal Code must be exactly 6 digits and contain only numbers',
                                            },
                                        ]}
                                    >
                                        <Input
                                            placeholder="Enter postal code"
                                            maxLength={6}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2"></div>
                            </div>
                        </div>
                    )}

                    {/* ===================================polocies========================================= */}
                    <div className="flex gap-8  lg:flex-row @sm:flex-col">
                        {showPolicies && (
                            <div className="border-[rgba(0, 0, 0, 0.1)] ms-3 mt-7 rounded-md border-1 px-8  py-6 lg:w-[40%]">
                                <p className="text-xl font-semibold text-[#1A3353]">
                                    Policies
                                </p>
                                <div className="flex flex-col gap-4  pt-3 ">
                                    <Form.Item
                                        label=""
                                        name="policies"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select policies',
                                            },
                                        ]}
                                    >
                                        <Checkbox.Group
                                            value={selectedPolicies}
                                            onChange={handlePolicyChange}
                                        >
                                            <div className="gap-y-5   lg:grid lg:grid-cols-2 @sm:flex @sm:flex-col @sm:gap-2 ">
                                                {policies &&
                                                    Object.entries(policies)
                                                        .filter(
                                                            ([_, value]) =>
                                                                value === true
                                                        )
                                                        .map(([key]) => (
                                                            <Checkbox
                                                                key={key}
                                                                value={key}
                                                            >
                                                                <p className="text-[#455560]">
                                                                    {formatStringWithSpaces(
                                                                        key
                                                                    )}
                                                                </p>
                                                            </Checkbox>
                                                        ))}
                                            </div>
                                        </Checkbox.Group>
                                    </Form.Item>
                                </div>
                            </div>
                        )}
                        <div className=" lg:w-[25%]">
                            <p className="text-xl text-[#8143D1]">
                                Upload Photo
                            </p>
                            <div className="rounded-lg border   p-5">
                                <Upload
                                    name="avatar"
                                    listType="picture-card"
                                    className="avatar-uploader overflow-hidden"
                                    showUploadList={false}
                                    // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                                    // beforeUpload={beforeUpload}
                                    // onChange={handleChange}
                                    customRequest={handleImageUpload}
                                >
                                    {imageUrl ? (
                                        <div className="relative h-full w-full">
                                            <img
                                                src={imageUrl}
                                                className="object-contain"
                                                alt="avatar"
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                }}
                                            />
                                            {loading && (
                                                <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/50">
                                                    <LoadingOutlined
                                                        style={{
                                                            fontSize: 24,
                                                            color: '#8143D1',
                                                        }}
                                                        spin
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        uploadButton
                                    )}
                                </Upload>
                            </div>
                        </div>
                        {/* ===================================save buttons========================================= */}
                        <div className="flex items-end justify-center gap-5 lg:w-[35%] lg:flex-row">
                            {!isAdded && (
                                <Form.Item>
                                    <Button
                                        // type="default "
                                        loading={assignloader}
                                        onClick={handleSaveAndRedirect}
                                        className="border-1 border-[#1A3353] px-8 py-7 text-xl  text-[#1A3353]"
                                    >
                                        Save and Assign Package
                                    </Button>
                                </Form.Item>
                            )}
                            <Form.Item>
                                <Button
                                    loading={loader}
                                    type="primary"
                                    className="bg-purpleLight px-14 py-7 text-xl  "
                                    htmlType="submit"
                                >
                                    Save
                                </Button>
                            </Form.Item>
                        </div>
                    </div>
                </Form>
            </ConfigProvider>
        </Modal>
    );
};

export default AddClientModal;

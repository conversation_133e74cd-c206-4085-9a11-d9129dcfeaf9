import {
    Button,
    ConfigProvider,
    Form,
    FormProps,
    Input,
    Modal,
    Select,
} from 'antd';
import { useEffect, useState } from 'react';
import AddClientModal from './add-client-modal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { PricingListingForSharePass } from '~/redux/actions/appointment-action';
import { capitalizeFirstLetter } from '~/components/common/function';
import {
    CustomerList,
    sharePassToOther,
} from '~/redux/actions/customer-action';
import { useDebounce } from '~/hooks/useDebounce';

interface SharePassModalProps {
    visible: boolean;
    onClose: (val?: boolean) => void;
    clientId?: string;
    purchaseId?: string;
    clientName?: string;
    bundledPricingId?: string;
    packageId?: string;
    invoiceId?: string;
}

const SharePassModal = (props: SharePassModalProps) => {
    const [showAddClientModal, setShowAddClientModal] = useState(false);
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    const [searchText, setSearchText] = useState('');
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const [disabledFields, setDisabledFields] = useState({
        username: false,
        clientID: false,
        phone: false,
    });

    const [availableSessions, setAvailableSessions] = useState(0);
    const [pricingOption, setPricingOption] = useState([]);

    const store = useAppSelector((state) => ({
        customerListForForms: state.customer_store.customerListForForms,
    }));

    useEffect(() => {
        dispatch(
            PricingListingForSharePass({
                userId: props.clientId,
                bundledPricingId: props.bundledPricingId,
                invoiceId: props.invoiceId,
            })
        )
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    let pricingList = res.data?.data?.map((item: any) => ({
                        value: item._id,
                        label: item.packageName,
                        id: item._id,
                        packageId: item.packageId,
                        ...item,
                    }));
                    if (props.packageId) {
                        pricingList = pricingList?.filter(
                            (option: any) =>
                                option.packageId === props.packageId
                        );
                    } else if (props.purchaseId)
                        pricingList = pricingList?.filter(
                            (option: any) => option._id === props.purchaseId
                        );
                    setPricingOption(pricingList);
                    if (props.purchaseId || props.packageId) {
                        const packageData = pricingList?.[0] || {};
                        setAvailableSessions(
                            packageData?.remainingSessions || 0
                        );
                        form.setFieldsValue({
                            package: packageData._id,
                            availableSessions: packageData.remainingSessions,
                        });
                    }
                }
            });
    }, []);

    const ClientOptions = store.customerListForForms
        ?.filter((item: any) => item.userId !== props?.clientId)
        ?.map((item: any) => ({
            value: item.userId,
            label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
            id: item._id,
            ...item,
        }));

    const handlePackageChange = (packageId: string, option: any) => {
        form.setFieldsValue({
            availableSessions: option?.remainingSessions,
            transferSessions: undefined,
        });
        setAvailableSessions(option?.remainingSessions || 0);
    };

    const openModal = () => {
        setShowAddClientModal(true);
    };

    const closeModal = () => {
        setShowAddClientModal(false);
    };

    useEffect(() => {
        if (searchText) {
            debouncedRequest(() => {
                dispatch(
                    CustomerList({
                        page: 1,
                        pageSize: 30,
                        search: searchText,
                        isActive: true,
                        notIncludedClientId: props?.clientId,
                    })
                ).then((res: any) => {
                    if (res?.payload?.data?.data?.count === 1) {
                        form.setFieldsValue({
                            clientId:
                                res?.payload?.data?.data?.list?.[0]?.clientId,
                            shareTo:
                                res?.payload?.data?.data?.list?.[0]?.userId,
                            phone: res?.payload?.data?.data?.list?.[0]?.mobile,
                        });
                        setDisabledFields({
                            username: false,
                            clientID: true,
                            phone: false,
                        });
                    }
                });
            });
        } else {
            dispatch(
                CustomerList({
                    page: 1,
                    pageSize: 30,
                    isActive: true,
                    notIncludedClientId: props?.clientId,
                })
            );
        }
    }, [searchText]);

    const handleSearch = (value: string) => {
        debouncedRequest(() => {
            dispatch(
                CustomerList({
                    page: 1,
                    pageSize: 30,
                    search: value,
                    isActive: true,
                    notIncludedClientId: props?.clientId,
                })
            );
        });
    };

    const validateTransferSessions = (_: any, value: any) => {
        const availableSessions = form.getFieldValue('availableSessions') || 0;
        if (value > availableSessions) {
            return Promise.reject(
                `You can't transfer more than ${availableSessions} sessions!`
            );
        }
        return Promise.resolve();
    };

    const handleShareToSelect = (userId: string) => {
        const selectedUser: any = store.customerListForForms?.find(
            (user: any) => user.userId === userId
        );
        if (selectedUser) {
            form.setFieldsValue({
                clientId: selectedUser.clientId,
                phone: selectedUser.mobile,
            });
            setDisabledFields({ username: false, clientID: true, phone: true });
        }
    };

    const handleFieldChange = (field: string, value: string) => {
        if (value) {
            setDisabledFields((prev) => ({
                username: field !== 'username',
                clientID: field !== 'clientID',
                phone: field !== 'phone',
            }));
            setSearchText(value);
        } else {
            setDisabledFields({
                username: false,
                clientID: false,
                phone: false,
            });
        }
    };

    const onFinish: FormProps['onFinish'] = (values) => {
        const payload = {
            shareFrom: props?.clientId,
            shareTo: values.shareTo,
            purchaseId: values.package,
            noOfSessions: Number(values.transferSessions),
        };
        dispatch(sharePassToOther({ payload }))
            .unwrap()
            .then((res: any) => {
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    form.resetFields();
                    props.onClose(true);
                }
            });
    };

    const handleClientAdded = (newClient: any) => {
        dispatch(
            CustomerList({
                page: 1,
                pageSize: 30,
                isActive: true,
                notIncludedClientId: props?.clientId,
            })
        );

        form.setFieldsValue({
            shareTo: newClient.userId,
            clientId: newClient.clientId,
            phone: newClient.mobile,
        });

        setDisabledFields({ username: false, clientID: true, phone: true });

        setShowAddClientModal(false);
    };

    return (
        <div>
            <Modal
                title="Share User's Pass"
                open={props.visible}
                onCancel={() => props.onClose()}
                footer={null}
                centered
                className="w-[35%]"
            >
                <div>
                    <ConfigProvider
                        theme={{
                            components: {
                                Form: {
                                    verticalLabelMargin: -3,
                                },
                            },
                        }}
                    >
                        <Form
                            name="sharePass"
                            form={form}
                            layout="vertical"
                            // size="medium"
                            initialValues={{
                                name: props.clientName,
                            }}
                            onFinish={onFinish}
                            className=" "
                        >
                            <Form.Item
                                label="Share From"
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter name!',
                                    },
                                ]}
                                className="w-[100%] pt-10"
                            >
                                <Input
                                    maxLength={50}
                                    placeholder="Enter Name"
                                    className="h-14"
                                    disabled
                                />
                            </Form.Item>
                            <div className="flex w-[100%] flex-row items-center gap-3">
                                <Form.Item
                                    label="Select Package"
                                    name="package"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select package',
                                        },
                                    ]}
                                    className="w-[50%] py-8"
                                >
                                    <Select
                                        showSearch
                                        // className="w-[50%]"
                                        placeholder="Select Package"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        disabled={
                                            props.purchaseId ||
                                            (props.packageId &&
                                                pricingOption.length === 1)
                                        }
                                        options={pricingOption}
                                        onChange={(item, option) =>
                                            handlePackageChange(item, option)
                                        }
                                    />
                                </Form.Item>
                                <Form.Item
                                    label="Available No. of Sessions"
                                    name="availableSessions"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter Available No. of Sessions',
                                        },
                                    ]}
                                    className="w-[50%]"
                                >
                                    <Input
                                        disabled
                                        maxLength={50}
                                        placeholder="Available No. of Sessions"
                                        className="h-14"
                                    />
                                </Form.Item>
                            </div>
                            <div className="flex w-[100%] flex-row items-center gap-3">
                                <Form.Item
                                    label="Share to"
                                    name="shareTo"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select name',
                                        },
                                    ]}
                                    className="w-[33.33%]"
                                >
                                    <Select
                                        showSearch
                                        allowClear
                                        placeholder="Select name"
                                        className="h-14"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={ClientOptions}
                                        onSearch={handleSearch}
                                        onChange={handleShareToSelect}
                                        // disabled={disabledFields.username}
                                    />
                                </Form.Item>
                                {/* <Form.Item
                                        label="Client ID"
                                        name="clientId"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select ID',
                                            },
                                        ]}
                                        className="w-[33.33%]"
                                    >
                                        <Input
                                            onChange={(e) =>
                                                handleFieldChange(
                                                    'clientID',
                                                    e.target.value
                                                )
                                            }
                                            // disabled={disabledFields.clientID}
                                            placeholder="Add client ID"
                                            className="h-14"
                                        />
                                    </Form.Item> */}
                                <Form.Item
                                    label="Phone No."
                                    name="phone"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select phone no.',
                                        },
                                    ]}
                                    className="w-[33.33%]"
                                >
                                    <Input
                                        onChange={(e) =>
                                            handleFieldChange(
                                                'phone',
                                                e.target.value
                                            )
                                        }
                                        // disabled={disabledFields.phone}
                                        placeholder="Add phone no."
                                        className="h-14"
                                    />
                                </Form.Item>
                                <Button
                                    onClick={openModal}
                                    className="h-14 w-[33.33%] translate-y-2 bg-purpleLight text-white"
                                >
                                    Add Customer
                                </Button>
                            </div>

                            <Form.Item
                                label="No. of Sessions to Transfer"
                                name="transferSessions"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter No. of Sessions to Transfer!',
                                    },
                                    { validator: validateTransferSessions },
                                ]}
                                className="w-[8 0%]"
                            >
                                {/* <Input
                                    type="text"
                                    maxLength={50}
                                    onInput={(e: any) => {
                                        e.target.value = e.target.value.replace(
                                            /[^0-9]/g,
                                            ''
                                        );
                                    }}
                                    placeholder="Enter No. of Sessions to Transfer"
                                    className="h-14"
                                /> */}
                                <Select
                                    placeholder="Select No. of Sessions"
                                    options={Array.from(
                                        { length: availableSessions },
                                        (_, i) => ({
                                            value: i + 1,
                                            label: (i + 1).toString(),
                                        })
                                    )}
                                />
                            </Form.Item>
                            <div className="flex w-[100%] justify-end pt-5">
                                <Form.Item>
                                    <Button
                                        htmlType="submit"
                                        className="h-14 bg-purpleLight text-white"
                                    >
                                        Submit
                                    </Button>
                                </Form.Item>
                            </div>
                        </Form>
                    </ConfigProvider>
                </div>
            </Modal>
            {showAddClientModal && (
                <AddClientModal
                    open={showAddClientModal}
                    onClose={closeModal}
                    isAdded={true}
                    onClientAdded={handleClientAdded}
                />
            )}
        </div>
    );
};

export default SharePassModal;

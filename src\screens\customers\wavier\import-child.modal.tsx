import { Mo<PERSON>, <PERSON>, Button, ConfigProvider, Checkbox } from 'antd';
import { useEffect, useState } from 'react';
import {
    customerLeadDetail,
    customerMinorList,
} from '~/redux/actions/customerLead-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';

const ImportChildFromWavierModal = ({
    open,
    onCancel,
    clientSourceId,
    onProceed,
    selectedMinors = [],
}: any) => {
    const dispatch = useAppDispatch();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const [minorData, setMinorData] = useState<any[]>([]);

    useEffect(() => {
        if (clientSourceId) {
            startLoader();
            dispatch(customerMinorList(clientSourceId))
                .then((res: any) => {
                    const subClientList = res?.payload?.data?.minors?.map(
                        (item: any) => ({
                            key: item._id,
                            _id: item._id,
                            firstName: item.firstName,
                            lastName: item.lastName,
                            dob: item.dob
                                ? new Date(item.dob).toLocaleDateString()
                                : 'NA',
                            gender: item.gender,
                            dateRequested: res.payload.data.createdAt
                                ? new Date(
                                      res.payload.data.createdAt
                                  ).toLocaleDateString()
                                : 'NA',
                            raw: item,
                        })
                    );
                    if (subClientList?.length > 0) {
                        setMinorData(subClientList);
                        const previouslySelectedIds = selectedMinors.map(
                            (m: any) => m._id
                        );
                        setSelectedIds(previouslySelectedIds);
                    }
                })
                .catch(console.error)
                .finally(() => endLoader());
        }
    }, [clientSourceId]);

    const handleCheckboxChange = (e: any, recordId: string) => {
        setSelectedIds((prev) => {
            const updated = new Set(prev);
            if (e.target.checked) {
                updated.add(recordId);
            } else {
                updated.delete(recordId);
            }
            return Array.from(updated);
        });
    };

    const isAllSelected =
        minorData.length > 0 && selectedIds.length === minorData.length;
    const isIndeterminate =
        selectedIds.length > 0 && selectedIds.length < minorData.length;

    const handleSelectAll = (e: any) => {
        if (e.target.checked) {
            const allIds = minorData.map((item) => item.key);
            setSelectedIds(allIds);
        } else {
            setSelectedIds([]);
        }
    };

    const columns = [
        {
            title: (
                <>
                    <div className="flex items-center gap-2">
                        <Checkbox
                            checked={isAllSelected}
                            indeterminate={isIndeterminate}
                            onChange={handleSelectAll}
                            style={{ accentColor: '#a259ff' }}
                        />
                        <span className="font-semibold">Name</span>
                    </div>
                </>
            ),
            dataIndex: '',
            render: (_: any, record: any) => (
                <ConfigProvider
                    theme={{
                        token: {
                            colorPrimary: 'primary',
                        },
                    }}
                >
                    <Checkbox
                        checked={selectedIds.includes(record.key)}
                        onChange={(e) => handleCheckboxChange(e, record.key)}
                    >
                        <span className="text-gray-800">
                            {record.firstName} {record.lastName}
                        </span>
                    </Checkbox>
                </ConfigProvider>
            ),
        },
        {
            title: 'GENDER',
            dataIndex: 'gender',
        },
        {
            title: 'D.O.B.',
            dataIndex: 'dob',
        },
        {
            title: 'DATE REQUESTED',
            dataIndex: 'dateRequested',
        },
    ];

    const handleProceed = () => {
        const selectedData = minorData
            .filter((row) => selectedIds.includes(row.key))
            .map((d) => d.raw);
        onProceed?.(selectedData);
    };

    return (
        <Modal
            open={open}
            onCancel={onCancel}
            footer={null}
            centered
            closable={false}
            className="rounded-xl"
            width={700}
        >
            <h2 className="mb-4 text-2xl font-semibold text-[#1a3353]">
                Import Client From Waiver
            </h2>

            <Table
                columns={columns}
                dataSource={minorData}
                pagination={false}
                className="rounded-xl border"
                rowClassName={(record) =>
                    selectedIds.includes(record.key)
                        ? '!bg-white !hover:bg-white'
                        : 'hover:bg-gray-50'
                }
            />

            <div className="mt-6 flex justify-end gap-4">
                <Button
                    onClick={onCancel}
                    className="border-[#1A3353] !text-[#1A3353] hover:!text-[#1A3353]"
                >
                    Cancel
                </Button>
                <Button
                    type="primary"
                    className="bg-purpleLight text-white "
                    loading={loader}
                    onClick={handleProceed}
                    disabled={selectedIds.length === 0}
                >
                    Proceed
                </Button>
            </div>
        </Modal>
    );
};

export default ImportChildFromWavierModal;

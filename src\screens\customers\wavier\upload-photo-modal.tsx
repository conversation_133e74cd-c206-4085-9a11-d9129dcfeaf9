import React, { useState } from 'react';
import { Modal, Upload, message, Spin } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { UploadImage } from '~/redux/actions/common-action';
import { useAppDispatch } from '~/hooks/redux-hooks';

interface UploadPhotoModalProps {
  open: boolean;
  onCancel: () => void;
  onSave: (imageUrl: string) => void;
}

const UploadPhotoModal: React.FC<UploadPhotoModalProps> = ({
  open,
  onCancel,
  onSave,
}) => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('Only image files are allowed!');
    }
    return isImage || Upload.LIST_IGNORE;
  };

  const handleUpload = async ({ file }: any) => {
    setLoading(true);
    try {
      const res: any = await dispatch(UploadImage({ file }));
      const url = res?.payload?.res?.data?.data;
      if (url) {
        setImageUrl(url);
      } else {
        message.error('Upload failed!');
      }
    } catch (e) {
      message.error('Upload error!');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      onCancel={onCancel}
      footer={null}
      title="Upload Sub-Client Photo"
      centered
      width={400}
      bodyStyle={{ padding: 24, borderRadius: 8 }}
    >
      <div className="flex flex-col items-center justify-center">
        <Upload
          accept="image/*"
          listType="picture-card"
          showUploadList={false}
          customRequest={handleUpload}
          beforeUpload={beforeUpload}
        >
          {imageUrl ? (
            <img
              src={imageUrl}
              alt="avatar"
              style={{ width: '100%', borderRadius: '10px', objectFit: 'cover' }}
            />
          ) : (
            <div style={{ padding: 10 }}>{loading ? <Spin /> : <><PlusOutlined /><div style={{ marginTop: 8 }}>Upload</div></>}</div>
          )}
        </Upload>

        <div className="flex justify-end w-full gap-4 mt-6">
          <button
            onClick={onCancel}
            className="border border-gray-400 px-4 py-2 rounded-md hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            onClick={() => {
                imageUrl && onSave(imageUrl)}}
            disabled={!imageUrl}
            className={`px-4 py-2 rounded-md text-white ${
              imageUrl ? 'bg-purple-500 hover:bg-purple-500' : 'bg-purple-500 hover:bg-purple-500 cursor-not-allowed'
            }`}
          >
            Save
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default UploadPhotoModal;

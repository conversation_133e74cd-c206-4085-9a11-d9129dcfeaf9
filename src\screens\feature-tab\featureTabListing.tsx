import React, { useState, useEffect } from 'react';
import { Menu, Dropdown, Switch, ConfigProvider, Pagination } from 'antd';
import { MoreOutlined } from '@ant-design/icons';
import CommonTable from '~/components/common/commonTable';
import {
    featureListing,
    deleteFeature,
    updateFeatureStatus,
} from '~/redux/actions/feature-action';
import { useLoader } from '~/hooks/useLoader';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { useAppSelector } from '~/hooks/redux-hooks';
import DeleteModal from '~/components/common/deleteModal';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import clsx from 'clsx';
import { useLocation } from 'wouter';

const columns = [
    {
        title: 'Names',
        dataIndex: 'name',
        // width: "25%",
    },
];
interface DataSourceItem {
    key: number;
    name: string;
    status: boolean;
}

const subCategoryListing: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();
    const [loader, startLoader, endLoader] = useLoader();
    const [currentRecord, setCurrentRecord] = useState<any>(null);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState<boolean>(false);
    const [isDeleteModalVisible, setDeleteIsModalVisible] =
        useState<boolean>(false);
    const [featureTabId, setFeatureTabId] = useState<any>(null);
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [location, setLocation] = useLocation();

    const handleStatusChange = (record: any) => {
        setCurrentRecord(record);
        setConfirmationModalVisible(true);
    };
    const editFeatureTab = (record: any) => {
        setLocation(`/setting/feature-tab/create-feature/${record._id}`);
    };
    useEffect(() => {
        startLoader();
        dispatch(featureListing({}))
            .unwrap()
            .then(() => {
                console.log('first');
            })
            .finally(endLoader);
    }, []);
    const store = useAppSelector((state) => ({
        roomList: state.fetaureTab_store.featureList,
    }));
    const dataSource: DataSourceItem[] = store.roomList.map(
        (rate: any, i: number) => ({
            key: i,
            _id: rate._id,
            name: rate.name,
            status: rate.isActive,
        })
    );
    const selectColumn = [
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.status ? 'bg-switch-on' : 'bg-switch-off'
                            )}
                            id="swtich-off"
                            checkedChildren="ON"
                            // className="bg-[#D0D4D7]"
                            unCheckedChildren="OFF"
                            onChange={() => handleStatusChange(record)}
                            checked={record.status || false}
                        />
                    </ConfigProvider>
                );
            },
        },
    ];
    const deleteFeatureData = () => {
        if (featureTabId) {
            dispatch(deleteFeature(featureTabId));
        }
        setDeleteIsModalVisible(false);
    };
    const actionColumn = [
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => {
                // console.log("record----------", record)
                const menu = (
                    <Menu>
                        <Menu.Item
                            key="1"
                            onClick={() => editFeatureTab(record)}
                        >
                            Edit
                        </Menu.Item>
                        <Menu.Item
                            key="2"
                            onClick={() => {
                                setDeleteIsModalVisible(true);
                                setFeatureTabId(record._id);
                            }}
                        >
                            Delete
                        </Menu.Item>
                    </Menu>
                );

                return (
                    <Dropdown
                        overlay={menu}
                        trigger={['click']}
                        visible={openDropdownKey === record.key}
                        onOpenChange={(visible) => {
                            setOpenDropdownKey(visible ? record.key : null);
                        }}
                    >
                        <MoreOutlined
                            style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                            }}
                        />
                    </Dropdown>
                );
            },
        },
    ];

    const handleConfirmStatusChange = () => {
        if (currentRecord)
            dispatch(
                updateFeatureStatus({
                    featureId: currentRecord?._id,
                    isActive: !currentRecord.status,
                })
            );
        setConfirmationModalVisible(false);
    };

    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };
    const combinedColumns = [...columns, ...selectColumn, ...actionColumn];

    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={combinedColumns}
                dataSource={dataSource}
                loading={loader}
                bulkAction={false}
                backButton={true}
                heading="Feature Tab"
                addNewLink="/setting/feature-tab/create-feature/0"
                addNewTitle="Create Feature Tabs"
            />
            {isDeleteModalVisible && (
                <DeleteModal
                    title="Confirm Delete"
                    message={`Do you want to delete this Feature?`}
                    isVisible={isDeleteModalVisible}
                    onDelete={deleteFeatureData}
                    onCancel={() => {
                        setDeleteIsModalVisible(false);
                    }}
                />
            )}
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
        </>
    );
};
export default subCategoryListing;

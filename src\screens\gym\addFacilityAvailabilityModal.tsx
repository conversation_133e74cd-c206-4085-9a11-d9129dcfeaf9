import React, { useState } from 'react';
import {
    Modal,
    Select,
    Radio,
    DatePicker,
    TimePicker,
    Form,
    Button,
    Row,
    Col,
    Checkbox,
} from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { AddFacilityUnavailabity } from '~/redux/actions/facility-action';

import { useForm } from 'antd/es/form/Form';
import {
    CloseOutlined,
    PlusOutlined,
    PlusSquareOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import WorkingHours from './workingHours';

const { Option } = Select;

interface IFacility {
    _id: string;
    facilityName: string;
}

interface ScheduleModalProps {
    visible: boolean;
    onClose: () => void;
}

interface TimeSlot {
    from: any;
    to: any;
}

const AddFacilityAvailabilityModal: React.FC<ScheduleModalProps> = ({
    visible,
    onClose,
}) => {
    const dispatch = useAppDispatch();
    const [dateRange, setDateRange] = useState<'single' | 'multiple'>('single');
    const [startDate, setStartDate] = useState<any>(null);
    const [selectedDay, setSelectedDay] = useState(null);
    const [endDate, setEndDate] = useState<any>(null);
    const [unavailabilityTime, setUnavailabilityTime] = useState<TimeSlot[]>([
        { from: null, to: null },
    ]);
    const [selectedFacilities, setSelectedFacilities] = useState<string[]>([]);

    const [form] = useForm();

    const facilityList = useAppSelector(
        (state) => state.facility_store.facilityList
    );
    const facilityUnavailabilityDetails = useAppSelector(
        (state) => state.facility_store.facilityUnavailabilityDetails
    );
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const handleDayClick = (day: any) => {
        setSelectedDay(day); // Update the selected day
    };

    console.log(
        'facilityUnavailabilityDetails------------',
        facilityUnavailabilityDetails
    );

    const handleFacilityChange = (value: string[]) => {
        if (value.includes('all')) {
            setSelectedFacilities(
                facilityList?.map((facility: any) => facility._id)
            );
        } else {
            setSelectedFacilities(value);
        }
    };

    const addTimeSlot = () => {
        setUnavailabilityTime([
            ...unavailabilityTime,
            {
                from: null,
                to: null,
            },
        ]);
    };

    // Function to remove a time slot
    const removeTimeSlot = (timeIndex: number) => {
        const newTimeSlots = unavailabilityTime.filter(
            (_: any, idx: number) => idx !== timeIndex
        );
        setUnavailabilityTime(newTimeSlots);
    };

    const getDisabledEndHours = (index: number) => {
        const startTime = dayjs(unavailabilityTime[index]?.from);
        const startHour = startTime.hour();
        if (!startTime.isValid() || startHour === null) return [];
        const startMinute = startTime.minute();

        return Array.from({ length: 24 }, (_, i) => i).filter(
            (hour) => hour <= (startMinute < 59 ? startHour - 1 : startHour)
        );
    };

    const getDisabledEndMinutes = (selectedHour: any, index: number) => {
        const startTime = unavailabilityTime[index].from
            ? dayjs(unavailabilityTime[index].from)
            : null;
        if (!startTime) return [];
        const startHour = startTime.hour();
        const startMinute = startTime.minute();

        return selectedHour === startHour
            ? Array.from({ length: 60 }, (_, i) => i).filter(
                  (minute) => minute <= startMinute
              )
            : [];
    };

    const renderTimeSlots = () => {
        return (
            <>
                <div className="mb-7 flex w-full flex-col gap-3 rounded-xl border  px-5 py-4">
                    {unavailabilityTime.map((slot, index) => (
                        <div
                            className="flex flex-row items-center lg:w-[100%]    "
                            key={`time-slot-${index}`}
                        >
                            <div className="w-[100%]">
                                <div className="flex gap-4  lg:w-[100%] lg:flex-row lg:items-center">
                                    <div className="lg:w-[100%]">
                                        <Form.Item label="Opening Time">
                                            <TimePicker
                                                style={{ width: '100%' }}
                                                format="HH:mm"
                                                value={
                                                    slot.from
                                                        ? dayjs(
                                                              slot.from,
                                                              'HH:mm'
                                                          )
                                                        : null
                                                }
                                                onChange={(time) => {
                                                    const newSlots = [
                                                        ...unavailabilityTime,
                                                    ];
                                                    newSlots[index] = {
                                                        ...newSlots[index],
                                                        from: time,
                                                    };
                                                    setUnavailabilityTime(
                                                        newSlots
                                                    );
                                                }}
                                            />
                                        </Form.Item>
                                    </div>
                                    <div className="lg:w-[100%]">
                                        <Form.Item label="Closing Time">
                                            <TimePicker
                                                style={{ width: '100%' }}
                                                format="HH:mm"
                                                value={
                                                    slot.to
                                                        ? dayjs(
                                                              slot.to,
                                                              'HH:mm'
                                                          )
                                                        : null
                                                }
                                                onChange={(time) => {
                                                    const newSlots = [
                                                        ...unavailabilityTime,
                                                    ];
                                                    newSlots[index] = {
                                                        ...newSlots[index],
                                                        to: time,
                                                    };
                                                    setUnavailabilityTime(
                                                        newSlots
                                                    );
                                                }}
                                                disabled={!slot.from}
                                                disabledTime={() => ({
                                                    disabledHours: () =>
                                                        getDisabledEndHours(
                                                            index
                                                        ),
                                                    disabledMinutes: (
                                                        selectedHour
                                                    ) =>
                                                        getDisabledEndMinutes(
                                                            selectedHour,
                                                            index
                                                        ),
                                                })}
                                            />
                                        </Form.Item>
                                    </div>
                                </div>
                                <Checkbox>
                                    Duplicate this slot for all days
                                </Checkbox>
                            </div>
                            {index > 0 && (
                                <CloseOutlined
                                    onClick={() => removeTimeSlot(index)}
                                    style={{
                                        marginLeft: '8px',
                                        cursor: 'pointer',
                                    }}
                                    title="Remove time slot"
                                />
                            )}
                        </div>
                    ))}
                </div>

                <div className="flex justify-center pt-5">
                    <Form.Item>
                        <Button
                            type="default"
                            icon={<PlusOutlined />}
                            onClick={() => addTimeSlot()}
                            // onClick={handleAddTimeSlot}
                        >
                            Add Shift
                        </Button>
                    </Form.Item>
                </div>
            </>
        );
    };

    const handleModalClose = () => {
        form.resetFields();
        setStartDate(null);
        setEndDate(null);
        setUnavailabilityTime([]);
        setSelectedFacilities([]);
        onClose();
    };

    const handleSubmit = (data: any) => {
        const payload = {
            facility: selectedFacilities,
            type: 'unavailable',
            fromDate: startDate
                ? startDate.startOf('day').format('YYYY-MM-DD HH:mm:ss')
                : '',
            endDate: endDate
                ? endDate.endOf('day').format('YYYY-MM-DD HH:mm:ss')
                : '',
            time: unavailabilityTime.map((slot) => ({
                from: slot.from ? slot.from.format('HH:mm') : '',
                to: slot.to ? slot.to.format('HH:mm') : '',
            })),
        };
        dispatch(AddFacilityUnavailabity(payload))
            .unwrap()
            .then(() => {
                handleModalClose();
            });
    };

    return (
        <Modal
            title={
                <div className="border-b-2 text-[#1A3353]">
                    Add Facility Unavailability
                </div>
            }
            open={visible}
            onCancel={handleModalClose}
            footer={null}
            width={800}
            style={{ top: 10 }}
        >
            <Form
                className="px-8 pt-10"
                name="schedule-form"
                layout="vertical"
                size="large"
                onFinish={handleSubmit}
                form={form}
            >
                <Form.Item
                    label="Location"
                    name="facilityId"
                    rules={[
                        {
                            required: true,
                            message: 'Please select Location',
                        },
                    ]}
                >
                    <Select
                        placeholder="Enter the Location"
                        value={selectedFacilities}
                        onChange={handleFacilityChange}
                        filterOption={(input, option) =>
                            (option?.children as unknown as string)
                                .toLowerCase()
                                .includes(input.toLowerCase())
                        }
                    >
                        <Option key="all" value="all">
                            All
                        </Option>
                        {facilityList.map((facility: IFacility) => (
                            <Option key={facility._id} value={facility._id}>
                                {facility.facilityName}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    label="Date Range"
                    name="dateRange"
                    rules={[
                        {
                            required: false,
                            message: 'Please select date range',
                        },
                    ]}
                >
                    <Radio.Group
                        value={dateRange}
                        defaultValue={dateRange}
                        onChange={(e) => {
                            setDateRange(e.target.value);
                            if (e.target.value === 'single') {
                                setEndDate(startDate);
                                form.setFieldsValue({
                                    endDate: startDate,
                                });
                            }
                        }}
                    >
                        <Radio value="single">Single</Radio>
                        <Radio value="multiple">Multiple</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item
                    label="Working Hours"
                    className="pb-5 pt-3"
                    name="workinghours"
                    rules={[
                        {
                            required: false,
                            message: 'Please enter working hours !',
                        },
                    ]}
                >
                    <div className="flex flex-row gap-5">
                        {days.map((day) => (
                            <div key={day} className="day-button">
                                <Button
                                    shape="circle"
                                    className={`p-2 hover:border-checkbox-checked  ${
                                        selectedDay === day
                                            ? 'bg-checkbox-checked text-white'
                                            : 'bg-white'
                                    }`}
                                    onClick={() => handleDayClick(day)}
                                >
                                    {day}
                                </Button>
                            </div>
                        ))}
                    </div>
                </Form.Item>
                <div className="mb-4">
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item label="Start Date" name="startDate">
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    style={{ width: '100%' }}
                                    placeholder="DD/MM/YYYY"
                                    format="DD/MM/YYYY"
                                    disabledDate={(currentDate) =>
                                        currentDate &&
                                        currentDate.isBefore(
                                            dayjs().startOf('day')
                                        )
                                    }
                                    onChange={(date) => {
                                        setStartDate(date);
                                        if (dateRange === 'single') {
                                            setEndDate(date);
                                            form.setFieldsValue({
                                                endDate: date,
                                            });
                                        }
                                    }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label="End Date" name="endDate">
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    placeholder="DD/MM/YYYY"
                                    format="DD/MM/YYYY"
                                    style={{ width: '100%' }}
                                    disabledDate={(currentDate) =>
                                        currentDate &&
                                        currentDate.isBefore(
                                            dayjs().startOf('day')
                                        )
                                    }
                                    onChange={(date) =>
                                        dateRange === 'multiple' &&
                                        setEndDate(date)
                                    }
                                    value={endDate}
                                    disabled={dateRange === 'single'}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    {renderTimeSlots()}
                </div>

                <div className="flex flex-row justify-end gap-5">
                    <Button
                        type="default"
                        className="border border-[#1A3353] px-20 py-7 text-2xl"
                        onClick={onClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        className="bg-purpleLight px-20 py-7 text-2xl text-white"
                        htmlType="submit"
                    >
                        Save
                    </Button>
                </div>
            </Form>
        </Modal>
    );
};

export default AddFacilityAvailabilityModal;

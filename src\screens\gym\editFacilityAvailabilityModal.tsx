import React, { useEffect, useState } from 'react';
import {
    Modal,
    Select,
    Radio,
    DatePicker,
    TimePicker,
    Form,
    Button,
    Row,
    Col,
} from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    UpdateAllFacilityUnavailabilities,
    DeleteFacilityUnavailability,
    UpdateFacilityUnavailability,
    FacilityUnavailabilityDetails,
} from '~/redux/actions/facility-action';

import { useForm } from 'antd/es/form/Form';
import { CloseOutlined, PlusSquareOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import DeleteModal from '~/components/common/deleteModal';
import { formatDate } from '~/utils/formatDate';
import { clearFacilityUnavailabilityDetails } from '~/redux/slices/facility-slice';

const { Option } = Select;

interface IFacility {
    _id: string;
    facilityName: string;
}

interface ScheduleModalProps {
    visible: boolean;
    onClose: () => void;
}

interface TimeSlot {
    _id: string;
    organizationId: string;
    facilityId: string;
    type: string;
    fromDate: string;
    endDate: string;
    time: any;
}

const EditFacilityAvailabilityModal: React.FC<ScheduleModalProps> = ({
    visible,
    onClose,
}) => {
    const dispatch = useAppDispatch();
    const [dateRange, setDateRange] = useState<'single' | 'multiple'>('single');
    const [startDate, setStartDate] = useState<any>(null);
    const [endDate, setEndDate] = useState<any>(null);
    const [selectedDay, setSelectedDay] = useState(null);

    const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
    const [selectedFacility, setSelectedFacility] = useState<string>('');
    const [isDeleteModalVisible, setDeleteIsModalVisible] =
        useState<boolean>(false);
    const [unavailabilityData, setUnavailabilityData] = useState<any>();

    const [form] = useForm();

    const facilityList = useAppSelector(
        (state) => state.facility_store.facilityList
    );

    const facilityUnavailabilityDetails = useAppSelector(
        (state) => state.facility_store.facilityUnavailabilityDetails
    );

    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const handleDayClick = (day: any) => {
        setSelectedDay(day); // Update the selected day
    };

    console.log(
        'facilityUnavailabilityDetails------------',
        facilityUnavailabilityDetails
    );

    useEffect(() => {
        if (
            facilityUnavailabilityDetails &&
            facilityUnavailabilityDetails?.unAvailabilities
        )
            setTimeSlots(facilityUnavailabilityDetails?.unAvailabilities || []);
    }, [facilityUnavailabilityDetails]);

    useEffect(() => {
        if (startDate && endDate && selectedFacility) {
            const payload = {
                fromDate: dayjs(startDate)
                    .startOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                endDate: dayjs(endDate)
                    .endOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                facilityId: selectedFacility,
            };
            dispatch(FacilityUnavailabilityDetails({ payload: payload }));
        }
    }, [startDate, endDate, selectedFacility]);

    const deleteSlot = () => {
        dispatch(
            DeleteFacilityUnavailability({
                _id: unavailabilityData._id,
                facility: unavailabilityData?.facilityId,
            })
        );
        setDeleteIsModalVisible(false);
        setUnavailabilityData({});
    };

    const addTimeSlot = (index: number) => {
        const newTimeSlots = timeSlots.map((slot, i) =>
            i === index
                ? {
                      ...slot,
                      time: [
                          ...slot.time,
                          {
                              from: null,
                              to: null,
                          },
                      ],
                  }
                : slot
        );
        setTimeSlots(newTimeSlots);
    };

    // Function to remove a time slot
    const removeTimeSlot = (slotIndex: number, timeIndex: number) => {
        const newTimeSlots = timeSlots.map((slot, i) =>
            i === slotIndex
                ? {
                      ...slot,
                      time: slot.time.filter(
                          (_: any, idx: number) => idx !== timeIndex
                      ),
                  }
                : slot
        );
        setTimeSlots(newTimeSlots);
    };

    const handleModalClose = () => {
        form.resetFields();
        dispatch(clearFacilityUnavailabilityDetails());
        setStartDate(null);
        setEndDate(null);
        setTimeSlots([]);
        setSelectedFacility('');
        onClose();
    };

    const handleSingleUpdate = (data: any) => {
        const payload = {
            facility: data.facilityId,
            type: 'unavailable',
            time: data.time.map((slot: any) => ({
                from: slot.from,
                to: slot.to,
            })),
        };
        dispatch(UpdateFacilityUnavailability({ data: payload, _id: data._id }))
            .unwrap()
            .then(() => {
                handleModalClose();
            });
    };

    const renderTimeSlots = () => {
        const getDisabledEndHours = (
            unavailabilityIndex: number,
            timeIndex: number
        ) => {
            const startTime = dayjs(
                timeSlots[unavailabilityIndex].time[timeIndex].from,
                'HH:mm'
            );
            if (!startTime) return [];
            const startMinute = startTime.minute();
            const startHour = startTime.hour();

            return startHour !== null
                ? Array.from({ length: 24 }, (_, i) => i).filter(
                      (hour) =>
                          hour <= (startMinute < 59 ? startHour - 1 : startHour)
                  )
                : [];
        };

        const getDisabledEndMinutes = (
            selectedHour: any,
            unavailabilityIndex: number,
            timeIndex: number
        ) => {
            const startTime = timeSlots[unavailabilityIndex].time[timeIndex]
                .from
                ? dayjs(
                      timeSlots[unavailabilityIndex].time[timeIndex].from,
                      'HH:mm'
                  )
                : null;

            if (!startTime) return [];
            const startMinute = startTime.minute();
            const startHour = startTime.hour();
            return selectedHour === startHour
                ? Array.from({ length: 60 }, (_, i) => i).filter(
                      (minute) => minute <= startMinute
                  )
                : [];
        };
        return timeSlots.map((unavailability: any, i: number) => (
            <div className="mb-7 flex w-full flex-col rounded-xl border px-8  py-4">
                <div className="flex justify-end">
                    <div
                        onClick={() => {
                            setUnavailabilityData(unavailability);
                            setDeleteIsModalVisible(true);
                        }}
                    >
                        <CloseOutlined />
                    </div>
                </div>
                Date: {`${formatDate(unavailability.fromDate)}`}
                {unavailability.time.map((slot: any, index: number) => (
                    <>
                        <Row gutter={16} key={index}>
                            <Col span={12}>
                                {/* <Form.Item
                            label={`Start Time ${index + 1}`}
                            name={`startTime${index}`}
                        > */}
                                <TimePicker
                                    style={{ width: '100%' }}
                                    format="HH:mm"
                                    value={
                                        slot.from
                                            ? dayjs(slot.from, 'HH:mm')
                                            : null
                                    }
                                    onChange={(time) => {
                                        const newSlots = timeSlots.map(
                                            (slot, slotIndex) =>
                                                slotIndex === i
                                                    ? {
                                                          ...slot,
                                                          time: slot.time.map(
                                                              (
                                                                  data: any,
                                                                  timeIndex: number
                                                              ) =>
                                                                  timeIndex ===
                                                                  index
                                                                      ? {
                                                                            ...data,
                                                                            from: time.format(
                                                                                'HH:mm'
                                                                            ),
                                                                            to: null,
                                                                        }
                                                                      : data
                                                          ),
                                                      }
                                                    : slot
                                        );
                                        setTimeSlots(newSlots);
                                    }}
                                />
                                {/* </Form.Item> */}
                            </Col>
                            <Col span={12}>
                                {/* <Form.Item
                            label={`End Time ${index + 1}`}
                            name={`endTime${index}`}
                        > */}
                                <TimePicker
                                    style={{ width: '100%' }}
                                    format="HH:mm"
                                    value={
                                        slot.to ? dayjs(slot.to, 'HH:mm') : null
                                    }
                                    disabled={!slot.from}
                                    disabledTime={() => ({
                                        disabledHours: () =>
                                            getDisabledEndHours(i, index),
                                        disabledMinutes: (selectedHour) =>
                                            getDisabledEndMinutes(
                                                selectedHour,
                                                i,
                                                index
                                            ),
                                    })}
                                    onChange={(time) => {
                                        const newSlots = timeSlots.map(
                                            (slot, slotIndex) =>
                                                slotIndex === i
                                                    ? {
                                                          ...slot,
                                                          time: slot.time.map(
                                                              (
                                                                  data: any,
                                                                  timeIndex: number
                                                              ) =>
                                                                  timeIndex ===
                                                                  index
                                                                      ? {
                                                                            ...data,
                                                                            to: time.format(
                                                                                'HH:mm'
                                                                            ),
                                                                        }
                                                                      : data
                                                          ),
                                                      }
                                                    : slot
                                        );
                                        setTimeSlots(newSlots);
                                    }}
                                />
                                {/* </Form.Item> */}
                            </Col>
                        </Row>

                        <div
                            onClick={() => {
                                removeTimeSlot(i, index);
                            }}
                        >
                            <CloseOutlined />
                        </div>
                    </>
                ))}
                <div
                    onClick={() => {
                        addTimeSlot(i);
                    }}
                >
                    <PlusSquareOutlined />
                </div>
                <Button
                    onClick={() => handleSingleUpdate(unavailability)}
                    className="px-20 py-7 text-2xl"
                    type="primary"
                    htmlType="button"
                >
                    Save
                </Button>
            </div>
        ));
    };

    const handleSubmit = (data: any) => {
        const payload = {
            facility: selectedFacility,
            type: 'unavailable',
            unAvailabilities: timeSlots.map((slot) => ({
                unAvailabilityId: slot._id,
                time: slot.time.map((slot: any) => ({
                    from: slot.from,
                    to: slot.to,
                })),
            })),
        };
        dispatch(UpdateAllFacilityUnavailabilities(payload))
            .unwrap()
            .then(() => {
                handleModalClose();
            });
    };

    return (
        <Modal
            title={
                <div className="border-b-2 text-[#1A3353]">
                    Edit Facility Unavailability
                </div>
            }
            open={visible}
            onCancel={handleModalClose}
            footer={null}
            width={800}
            style={{ top: 10 }}
        >
            <Form
                className="px-8 pt-10"
                name="schedule-form"
                layout="vertical"
                size="large"
                onFinish={handleSubmit}
                form={form}
            >
                <Form.Item
                    label="Branch Name"
                    name="facilityId"
                    rules={[
                        {
                            required: true,
                            message: 'Please select branch name(s)',
                        },
                    ]}
                >
                    <Select
                        placeholder="Enter the branch name"
                        value={selectedFacility}
                        onChange={(value) => setSelectedFacility(value)}
                        filterOption={(input, option) =>
                            (option?.children as unknown as string)
                                .toLowerCase()
                                .includes(input.toLowerCase())
                        }
                    >
                        {facilityList.map((facility: IFacility) => (
                            <Option key={facility._id} value={facility._id}>
                                {facility.facilityName}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    label="Date Range"
                    name="dateRange"
                    rules={[
                        {
                            required: false,
                            message: 'Please select date range',
                        },
                    ]}
                >
                    <Radio.Group
                        value={dateRange}
                        defaultValue={dateRange}
                        onChange={(e) => {
                            setDateRange(e.target.value);
                            if (e.target.value === 'single') {
                                setEndDate(startDate);
                                form.setFieldsValue({
                                    endDate: startDate,
                                });
                            }
                        }}
                    >
                        <Radio value="single">Single</Radio>
                        <Radio value="multiple">Multiple</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item
                    label="Working Hours"
                    className="pb-5 pt-3"
                    name="workinghours"
                    rules={[
                        {
                            required: false,
                            message: 'Please enter working hours !',
                        },
                    ]}
                >
                    <div className="flex flex-row gap-5">
                        {days.map((day) => (
                            <div key={day} className="day-button">
                                <Button
                                    shape="circle"
                                    className={`p-2 hover:border-checkbox-checked  ${
                                        selectedDay === day
                                            ? 'bg-checkbox-checked text-white'
                                            : 'bg-white'
                                    }`}
                                    onClick={() => handleDayClick(day)}
                                >
                                    {day}
                                </Button>
                            </div>
                        ))}
                    </div>
                </Form.Item>

                <div className="mb-4">
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item label="Start Date" name="startDate">
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    placeholder="DD/MM/YYYY"
                                    format="DD/MM/YYYY"
                                    style={{ width: '100%' }}
                                    onChange={(date) => {
                                        setStartDate(date);
                                        if (dateRange === 'single') {
                                            setEndDate(date);
                                            form.setFieldsValue({
                                                endDate: date,
                                            });
                                        }
                                    }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label="End Date" name="endDate">
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    placeholder="DD/MM/YYYY"
                                    format="DD/MM/YYYY"
                                    style={{ width: '100%' }}
                                    onChange={(date) =>
                                        dateRange === 'multiple' &&
                                        setEndDate(date)
                                    }
                                    value={endDate}
                                    disabled={dateRange === 'single'}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    {renderTimeSlots()}
                </div>

                <div className="flex flex-row justify-end gap-5">
                    <Button
                        type="default"
                        className="border border-[#1A3353] px-20 py-7 text-2xl"
                        onClick={handleModalClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        className="bg-purpleLight px-20 py-7 text-2xl"
                        type="primary"
                        htmlType="submit"
                    >
                        Save
                    </Button>
                </div>
            </Form>
            {isDeleteModalVisible && (
                <DeleteModal
                    title="Confirm Delete"
                    message={`Do you want to remove unavailbility of ${formatDate(
                        unavailabilityData.fromDate
                    )} ?`}
                    isVisible={isDeleteModalVisible}
                    onDelete={deleteSlot}
                    onCancel={() => {
                        setDeleteIsModalVisible(false);
                        setUnavailabilityData({});
                    }}
                />
            )}
        </Modal>
    );
};

export default EditFacilityAvailabilityModal;

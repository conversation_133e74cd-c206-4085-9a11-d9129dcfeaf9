import React, { useEffect, useState } from 'react';
import {
    Form,
    Input,
    Modal,
    Select,
    Button,
    Space,
    Switch,
    Divider,
} from 'antd';

import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { createDynamicAttribute, getDynamicAttributeDetails, updateDynamicAttribute } from '~/redux/actions/merchandise/attribute-action';
interface AddAttributeModalProps {
    visible: boolean;
    onClose: () => void;
    attributeId?: string;
}


const AddAttributeModal: React.FC<AddAttributeModalProps> = ({ visible, onClose, attributeId }) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    useEffect(() => {
        if (attributeId) {
            dispatch(getDynamicAttributeDetails(attributeId)).unwrap().then((res) => {
                form.setFieldsValue({
                    name: res?.data?.name,
                })
            }
            ).catch((err) => {
                console.error(err);
            })
        }
    }, [])
    const handleFinish = async (values: any) => {
        const cleanAttributeId = decodeURIComponent(attributeId || '').replace(/^\/+/, '');
        if (attributeId) {
            const payLoad = {
                ...values,
            }
            await dispatch(updateDynamicAttribute({ id: attributeId, payload: payLoad })).unwrap().then(() => {
                form.resetFields();
                onClose();
            })
            return;
        }
        else {
            const payLoad = {
                ...values,
                type: "variant",
                inputType: "dropdown",
                multiple: false
            }
            const response = await dispatch(createDynamicAttribute(payLoad)).unwrap().then(() => {
                form.resetFields();
                onClose();
            })
        }
    };
const handleCLose = () => {
    console.log("sljfl")
    form.resetFields();
    onClose()
}
    return (
        <Modal
            title={attributeId ? 'Edit Attribute' : 'Add Attribute'}
            open={visible}
            onCancel={handleCLose}
            onOk={() => form.submit()}
            width={700}
            okText={attributeId ? 'Edit Attribute' : "Save Attribute"}
            okButtonProps={{
                className: 'border bg-[#8143D1] px-8 py-3 text-xl text-white',
            }}
           
        >
            <Form
                layout="vertical"
                form={form}
                onFinish={handleFinish}
                initialValues={{
                    multiple: false,
                    fields: [{ name: '', type: 'text', multiple: false, required: false }]
                }}
            >

                <Form.Item label="Name" name="name" rules={[{ required: true, message: 'Please enter attribute name' }]}>
                    <Input placeholder="Attribute name" />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default AddAttributeModal;
import { MoreOutlined } from '@ant-design/icons';
import { ConfigProvider, Dropdown, Table, Pagination, Menu } from 'antd';
import { useState, useEffect, useCallback } from 'react';
import CustomTable from '~/components/common/customTable';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';
import { useParams } from 'wouter';
import { useAppSelector } from '~/hooks/redux-hooks';
import { SubAttributeList, deleteAttribute } from '~/redux/actions/merchandise/attribute-action';
import { navigate } from 'wouter/use-location';
import DeleteModal from '~/components/common/deleteModal';

const SubAttributeListing = () => {
    const dispatch = useDispatch<AppDispatch>();
    const params = getQueryParams();
    const { id } = useParams<{ id: string }>();

    const pageParam = Number(params.page) || 1;
    const pageSizeParam = Number(params.pageSize) || 10;

    const [search, setSearch] = useState<string>(params.search || '');
    const [currentPage, setCurrentPage] = useState<number>(pageParam);
    const [pageSize, setPageSize] = useState<number>(pageSizeParam);
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [subAttributeList, setSubAttributeList] = useState<any>([])
    const [subAttributeCount, setSubAttributeCount] = useState<number>()

    const [isDeleteModalVisible, setDeleteIsModalVisible] = useState(false);
    const [attributeId, setAttributeId] = useState(null);
    const cleanId = decodeURIComponent(id).replace(/^\/+/, '');
    const fetchSubAttributes = useCallback(() => {
        dispatch(
            SubAttributeList({
                page: currentPage,
                pageSize,
                attribute: cleanId,
                search,
            })
        )
            .unwrap()
            .then((res: any) => {
                setSubAttributeCount(res.data.count);
                setSubAttributeList(res.data.list);
            });
    }, [dispatch, currentPage, pageSize, search, id]);

    useEffect(() => {
        fetchSubAttributes();
    }, [fetchSubAttributes]);

    const handlePageChange = useCallback((page: number, pageSize?: number) => {
        setCurrentPage(page);
        if (pageSize) setPageSize(pageSize);
    }, []);

    const dataSource = subAttributeList.map((item: any, i: number) => ({
        key: i + 1,
        _id: item?._id,
        name: item?.value,
        slug: item?.slug,
    }));

    const menu = (record: any) => (
        <Menu>
            <Menu.Item key="1" onClick={() => navigate(`add-sub-attribute/${id}/${record._id}`)}>Edit</Menu.Item>
            <Menu.Item key="2" onClick={() => {
                setDeleteIsModalVisible(true);
                setAttributeId(record._id);
            }}>Delete</Menu.Item>
        </Menu>
    );

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
        },
        // {
        //     title: 'Slug',
        //     dataIndex: 'slug',
        // },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => (
                <Dropdown
                    overlay={menu(record)}
                    trigger={['click']}
                    menu={{ open: openDropdownKey === record.key }}
                    onOpenChange={(open) => setOpenDropdownKey(open ? record.key : null)}
                >
                    <MoreOutlined style={{ fontSize: '20px', cursor: 'pointer' }} />
                </Dropdown>
            ),
        },
    ];
    function handleSearch(value: string) {
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }
    const deleteAtributeData = async () => {
        if (!attributeId) return;

        try {
            await dispatch(deleteAttribute(attributeId)).unwrap();

            setSubAttributeList((prevList: any) => prevList.filter((item: any) => item._id !== attributeId));
        } catch (error) {
            console.error("Error deleting attribute:", error);
        } finally {
            setDeleteIsModalVisible(false);
            setAttributeId(null);
            fetchSubAttributes();
        }
    };

    return (
        <div>
            <CustomTable
                className="min-w-min"
                heading="Sub Attribute Listing"
                addNewTitle="Add New"
                addNewLink={`add-sub-attribute/${id}/0`}
                search={search}
                showSearch={true}
                onSearch={handleSearch}
            />
            <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                borderColor: '#0000001A',
                                cellFontSize: 13,
                                headerBg: '#fff',
                                headerColor: '#1A3353',
                                colorText: '#455560',
                            },
                        },
                    }}
                >
                    <Table
                        className="m-2 rounded-[6px] border-1"
                        columns={columns}
                        dataSource={dataSource}
                        pagination={false}
                    />
                </ConfigProvider>
            </div>
            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={subAttributeCount}
                    pageSize={pageSize}
                    onChange={handlePageChange}
                    showSizeChanger
                    onShowSizeChange={handlePageChange}
                    hideOnSinglePage
                />
            </div>
            {isDeleteModalVisible && (
                <DeleteModal
                    title="Confirm Delete"
                    message={`Do you want to delete this Attribute?`}
                    isVisible={isDeleteModalVisible}
                    onDelete={deleteAtributeData}
                    onCancel={() => {
                        setDeleteIsModalVisible(false);
                    }}
                />
            )}
        </div>
    );
};

export default SubAttributeListing;

import React, { useEffect } from 'react';
import { Modal, Button, Form, Upload } from 'antd';
import type { UploadProps } from 'antd/es/upload';

// import ErrorTable from '~/components/common/error-table';

interface BulkUploadModalProps {
    open: boolean;
    onClose: () => void;
    handleCSVFile: (file: File) => void;
    error_data: any;
    bulkUpload: (confirmUpdate: boolean) => void;
    bulk_upload_loader: boolean;
    resetFile?: boolean;
    setResetFile?: (val: boolean) => void;
}

const BulkUploadModal: React.FC<BulkUploadModalProps> = ({
    open,
    onClose,
    handleCSVFile,
    error_data,
    bulkUpload,
    bulk_upload_loader,
    resetFile,
    setResetFile,
}) => {
    const [selectedFileName, setSelectedFileName] = React.useState<string | null>(null);
    const [confirmUpdate, setConfirmUpdate] = React.useState(false);
    useEffect(() => {
        if (resetFile) {
            setSelectedFileName(null);
            setConfirmUpdate(false);
            setResetFile?.(false); // reset the flag in parent
        }
    }, [resetFile]);
    const uploadProps: UploadProps = {
        name: 'file',
        multiple: false,
        accept: '.csv',
        beforeUpload: () => false,
        onChange(info) {
            console.log(info)
            const fileObj = info.fileList?.[0]?.originFileObj;
            if (fileObj && fileObj.name.endsWith('.csv')) {
                setSelectedFileName(fileObj.name); // 
                handleCSVFile(fileObj);
            }
        },
        fileList: [], // Optional: disable internal state for Ant Upload
    };
    const handleModalClose = () => {
        setSelectedFileName(null);
        setConfirmUpdate(false);
        onClose();

    }
    return (
        <Modal
            centered
            open={open}
            footer={false}
            onCancel={handleModalClose}
            width={600}
        >
            <div className="w-full h-full p-10">
                <h1 className="text-[1.4vw] font-bold mb-6">Bulk Product Upload</h1>
                <div className="mb-3 text-left">
                    <Button
                        className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                        type="primary"
                        ghost
                        onClick={() => {
                            const link = document.createElement('a');
                            link.href = '/sample-product-upload-formatted.csv';
                            link.download = 'sample-product-upload-formatted.csv';
                            document.body.appendChild(link);
                            link.click();
                            link.remove();
                        }}
                    >
                        Download Sample CSV
                    </Button>

                </div>
                <Form layout="vertical">
                    <Form.Item
                        name="csvFile"
                        label={<label className="text-[#1A3353]">Upload CSV File *</label>}
                    >
                        <Upload.Dragger {...uploadProps} style={{ borderRadius: 8 }}>
                            {/* <p className="ant-upload-drag-icon">
                                <img
                                    src="/icons/common/csv-icon.svg"
                                    alt="csv-icon"
                                    className="w-[40px] mx-auto"
                                />
                            </p> */}
                            <p className="text-[#3B3B3B] font-medium">
                                Click or drag CSV file to this area
                            </p>
                            <p className="text-gray-400 text-sm">
                                Only .csv files are accepted
                            </p>
                        </Upload.Dragger>
                        {selectedFileName && (
                            <div className="mt-2  text-sm font-medium">
                                Selected File: <span>{selectedFileName}</span>
                            </div>
                        )}
                    </Form.Item>

                    {/* {error_data && <ErrorTable data={error_data} />} */}
                    <Form.Item>
                        <label className="flex items-center gap-2">
                            <input
                                type="checkbox"
                                checked={confirmUpdate}
                                onChange={(e) => setConfirmUpdate(e.target.checked)}
                                className="accent-purple-600 w-5 h-5"
                            />
                            <span className="text-[#1A3353] font-medium">
                                Do you want to update the product?
                            </span>
                        </label>
                    </Form.Item>
                    <Button
                        className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                        onClick={() => bulkUpload(confirmUpdate)}
                        loading={bulk_upload_loader}
                    >
                        Upload
                    </Button>
                </Form>
            </div>
        </Modal>
    );
};

export default BulkUploadModal;

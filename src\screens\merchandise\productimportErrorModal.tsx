import React from 'react';
import { Modal, Table } from 'antd';

const error_columns = [
    {
        title: 'Line Number',
        dataIndex: 'lineNumber',
        key: 'lineNumber',
    },
    {
        title: 'Message',
        dataIndex: 'message',
        key: 'message',
    },
    {
        title: 'Value',
        dataIndex: 'value',
        key: 'value',
    },
];

interface ErrorTableModalProps {
    visible: boolean;
    data: any[];
    onClose: () => void;
}

const ErrorTableModal: React.FC<ErrorTableModalProps> = ({ visible, data, onClose }) => {
    return (
        <Modal
            title="Upload Errors"
            open={visible}
            onCancel={onClose}
            footer={null}
            width={800}
        >
            <Table
                columns={error_columns}
                dataSource={data}
                pagination={{ pageSize: 10 }}
                rowKey={(record: any, index) => `${record.lineNumber}-${index}`}
              
            />
        </Modal>
    );
};

export default ErrorTableModal;

import { CloudDownloadOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Table } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import {
    ConfimredButtonChip,
    PendingButtonChip,
    RejectedButtonChip,
} from '~/components/common/chip-component';
import {
    capitalizeFirstLetter,
    convertToWords,
    formatDate as formatCommonDate,
    goBack,
    toTitleCase,
} from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    DownloadInvoice,
    OrderInvoiceDetails,
} from '~/redux/actions/purchased-action';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { formatDate } from '~/utils/formatDate';
import { getQueryParams } from '~/utils/getQueryParams';

const columns = [
    {
        title: 'Product',
        dataIndex: 'name',
        key: 'name',
        width: 200,
    },
    {
        title: 'Qty ',
        dataIndex: 'quantity',
        key: 'quantity',
    },
    {
        title: 'Validity',
        dataIndex: '',
        key: 'expiredInDays',
        render: (text: any, record: any) => {
            return record?.expiredInDays
                ? `${record.expiredInDays} ${record.durationUnit}`
                : '--';
        },
    },
    {
        title: 'Start Date',
        dataIndex: 'startDate',
        key: 'startDate',
        render: (text: any) => (text ? formatDate(text) : '--'),
    },
    {
        title: 'End Date',
        dataIndex: 'endDate',
        key: 'endDate',
        render: (text: any) => (text ? formatDate(text) : '--'),
    },

    {
        title: 'HSN / SAC',
        dataIndex: 'hsnOrSacCode',
        key: 'hsnOrSacCode',
    },
    // {
    //     title: 'SKU',
    //     dataIndex: 'SKU',
    //     key: 'SKU',
    // },
    {
        title: 'Unit Price ',
        dataIndex: 'price',
        key: 'price',
        align: 'center',
    },
    {
        title: 'Discount ',
        dataIndex: 'discountValue',
        key: 'discountValue',
        align: 'center',
    },
    {
        title: 'GST % ',
        dataIndex: 'tax',
        key: 'tax ',
        align: 'center',
    },
    // {
    //     title: 'GST Value ',
    //     dataIndex: 'GSTValue',
    //     key: 'GSTValue',
    //     align: 'center',
    //     render: (text: string, record: any) => {
    //         const { price, discountValue, tax } = record;
    //         const taxableAmount = price - (discountValue || 0);
    //         const gstValue = (taxableAmount * (tax || 0)) / 100;
    //         return gstValue?.toFixed(2);
    //     },
    // },
    // {
    //     title: 'Final Price ',
    //     dataIndex: 'FinalPrice',
    //     key: 'FinalPrice',
    //     align: 'center',
    //     render: (text: string, record: any) => {
    //         const { price, discountValue, tax } = record;
    //         const taxableAmount = price - (discountValue || 0);
    //         const gstValue = (taxableAmount * (tax || 0)) / 100;
    //         const finalPrice = taxableAmount + gstValue;
    //         return finalPrice?.toFixed(2);
    //     },
    // },
];

const OrderDetail = () => {
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const orderId = params.orderId;
    const [loader, startLoader, endLoader] = useLoader(true);
    const [_, setLocation] = useLocation();

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        orderInvoiceDetail: state.purchased_store.orderInvoiceDetail,
    }));

    const dataSource = [
        ...(Array.isArray(store.orderInvoiceDetail?.purchaseItems)
            ? store.orderInvoiceDetail.purchaseItems.map((item: any) => ({
                  ...item,
                  key:
                      item._id ||
                      `purchase_${Math.random().toString(36).substr(2, 9)}`,
              }))
            : []),
        ...(Array.isArray(store.orderInvoiceDetail?.productItem)
            ? store.orderInvoiceDetail.productItem.map((item: any) => ({
                  ...item,
                  key:
                      item._id ||
                      `product_${Math.random().toString(36).substr(2, 9)}`,
              }))
            : []),
        ...(Array.isArray(store.orderInvoiceDetail?.customPackageItems)
            ? store.orderInvoiceDetail.customPackageItems.map((item: any) => ({
                  ...item,
                  key:
                      item._id ||
                      `package_${Math.random().toString(36).substr(2, 9)}`,
              }))
            : []),
    ];

    useEffect(() => {
        startLoader();
        if (orderId) {
            dispatch(OrderInvoiceDetails({ orderId: orderId }))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [orderId]);

    const handleDownloadInvoice = async () => {
        if (!orderId) return;

        try {
            const response = await dispatch(
                DownloadInvoice({ orderId })
            ).unwrap();

            if (!response) {
                throw new Error('No URL received from API.');
            }

            window.open(response, '_blank', 'noopener,noreferrer');

            // setTimeout(() => {
            //     const a = document.createElement("a");
            //     a.href = response;
            //     a.setAttribute("download", `Invoice-${orderId}.pdf`);
            //     a.setAttribute("target", "_blank"); // Ensures download doesn't affect current tab
            //     a.style.display = "none"; // Hide the element
            //     document.body.appendChild(a);
            //     a.click();
            //     document.body.removeChild(a);
            // }, 1000);
        } catch (error) {
            console.error('Error downloading invoice:', error);
        }
    };
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasOrdersActionPermission = useMemo(() => {
        if (store.role === RoleType.ORGANIZATION) {
            return true;
        } else {
            return all_permissions_for_role?.some((module: any) =>
                module.subjects?.some(
                    (subject: any) =>
                        subject.type === SUBJECT_TYPE.ORDER &&
                        subject.actions?.some((action: any) =>
                            action.permissions?.some(
                                (permission: any) =>
                                    permission.type ===
                                    PERMISSIONS_ENUM.PURCHASE_INVOICE_WRITE
                            )
                        )
                )
            );
        }
    }, [all_permissions_for_role]);
    console.log('hasOrdersActionPermission', hasOrdersActionPermission);
    return (
        <>
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <>
                    <div className="flex flex-row justify-between">
                        <div className="flex items-center gap-5">
                            <img
                                src="/icons/back.svg"
                                alt="edit"
                                className="h-[10px] cursor-pointer"
                                onClick={goBack}
                            />
                            <Title className=" text-[#1a3353]" level={4}>
                                Order ID : {store.orderInvoiceDetail?.orderId}
                            </Title>
                        </div>
                        <div className="flex flex-row items-center gap-5">
                            {/* <Button
                                className=" fw-500 flex w-[100px] items-center rounded-2xl  border-[#1A3353] 
                                     py-3 text-xl text-[#1A3353]"
                            >
                                Schedule
                            </Button> */}

                            {store.orderInvoiceDetail?.paymentStatus ===
                            'pending' ? (
                                hasOrdersActionPermission && (
                                    <Button
                                        onClick={() =>
                                            setLocation(
                                                `/payment?orderId=${orderId}`
                                            )
                                        }
                                        className="fw-500 flex items-center rounded-2xl bg-purpleLight px-6 py-3 text-xl text-white"
                                    >
                                        Complete Payment
                                    </Button>
                                )
                            ) : store.orderInvoiceDetail?.paymentStatus ===
                                  'completed' ||
                              store.orderInvoiceDetail?.paymentStatus ===
                                  'refund' ? (
                                <Button
                                    onClick={handleDownloadInvoice}
                                    className="fw-500 flex items-center rounded-2xl bg-purpleLight px-6 py-3 text-xl text-white"
                                >
                                    <CloudDownloadOutlined className="text-2xl" />
                                    Download Invoice
                                </Button>
                            ) : null}
                        </div>
                    </div>

                    {/* -----------------order detail on card-------------------- */}

                    <div className="mb-4 mt-10 flex flex-row gap-4 ">
                        <div className=" text-xl font-medium text-[#1A3353]">
                            Ordered on:{' '}
                        </div>
                        <div className=" text-[#1A3353]">
                            {formatCommonDate(
                                store.orderInvoiceDetail?.invoiceDate
                            )}
                        </div>
                    </div>

                    <div
                        className="mb-10 flex flex-row  gap-4 "
                        id="target-div"
                    >
                        <div className="flex w-[20%] flex-col gap-3 rounded-2xl border   px-4 py-4">
                            <div className="flex w-[100%] flex-row items-start gap-5 ">
                                <div className="flex w-[20%] flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Name
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className="text-xl text-[#455560] ">
                                    {capitalizeFirstLetter(
                                        store.orderInvoiceDetail?.clientDetails
                                            ?.name
                                    )}
                                </p>
                            </div>
                            <div className="flex w-[100%] flex-row items-start gap-5 ">
                                <div className="flex w-[20%] flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Email
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className=" overflow-hidden truncate text-xl text-[#455560]">
                                    {
                                        store.orderInvoiceDetail?.clientDetails
                                            ?.email
                                    }
                                </p>
                            </div>
                            <div className="flex w-[100%] flex-row items-center gap-5 ">
                                <div className="flex w-[20%] flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Mobile
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className="text-xl text-[#455560] ">
                                    {
                                        store.orderInvoiceDetail?.clientDetails
                                            ?.phone
                                    }
                                </p>
                            </div>
                            <div className="flex w-[100%] flex-row items-center gap-5 ">
                                <div className="flex flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Paid On
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className="text-xl text-[#455560] ">
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'pending'
                                        ? '--'
                                        : formatDate(
                                              store.orderInvoiceDetail
                                                  ?.paymentDetails?.[0]
                                                  ?.paymentDate
                                          )}
                                </p>
                            </div>

                            {/* <div className="flex w-[100%] flex-row items-center gap-5 ">
                                <div className="flex w-[20%] flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Status
                                    </p>
                                    <p>:</p>
                                </div>
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'completed' && <ConfimredButtonChip />}
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'pending' && <PendingButtonChip />}
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'failed' && <RejectedButtonChip />}
                            </div> */}
                        </div>
                        <div className="flex w-[13%] flex-col gap-3">
                            <div className="flex flex-col items-center justify-center rounded-2xl border py-7">
                                <p className="text-2xl font-semibold text-[#1A3353]">
                                    {Math.floor(
                                        store.orderInvoiceDetail?.grandTotal
                                    )}{' '}
                                    /-
                                </p>
                                <p className="text-lg  text-[#455560]">
                                    Order Value
                                </p>
                            </div>
                            <div className="flex flex-col items-center justify-center rounded-2xl border py-7">
                                <p className="text-2xl font-semibold text-[#1A3353]">
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'completed' && <ConfimredButtonChip />}
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'pending' && <PendingButtonChip />}
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'failed' && <RejectedButtonChip />}
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'refund' && (
                                        <button
                                            className="h-[20px] rounded bg-red-100 px-4 text-[13px] font-[600] text-red-500"
                                            title={
                                                store.orderInvoiceDetail
                                                    ?.paymentReason
                                            }
                                        >
                                            {'Refunded'}
                                        </button>
                                    )}
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'cancel' && (
                                        <button
                                            className="h-[20px] rounded bg-red-100 px-4 text-[13px] font-[600] text-red-500"
                                            title={
                                                store.orderInvoiceDetail
                                                    ?.paymentReason
                                            }
                                        >
                                            {'Cancelled'}
                                        </button>
                                    )}
                                </p>
                                <p className="text-lg  text-[#455560]">
                                    Payment Status
                                </p>
                            </div>
                        </div>
                        <div className="flex w-[67%] flex-row items-start rounded-2xl border px-4 ">
                            <div className="flex w-[25%] flex-col justify-center gap-3  px-4 py-4">
                                <p className="  text-xl font-medium text-[#1A3353]">
                                    Client Details
                                </p>
                                <p className="  text-xl  text-[#455560]">
                                    {capitalizeFirstLetter(
                                        store.orderInvoiceDetail
                                            ?.clientBillingDetails?.name
                                    )}
                                </p>
                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail
                                            ?.clientBillingDetails?.addressLine1
                                    }{' '}
                                    {
                                        store.orderInvoiceDetail
                                            ?.clientBillingDetails?.addressLine2
                                    }
                                </p>

                                {(store.orderInvoiceDetail?.clientBillingDetails
                                    ?.city ||
                                    store.orderInvoiceDetail
                                        ?.clientBillingDetails?.state) && (
                                    <p className="text-xl text-[#455560]">
                                        {store.orderInvoiceDetail
                                            ?.clientBillingDetails?.city
                                            ? `${store.orderInvoiceDetail?.clientBillingDetails?.city}, ${store.orderInvoiceDetail?.clientBillingDetails?.state}`
                                            : store.orderInvoiceDetail
                                                  ?.clientBillingDetails?.state}
                                    </p>
                                )}

                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail
                                            ?.clientBillingDetails?.postalCode
                                    }
                                </p>
                                {store.orderInvoiceDetail?.clientBillingDetails
                                    ?.gstNumber && (
                                    <p className="  text-xl  text-[#455560]">
                                        GST No:{' '}
                                        {store.orderInvoiceDetail?.clientBillingDetails?.gstNumber?.toUpperCase()}
                                    </p>
                                )}
                            </div>
                            <div className="flex w-[35%]  flex-col justify-center gap-3  px-4 py-4">
                                <p className="  text-xl font-medium text-[#1A3353]">
                                    Billing Details
                                </p>
                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.billingName
                                    }
                                </p>
                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.cityName
                                    }{' '}
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.addressLine1
                                    }{' '}
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.addressLine2
                                    }
                                </p>

                                {(store.orderInvoiceDetail?.billingDetails
                                    ?.cityName ||
                                    store.orderInvoiceDetail?.billingDetails
                                        ?.state) && (
                                    <p className="text-xl text-[#455560]">
                                        {store.orderInvoiceDetail
                                            ?.billingDetails?.city
                                            ? `${store.orderInvoiceDetail?.billingDetails?.city}, ${store.orderInvoiceDetail?.billingDetails?.state}`
                                            : store.orderInvoiceDetail
                                                  ?.clientDetails?.state}
                                    </p>
                                )}
                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.postalCode
                                    }
                                </p>
                                {store.orderInvoiceDetail?.billingDetails
                                    ?.gstNumber && (
                                    <p className="  text-xl  text-[#455560]">
                                        GST No:{' '}
                                        {store.orderInvoiceDetail?.billingDetails?.gstNumber?.toUpperCase()}
                                    </p>
                                )}
                            </div>
                            <div className="flex w-[20%] flex-col justify-center gap-8  px-4 py-4 ">
                                <div className=" flex w-[100%] flex-col items-start justify-center  ">
                                    <p className="text-xl font-medium text-[#1A3353]">
                                        Payment Method
                                    </p>
                                    <p className="text-lg  text-[#455560]">
                                        {store.orderInvoiceDetail?.updatedPaymentDetails
                                            ?.map(
                                                (detail: any) =>
                                                    detail.paymentMethodName
                                            )
                                            .join(', ')}
                                    </p>
                                </div>
                                <div className="flex w-[100%] flex-col items-start justify-center ">
                                    <p className="text-xl font-medium text-[#1A3353]">
                                        Total Products
                                    </p>
                                    <p className="text-lg  text-[#455560]">
                                        {store.orderInvoiceDetail?.totalItems}
                                    </p>
                                </div>
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'cancel' &&
                                    store?.orderInvoiceDetail?.cancelledBy && (
                                        <div className="flex w-[100%] flex-col items-start justify-center ">
                                            <p className="text-xl font-medium text-[#1A3353]">
                                                Cancelled By
                                            </p>
                                            <p className="text-lg  text-[#455560]">
                                                {
                                                    store.orderInvoiceDetail
                                                        ?.cancelledBy
                                                }
                                            </p>
                                        </div>
                                    )}
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'refund' &&
                                    store?.orderInvoiceDetail?.cancelledBy && (
                                        <div className="flex w-[100%] flex-col items-start justify-center ">
                                            <p className="text-xl font-medium text-[#1A3353]">
                                                Refunded By
                                            </p>
                                            <p className="text-lg  text-[#455560]">
                                                {
                                                    store?.orderInvoiceDetail
                                                        ?.cancelledBy
                                                }
                                            </p>
                                        </div>
                                    )}
                                {/* {store?.orderInvoiceDetail?.paymentDetails?.[0]
                                    ?.transactionId && (
                                    <div className=" flex w-[100%] flex-col items-start justify-center ">
                                        <p className="text-xl font-medium text-[#1A3353]">
                                            Notes/TXN ID :-{' '}
                                            {
                                                store.orderInvoiceDetail
                                                    .paymentDetails[0]
                                                    .transactionId
                                            }
                                        </p>
                                    </div>
                                )} */}
                            </div>
                            <div className="flex w-[20%] flex-col justify-center gap-8  px-4 py-4">
                                <div className=" flex w-[100%] flex-col items-start justify-center ">
                                    <p className="text-xl font-medium text-[#1A3353]">
                                        Order Created By
                                    </p>
                                    <p className="text-lg  text-[#455560]">
                                        {
                                            store.orderInvoiceDetail
                                                ?.createdByName
                                        }
                                    </p>
                                </div>

                                <div className=" flex w-[100%] flex-col items-start justify-center ">
                                    <p className="text-xl font-medium text-[#1A3353]">
                                        Notes/TXN ID :-{' '}
                                    </p>
                                    <p className="text-lg  text-[#455560]">
                                        {
                                            store.orderInvoiceDetail
                                                ?.paymentDetails[0]
                                                ?.transactionId
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* ----------------------table----------------------- */}

                    <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        cellPaddingBlock: 12,
                                    },
                                },
                            }}
                        >
                            <Table
                                dataSource={dataSource}
                                columns={columns}
                                pagination={false}
                            />
                        </ConfigProvider>
                    </div>

                    {/* ====================bill reciept--------------------------- */}

                    <div className="flex flex-row justify-between py-8">
                        <div className="flex flex-col ">
                            <p className="text-2xl text-[#1a3353]">
                                Total amount in words :
                            </p>
                            <p className="text-2xl font-medium text-[#1a3353]">
                                {/* {convertToWords(
                                    Number(store.orderInvoiceDetail?.total)
                                )?.toUpperCase()} */}
                                {toTitleCase(
                                    store.orderInvoiceDetail?.amountInWords
                                )}
                            </p>
                        </div>
                        <div className="flex w-[25%] flex-col gap-3 rounded-xl border px-5 py-7  text-xl shadow-md">
                            <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Item Total </p>
                                <p>₹{store.orderInvoiceDetail?.subTotal}</p>
                            </div>
                            <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Discount </p>
                                <p>₹ {store.orderInvoiceDetail?.discount}</p>
                            </div>
                            <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>
                                    Cart Discount(
                                    {store?.orderInvoiceDetail
                                        ?.cartDiscountType === 'Flat'
                                        ? `₹ ${store.orderInvoiceDetail?.cartDiscount}`
                                        : `${store.orderInvoiceDetail?.cartDiscount} %`}
                                    )
                                </p>
                                <p>
                                    ₹
                                    {
                                        store.orderInvoiceDetail
                                            ?.cartDiscountAmount
                                    }
                                </p>
                            </div>
                            {store.orderInvoiceDetail?.clientBillingDetails
                                ?.utCode ===
                            store.orderInvoiceDetail?.billingDetails?.utCode ? (
                                <>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>CGST</p>
                                        <p>₹{store.orderInvoiceDetail?.cgst}</p>
                                    </div>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>SGST</p>
                                        <p>₹{store.orderInvoiceDetail?.sgst}</p>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>IGST</p>
                                        <p>₹{store.orderInvoiceDetail?.igst}</p>
                                    </div>
                                </>
                            )}
                            {/* <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Total GST</p>
                                <p>
                                    ₹{store.orderInvoiceDetail?.totalGstValue}
                                </p>
                            </div> */}
                            <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Total Amount </p>
                                <p>
                                    ₹
                                    {
                                        store.orderInvoiceDetail
                                            ?.totalAmountAfterGst
                                    }
                                </p>
                            </div>
                            <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Round Off </p>
                                <p>₹ -{store.orderInvoiceDetail?.roundOff}</p>
                            </div>
                            <div className="flex flex-row items-center justify-between  px-4 pb-1 text-2xl font-semibold text-[#1a3353]">
                                <p>Grand Total </p>
                                <p>
                                    ₹
                                    {Math.floor(
                                        store.orderInvoiceDetail?.grandTotal
                                    )}
                                </p>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </>
    );
};

export default OrderDetail;

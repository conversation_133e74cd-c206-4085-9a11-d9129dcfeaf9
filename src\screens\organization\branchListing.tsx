import { MoreOutlined } from '@ant-design/icons';
import {
    Button,
    ConfigProvider,
    Dropdown,
    Menu,
    Pagination,
    Switch,
    Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import { capitalizeFirstLetter } from '~/components/common/function';
import NoShadowTable from '~/components/common/tableWithoutShadowBorder';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import {
    FacilitiesList,
    UpdateFacilityStatus,
} from '~/redux/actions/facility-action';
import { OrganizationList } from '~/redux/actions/organization-action';
import { RoleType } from '~/types/enums';
import { getQueryParams } from '~/utils/getQueryParams';

const { Title } = Typography;

interface BranchListProps {
    organizationId: string | undefined;
}

const BranchListing: React.FC<BranchListProps> = ({ organizationId }) => {
    const dispatch = useAppDispatch();
    const [_, setLocation] = useLocation();

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        facilityListCount: state.facility_store.facilityListCount,
    }));
    const { role } = useSelector((state: any) => state.auth_store);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);

    // console.log('store-----------------', store.facilityList);

    const params = getQueryParams();

    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const searchValue = useSelector(
        (state: any) => state.common_store.searchValue
    );

    const [branchData, setBranchData] = useState<any>(null);

    const debouncedRequest = useDebounce((callback) => callback(), 300);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    }

    useEffect(() => {
        const payload: any = {
            page: currentPage,
            pageSize: pageSizes,
        };

        if (role === RoleType.SUPER_ADMIN) {
            payload.organizationId = organizationId;
        }

        if (searchValue) {
            debouncedRequest(() => {
                dispatch(
                    FacilitiesList({
                        ...payload,
                        search: searchValue,
                    })
                );
            });
        } else {
            dispatch(FacilitiesList(payload));
        }
    }, [currentPage, pageSizes, searchValue, organizationId]);

    const handleCancelStatusChange = () => {
        setBranchData(null);
        setConfirmationModalVisible(false);
    };

    const handleConfirmStatusChange = () => {
        dispatch(
            UpdateFacilityStatus({
                facilityId: branchData._id,
                reqData: {
                    status: !branchData.isActive,
                },
            })
        );
        setConfirmationModalVisible(false);
        setBranchData(null);
    };

    const openConfirmationModal = (record: any) => {
        setBranchData(record);
        setConfirmationModalVisible(true);
    };

    const columns = [
        {
            title: 'BRANCH NAME',
            dataIndex: '',
            render: (record: any) => {
                // console.log('Record--------', record);

                return (
                    <Link
                        to={`/facility/create-facility/${record?._id}?updateDetails=true&organizationId=${organizationId}&view=true`}
                    >
                        {capitalizeFirstLetter(record.facilityName)}
                    </Link>
                );
            },
        },
        {
            title: 'EMAIL',
            dataIndex: 'email',
            align: 'center',
            // width: "10%",
            render: (email: any) => {
                // console.log('Record--------', email);

                return <div>{email ? email : '-'}</div>;
            },
        },
        {
            title: 'ADDRESS',
            dataIndex: '',
            render: (record: any) => {
                // console.log('Record--------', record);

                return (
                    <div>{`${record.address.addressLine1}, ${record.address.postalCode}`}</div>
                );
            },
        },
    ];

    const selectColumn = [
        {
            title: 'STATUS',
            dataIndex: '',
            key: '',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    colorPrimaryBorder: '#8143D1',
                                    colorPrimary: '#8143D1',
                                    colorTextQuaternary: 'gray',
                                    colorFillQuaternary: 'gray',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="swtich-off"
                            checkedChildren="ON"
                            // className={`${
                            //     record?.isActive
                            //         ? 'bg-[#8143D1]'
                            //         : 'bg-[#D0D4D7]'
                            // }`}
                            unCheckedChildren="OFF"
                            checked={record?.isActive}
                            onChange={() => openConfirmationModal(record)}
                        />
                    </ConfigProvider>
                );
            },
        },
        {
            title: 'ACTIONS',
            dataIndex: '',
            width: '120px',
            key: 'action',
            render: (record: any) => {
                // console.log('Record:--------- ', record);
                const menu = (
                    <Menu>
                        <Menu.Item key="edit">
                            <Link
                                to={`/facility/create-facility/${record?._id}?updateDetails=true&organizationId=${organizationId}`}
                            >
                                Edit Branch
                            </Link>
                        </Menu.Item>
                        {/* <Menu.Item key="add-branch">
                            {' '}
                            <Link
                                to={`/facility/create-facility/${record?._id}?organizationId=${organizationId}`}
                            >
                                Add Branch
                            </Link>
                        </Menu.Item> */}
                    </Menu>
                );
                return (
                    <>
                        <span className="flex gap-5 ">
                            {/* <Link
                                to={`/attribute/create-attribute/${record?._id}`}
                                // onClick={() => selectAttribute(record)}
                            >
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                            <div
                                className="cursor-pointer"
                                // onClick={() => deleteSubAttribute(record.key)}
                            >
                                <img
                                    src="/icons/common/delete.svg"
                                    alt="delete"
                                    className="h-[20px] cursor-pointer"
                                />
                            </div> */}
                            <div>
                                <Dropdown overlay={menu} trigger={['click']}>
                                    <MoreOutlined
                                        style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...selectColumn];

    return (
        <>
            <div className="flex items-center  justify-between">
                <p className="mt-10 ps-4 text-3xl font-semibold text-[#1A3353]">
                    Branch List
                </p>
                <Button
                    onClick={() =>
                        setLocation(
                            `/facility/create-facility/${organizationId}?organizationId=${organizationId}`
                        )
                    }
                    htmlType="submit"
                    className="mt-10  bg-purpleLight text-white"
                >
                    <p> Add Branch </p>
                </Button>
            </div>
            <div className=" mt-3 rounded-3xl border p-5">
                <NoShadowTable
                    className="min-w-min"
                    columns={combinedColumns}
                    dataSource={store.facilityList}
                />
                <div className="flex justify-center  py-10">
                    <Pagination
                        current={currentPage}
                        total={store.facilityListCount}
                        pageSize={pageSizes}
                        onChange={paginate}
                        // hideOnSinglePage
                    />
                </div>
            </div>

            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
        </>
    );
};

export default BranchListing;

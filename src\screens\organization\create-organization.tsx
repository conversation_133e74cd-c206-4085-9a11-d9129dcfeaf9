import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    Button,
    ConfigProvider,
    Form,
    FormProps,
    GetProp,
    Input,
    message,
    Select,
    Typography,
    Upload,
    UploadProps,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import { goBack } from '~/components/common/function';
import { ProjectUrl } from '~/env';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import {
    CityList,
    CountryList,
    UploadImage,
} from '~/redux/actions/common-action';
import {
    CreateNewOrganization,
    OrganizationDetails,
    UpdateOrganization,
} from '~/redux/actions/organization-action';
const { Title } = Typography;

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const getBase64 = (img: FileType, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
};

const beforeUpload = (file: FileType) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
        message.error('You can only upload JPG/PNG file!');
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
        message.error('Image must smaller than 2MB!');
    }
    return isJpgOrPng && isLt2M;
};

const CreateOrganization = () => {
    const [form] = Form.useForm();
    const [location, setLocation] = useLocation();
    const organizationId = location.split('/').pop();
    const [loading, setLoading] = useState(false);
    const [imageUrl, setImageUrl] = useState<string>();
    const [loader, startLoader, endLoader] = useLoader();
    const [selectedState, setSelectedState] = useState<string | null>(null);
    const debouncedRequest = useDebounce((callback) => callback(), 200);
    const [isSelectedState, setIsSelectedState] = useState<boolean>(false);
    const { countryList, cityList } = useSelector(
        (state: any) => state.common_store
    );

    const [logoUrl, setLogoUrl] = useState<string | null>('');

    const store = useAppSelector((state) => ({
        organizationDetails: state.organization_store.organizationDetails,
        countryList: state.common_store.countryList,
    }));
    const [citySearchText, setCitySearchText] = useState<string>('');

    const dispatch = useAppDispatch();

    const handleImageUpload = (file: any) => {
        setLoading(true);
        dispatch(UploadImage({ file: file.file }))
            .then((res: any) => {
                setImageUrl(res?.payload?.res?.data?.data);
            })
            .finally(() => setLoading(false));
    };

    console.log('Image url------------', imageUrl);

    // const handleChange: UploadProps['onChange'] = (info) => {
    //     console.log('info', info);
    //     if (info.file.status === 'uploading') {
    //         setLoading(true);
    //         return;
    //     }
    //     if (info.file.status === 'done') {
    //         getBase64(info.file.originFileObj as FileType, (url) => {
    //             setLoading(false);
    //             setImageUrl(url);

    //             console.log('url--------------', url);
    //         });
    //         const file = info.file.originFileObj as FileType;
    //         handleImageUpload(file);
    //     }
    // };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );

    const handleStateChange = (value: string) => {
        setSelectedState(value);
        setIsSelectedState(true);
        form.setFieldValue('city', null);
        dispatch(CityList({ stateId: value, page: 1, pageSize: 50 }));
    };

    useEffect(() => {
        dispatch(CountryList({ page: 1, pageSize: 40 }));
    }, []);

    const CountryOptions = countryList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    useEffect(() => {
        if (organizationId && organizationId !== '0') {
            dispatch(OrganizationDetails({ organizationId: organizationId }));
        }
    }, [organizationId]);

    useEffect(() => {
        if (store.organizationDetails && organizationId !== '0') {
            const { name, associatedPerson, email, mobile, logo, address } =
                store.organizationDetails;
            form.setFieldsValue({
                organizationName: name,
                contactName: associatedPerson,
                address: address?.addressLine1,
                state: address?.state,
                city: address?.city,
                postalCode: address?.postalCode,
                email,
                mobile: mobile,
                logo,
            });
            setImageUrl(logo);
            setSelectedState(address?.state);
            dispatch(
                CityList({ stateId: address?.state, page: 1, pageSize: 50 })
            );
        }
    }, [store.organizationDetails]);

    useEffect(() => {
        if (citySearchText) {
            debouncedRequest(() => {
                dispatch(
                    CityList({
                        stateId: selectedState,
                        page: 1,
                        pageSize: 50,
                        search: citySearchText,
                    })
                );
            });
        }
    }, [citySearchText]);

    const CityOptions = cityList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const onFinish: FormProps['onFinish'] = (values) => {
        console.log('Success:', values);
        // Add your form submission logic here
        startLoader();
        const payload: any = {
            organizationName: values.organizationName,
            address: {
                stateId: values.state,
                cityId: values.city,
                addressLine1: values.address,
                postalCode: Number(values.postalCode),
            },
            // logo: imageUrl,
            associatedPerson: values.contactName,
        };

        if (imageUrl) {
            payload.logo = imageUrl;
        }

        // console.log('Payload-------------', payload);

        if (organizationId === '0') {
            (payload.mobile = values.mobile),
                (payload.email = values.email),
                dispatch(CreateNewOrganization(payload))
                    .unwrap()
                    .then((res: any) => {
                        console.log('Res----------', res);
                        if (res?.status === 200 || res?.status === 201) {
                            setLocation('/organizations');
                        }
                    })
                    .finally(endLoader);
        } else if (organizationId !== '0' || undefined || null) {
            dispatch(
                UpdateOrganization({
                    reqData: payload,
                    organizationId: organizationId,
                })
            )
                .unwrap()
                .then((res: any) => {
                    console.log('Res----------', res);
                    if (res?.status === 200 || res?.status === 201) {
                        setLocation('/organizations');
                    }
                })
                .finally(endLoader);
        }
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                },
                token: {
                    borderRadius: 4,
                },
            }}
        >
            <div className="flex items-center gap-4">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1A3353]" level={4}>
                    {organizationId === '0'
                        ? `Create Organization`
                        : 'Edit Organization'}
                </Title>
            </div>
            <div className="lg:mt-16 @sm:mt-5">
                <div
                    className={`rounded-lg border  ${
                        organizationId === '0' ? 'lg:w-[80%]' : 'lg:w-[95%] '
                    }   lg:p-16 @sm:p-5`}
                >
                    <Form
                        name="organizationCreate"
                        layout="vertical"
                        size="large"
                        form={form}
                        initialValues={{ remember: true }}
                        onFinish={onFinish}
                        autoComplete="off"
                    >
                        <div className="flex  lg:flex-row lg:gap-8 @sm:flex-col">
                            <div className="w-full px-2 md:w-1/2 lg:mb-4">
                                <Form.Item
                                    label="Organization Name"
                                    name="organizationName"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter organization name!',
                                        },
                                    ]}
                                >
                                    <Input placeholder="Enter Organization Name" />
                                </Form.Item>
                            </div>
                            <div className="w-full px-2 md:w-1/2 lg:mb-4">
                                <Form.Item
                                    label="Email"
                                    name="email"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please enter email!',
                                        },
                                        {
                                            type: 'email',
                                            message:
                                                'The input is not valid E-mail!',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder="Enter email"
                                        disabled={organizationId !== '0'}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex  lg:flex-row lg:gap-8 @sm:flex-col">
                            <div className="w-full px-2 md:w-1/2 lg:mb-4">
                                <Form.Item
                                    label="Phone Number"
                                    name="mobile"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter phone number!',
                                        },
                                    ]}
                                >
                                    <Input
                                        type="text"
                                        maxLength={10}
                                        placeholder="Enter Phone Number"
                                        disabled={organizationId !== '0'}
                                        onInput={(e: any) => {
                                            e.target.value =
                                                e.target.value.replace(
                                                    /[^0-9]/g,
                                                    ''
                                                );
                                        }}
                                    />
                                </Form.Item>
                            </div>
                            <div className="w-full px-2 md:w-1/2 lg:mb-4">
                                <Form.Item
                                    label="Address"
                                    name="address"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please enter address!',
                                        },
                                    ]}
                                >
                                    <Input placeholder="Enter Address" />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex  lg:flex-row lg:gap-8 @sm:flex-col">
                            <div className="w-full px-2 md:w-1/2 lg:mb-4">
                                <Form.Item
                                    label="State"
                                    name="state"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select state!',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        placeholder="Select state"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                ?.toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={CountryOptions}
                                        onChange={handleStateChange}
                                    />
                                </Form.Item>
                            </div>
                            <div className="w-full px-2 md:w-1/2 lg:mb-4">
                                <Form.Item
                                    label="City"
                                    name="city"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select city!',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        disabled={!isSelectedState}
                                        onSearch={(value) =>
                                            setCitySearchText(value)
                                        }
                                        placeholder="Select city"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={CityOptions}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex  lg:flex-row lg:gap-8 @sm:flex-col">
                            <div className="w-full px-2 md:w-1/2 lg:mb-4">
                                <Form.Item
                                    label="Contact Person Name"
                                    name="contactName"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter contact person name!',
                                        },
                                    ]}
                                >
                                    <Input
                                        type="text"
                                        placeholder="Enter Contact Person Name"
                                    />
                                </Form.Item>
                            </div>
                            <div className="w-full px-2 md:w-1/2 lg:mb-4">
                                <Form.Item
                                    label="Postal Code"
                                    name="postalCode"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter your postal code!',
                                        },
                                        {
                                            pattern: /^[0-9]{6}$/,
                                            message:
                                                'Postal Code must be exactly 6 digits and contain only numbers',
                                        },
                                    ]}
                                >
                                    {/* <PhoneInput enableSearch /> */}
                                    <Input
                                        maxLength={6}
                                        placeholder="Enter postal code"
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="w-full px-2 md:w-1/2 lg:mb-4">
                            <div className="rounded-3xl border lg:p-10 @sm:w-full @sm:p-5">
                                <Typography.Title level={5}>
                                    <span className="text-purpleLight ">
                                        UPLOAD LOGO
                                    </span>
                                </Typography.Title>
                                <Upload
                                    id="banner-upload"
                                    name="avatar"
                                    listType="picture-card"
                                    className="avatar-uploader overflow-hidden lg:mt-8 @sm:mt-2"
                                    showUploadList={false}
                                    // action={`${ProjectUrl}/general/upload-image`}
                                    // beforeUpload={beforeUpload}
                                    // onChange={handleChange}
                                    // disabled={organizationId !== '0'}
                                    customRequest={handleImageUpload}
                                >
                                    {imageUrl ? (
                                        <div className="relative h-full w-full">
                                            <img
                                                src={imageUrl}
                                                className="object-contain"
                                                alt="avatar"
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                }}
                                            />
                                            {loading && (
                                                <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/50">
                                                    <LoadingOutlined
                                                        style={{
                                                            fontSize: 24,
                                                            color: '#8143D1',
                                                        }}
                                                        spin
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        uploadButton
                                    )}
                                </Upload>
                            </div>
                        </div>
                        {/* <div className="lg:mb-4">
                            <Button type="dashed" className="w-full">
                                Add Branch
                            </Button>
                        </div> */}
                        <Form.Item className="mt-8 flex flex-row justify-end">
                            <Button
                                htmlType="submit"
                                className="rounded-lg border-purpleLight bg-purpleLight px-10 text-white"
                            >
                                Submit
                            </Button>
                        </Form.Item>
                    </Form>
                </div>
            </div>
        </ConfigProvider>
    );
};

export default CreateOrganization;

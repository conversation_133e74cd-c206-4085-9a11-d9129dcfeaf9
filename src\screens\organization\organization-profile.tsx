import { ConfigProvider, Typography, Form, Tabs } from 'antd';
import { Link, useLocation } from 'wouter';
import ImageUpload from '~/components/common/image-upload-comp';
import BranchListing from './branchListing';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    OrganizationDetails as FetchOrganizationDetails,
    UpdateOrganization,
} from '~/redux/actions/organization-action';
import { CityList, UploadImage } from '~/redux/actions/common-action';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import OrganizationDetails from './organization-details';
import OrganizationPermissions from './organization-permissions';

const { Title } = Typography;

function goBack() {
    window.history.back();
}

const OrganizationProfile = () => {
    const [form] = Form.useForm();
    const [location] = useLocation();
    const organizationId = location.split('/').pop();
    const dispatch = useAppDispatch();
    const [logoUrl, setLogoUrl] = useState<string>();
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        organizationDetails: state.organization_store.organizationDetails,
        countryList: state.common_store.countryList,
    }));

    const CountryOptions = store.countryList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    useEffect(() => {
        startLoader();
        if (organizationId) {
            dispatch(FetchOrganizationDetails({ organizationId }))
                .unwrap()
                .finally(endLoader);
        }
    }, [dispatch, organizationId]);

    useEffect(() => {
        if (store.organizationDetails && organizationId !== '0') {
            const { name, associatedPerson, email, mobile, logo, address } =
                store.organizationDetails;
            dispatch(
                CityList({ cityId: address?.city, page: 1, pageSize: 50 })
            ).then((res: any) => {
                form.setFieldsValue({
                    city: res?.payload?.data?.data[0]?.name,
                });
            });

            form.setFieldsValue({
                organizationName: name,
                contactPersonName: associatedPerson,
                address: address?.addressLine1,
                state: address?.state,
                city: address?.city,
                pinCode: address?.postalCode,
                email,
                phoneNumber: mobile,
                logo,
            });

            setLogoUrl(logo);
        }
    }, [store.organizationDetails, dispatch, form, organizationId]);
    const handleImageUpload = (file: File) => {
        console.log('Uploaded file:', file);
        dispatch(UploadImage({ file })).then((res) => {
            // console.log("Res--------------", res);
            if (
                res?.payload?.res?.status === 200 ||
                res?.payload?.res?.status === 201
            ) {
                const payload: any = {
                    organizationName: store.organizationDetails?.name,
                    // mobile: store.organizationDetails.mobile,
                    // email: store.organizationDetails?.email,
                    address: {
                        stateId: store.organizationDetails?.address?.state,
                        cityId: store.organizationDetails?.address?.city,
                        addressLine1:
                            store.organizationDetails?.address?.addressLine1,
                        postalCode: Number(
                            store.organizationDetails?.address?.postalCode
                        ),
                    },
                    logo: res?.payload?.res?.data?.data,
                    associatedPerson:
                        store.organizationDetails?.associatedPerson,
                };

                 console.log('Data in payload-------------', payload);
                dispatch(
                    UpdateOrganization({
                        reqData: payload,
                        organizationId: organizationId,
                    })
                ).unwrap();
            }
        });
    };
    const tabItems = [
        {
            key: '1',
            label: 'Details',
            children: (
                <OrganizationDetails
                    form={form}
                    store={store}
                    CountryOptions={CountryOptions}
                    organizationId={organizationId}
                />
            ),
        },
        {
            key: '2',
            label: 'Permissions',
            children: (
                <OrganizationPermissions organizationId={organizationId} />
            ),
        },
    ];
    return (
        <ConfigProvider>
            <div>
                {loader ? (
                    <FullLoader state={true} />
                ) : (
                    <>
                        <div className="flex items-center gap-4">
                            <img
                                src="/icons/back.svg"
                                alt="edit"
                                className="h-[10px] cursor-pointer"
                                onClick={goBack}
                            />
                            <Title className="text-[#1A3353]" level={4}>
                                Organization Profile
                            </Title>
                        </div>
                        <div className=" flex rounded-lg pt-10   lg:w-[80%] @sm:w-full">
                            <div className=" lg:w-[15%] @sm:w-full">
                                <ImageUpload
                                    imageUrl={logoUrl && logoUrl}
                                    onUpload={handleImageUpload}
                                />
                            </div>
                        </div>

                        <Tabs defaultActiveKey="1" items={tabItems} />
                    </>
                )}
            </div>
        </ConfigProvider>
    );
};

export default OrganizationProfile;

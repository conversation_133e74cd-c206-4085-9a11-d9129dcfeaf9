import { MoreOutlined } from '@ant-design/icons';
import {
    ConfigProvider,
    Dropdown,
    Form,
    Menu,
    Pagination,
    Select,
    Switch,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { capitalizeFirstLetter } from '~/components/common/function';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { CityList, CountryList } from '~/redux/actions/common-action';
import {
    OrganizationList,
    sendPasswordMail,
    UpdateOrganizationStatus,
} from '~/redux/actions/organization-action';
import { RoleType } from '~/types/enums';
import { setSearchValue } from '~/redux/slices/common-slice';
import { getQueryParams } from '~/utils/getQueryParams';

const columns = [
    {
        title: 'Organization Name',
        dataIndex: '',
        // width: '25%',
        render: (record: any) => {
            const mobileStyle =
                window.innerHeight <= 768 ? {} : { width: '200px' };
            // console.log('Record--------', record);

            return (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        ...mobileStyle,
                    }}
                >
                    <img
                        src={record.image}
                        className="object-cover"
                        // alt={record.organizationName}
                        style={{
                            width: '60px',
                            height: '50px',
                            marginRight: '10px',
                            borderRadius: '10%',
                        }}
                    />
                    <Link to={`${'/organization-profile'}/${record._id}`}>
                        {capitalizeFirstLetter(record.organizationName)}
                    </Link>
                </div>
            );
        },
    },
    {
        title: 'Email',
        dataIndex: 'email',
        ellipses: true,
    },
    {
        title: 'Phone No',
        dataIndex: 'mobile',
    },
    {
        title: 'No of Branches',
        dataIndex: 'numberOfFacilities',
        align: 'center',
    },
    { title: 'Location', dataIndex: 'city' },
];

const OrganizationListing: React.FC = () => {
    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        organizationList: state.organization_store.organizationList,
        organizationListCount: state.organization_store.organizationListCount,
    }));

    const [loader, startLoader, endLoader] = useLoader();

    // console.log('store-----------------', store.organizationList);

    const params = getQueryParams();
    const { role } = useSelector((state: any) => state.auth_store);

    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);

    const searchValue = useSelector(
        (state: any) => state.common_store.searchValue
    );

    const debouncedRequest = useDebounce((callback) => callback(), 300);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    }
    // state and city
    const [selectedState, setSelectedState] = useState<string | null>(null);
    const [selectedCity, setSelectedCity] = useState<string[]>([]);
    const [isSelectedState, setIsSelectedState] = useState<boolean>(false);
    const [organizationData, setOrganizationData] = useState<any>(null);
    const { countryList, cityList } = useSelector(
        (state: any) => state.common_store
    );
    const [citySearchText, setCitySearchText] = useState<string>('');

    // state filter
    const handleStateChange = (value: string) => {
        setSelectedState(value);
        setIsSelectedState(true);
        setSelectedCity([]);
        dispatch(CityList({ stateId: value, page: 1, pageSize: 50 }));
    };

    // Use useEffect to perform actions after selectedState is updated
    useEffect(() => {
        if (selectedState) {
            if (searchValue) {
                debouncedRequest(() => {
                    dispatch(
                        OrganizationList({
                            page: currentPage,
                            pageSize: pageSizes,
                            search: searchValue,
                            stateId: selectedState,
                        })
                    )
                        .unwrap()
                        .then(() => {})
                        .finally(endLoader);
                });
            } else {
                dispatch(
                    OrganizationList({
                        page: currentPage,
                        pageSize: pageSizes,
                        stateId: selectedState, // Use selectedState here
                    })
                )
                    .unwrap()
                    .then(() => {})
                    .finally(endLoader);
            }
        }
    }, [selectedState]); // This effect runs whenever selectedState changes

    useEffect(() => {
        dispatch(CountryList({ page: 1, pageSize: 40 }));
    }, []);

    const CountryOptions = countryList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));
    const onSearch = (value: any) => {
        setCitySearchText(value);
    };
    useEffect(() => {
        if (citySearchText) {
            debouncedRequest(() => {
                dispatch(
                    CityList({
                        stateId: selectedState,
                        page: 1,
                        pageSize: 50,
                        search: citySearchText,
                    })
                );
            });
        }
    }, [citySearchText]);

    const CityOptions = cityList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));
    // state and city end

    useEffect(() => {
        startLoader();
        if (searchValue) {
            debouncedRequest(() => {
                dispatch(
                    OrganizationList({
                        page: currentPage,
                        pageSize: pageSizes,
                        search: searchValue,
                        stateId: selectedState,
                        // cityId: cityId,
                    })
                )
                    .unwrap()
                    .then(() => {})
                    .finally(endLoader);
            });
        } else {
            dispatch(
                OrganizationList({ page: currentPage, pageSize: pageSizes })
            )
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [currentPage, pageSizes, searchValue]);

    const handleCityChange = (value: string[]) => {
        setSelectedCity((prevSelectedCity) => {
            console.log('City is ---------', value);
            if (value.length > 0) {
                if (searchValue) {
                    debouncedRequest(() => {
                        dispatch(
                            OrganizationList({
                                page: currentPage,
                                pageSize: pageSizes,
                                search: searchValue,
                                cityId: value,
                            })
                        )
                            .unwrap()
                            .then(() => {})
                            .finally(endLoader);
                    });
                } else {
                    dispatch(
                        OrganizationList({
                            page: currentPage,
                            pageSize: pageSizes,
                            cityId: value,
                        })
                    )
                        .unwrap()
                        .then(() => {})
                        .finally(endLoader);
                }
            }
            if (value.length === 0) {
                dispatch(
                    OrganizationList({
                        page: currentPage,
                        pageSize: pageSizes,
                        stateId: selectedState,
                    })
                )
                    .unwrap()
                    .then(() => {})
                    .catch((error) => {
                        console.error(
                            'Error fetching organization list:',
                            error
                        );
                    })
                    .finally(() => {
                        endLoader();
                    });
            }
            return value; // Update the state with the new value
        });
    };

    const handleCancelStatusChange = () => {
        setOrganizationData(null);
        setConfirmationModalVisible(false);
    };

    const handleConfirmStatusChange = () => {
        dispatch(
            UpdateOrganizationStatus({
                organizationId: organizationData._id,
                reqData: {
                    status: !organizationData.status,
                },
            })
        );
        setConfirmationModalVisible(false);
        setOrganizationData(null);
    };

    const openConfirmationModal = (record: any) => {
        setOrganizationData(record);
        setConfirmationModalVisible(true);
    };

    const selectColumn = [
        {
            title: 'Status',
            dataIndex: '',
            key: '',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    colorPrimaryBorder: '#8143D1',
                                    colorPrimary: '#8143D1',
                                    colorTextQuaternary: 'gray',
                                    colorFillQuaternary: 'gray',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="swtich-off"
                            checkedChildren="ON"
                            unCheckedChildren="OFF"
                            onChange={() => openConfirmationModal(record)}
                            checked={record.status || false}
                        />
                    </ConfigProvider>
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            width: '120px',
            key: 'action',
            render: (record: any) => {
                const menuItems = [
                    {
                        key: 1,
                        label: (
                            <Link
                                to={`/organization-profile/${record?._id}?orgName=${record.organizationName}`}
                            >
                                View Organization
                            </Link>
                        ),
                    },
                    {
                        key: 2,
                        label: (
                            <Link
                                to={`/create-organization/${record?._id}?orgName=${record.organizationName}`}
                            >
                                Edit Organization
                            </Link>
                        ),
                    },
                    {
                        key: 3,
                        label: (
                            <Link
                                to={`/facility/create-facility/${record?._id}?organizationId=${record?._id}`}
                            >
                                Add Branch
                            </Link>
                        ),
                    },
                    ...(role === RoleType.SUPER_ADMIN && !record.isPasswordReset
                        ? [
                              {
                                  key: 4,
                                  label: (
                                      <div
                                          onClick={() =>
                                              dispatch(
                                                  sendPasswordMail({
                                                      id: record._id,
                                                  })
                                              )
                                          }
                                          style={{ cursor: 'pointer' }}
                                      >
                                          Send Password Mail
                                      </div>
                                  ),
                              },
                          ]
                        : []),
                ];

                return (
                    <>
                        <span className="flex gap-5 ">
                            <div>
                                <Dropdown
                                    menu={{ items: menuItems }}
                                    trigger={['click']}
                                >
                                    <MoreOutlined
                                        style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...selectColumn];

    const handleSearch = (value: string) => {
        dispatch(setSearchValue(value));
    };

    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={combinedColumns}
                dataSource={store.organizationList}
                // addNewModal={true}
                // openModal={showAddStaffModal}
                addNewLink="/create-organization/0"
                addNewTitle="Create Organization"
                heading="Organization"
                onSearch={handleSearch}
                showSearch={true}
                loading={loader}
                organizationFilters={true}
                // for select city
                selectedCity={selectedCity}
                cityDisabled={!isSelectedState}
                onCitySearch={onSearch}
                cityOptions={CityOptions}
                onCityChange={handleCityChange}
                // for select state
                stateOptions={CountryOptions}
                onStateChange={handleStateChange}
            />
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={store.organizationListCount}
                    pageSize={pageSizes}
                    onChange={paginate}
                    // hideOnSinglePage
                />
            </div>

            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
        </>
    );
};

export default OrganizationListing;

import { CloseOutlined } from '@ant-design/icons';
import { Button, DatePicker, Form, Input, Modal, Select } from 'antd';
import React, { useState } from 'react';
import LocalConfigProvider from '~/components/common/local-config-provider';
import PasswordChecklist from '~/components/common/password-checklist';
import PayRates from '~/components/table/pay-rates';

const { Option } = Select;

interface ITestScreen {}

const CLassPayRates: React.FC<ITestScreen> = () => {
    const [password, setPassword] = useState('');

    return (
        <>
            <LocalConfigProvider>
                <Modal
                    open={false}
                    title={
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                            }}
                        >
                            <span>Create Staff Login</span>
                            <CloseOutlined
                                // onClick={handleCancel}
                                style={{ cursor: 'pointer' }}
                            />
                        </div>
                    }
                    footer={null}
                    closeIcon={false}
                    // onOk={handleOk}
                    // onCancel={handleCancel}
                >
                    <Form
                        name="add-trainer"
                        layout="vertical"
                        size="large"
                        autoComplete="off"
                        initialValues={{ country: 'india' }}
                        // form={form}
                    >
                        <Form.Item
                            label="First Name"
                            name="firstName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input first name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter First Name" />
                        </Form.Item>
                        <Form.Item
                            label="Last Name"
                            name="lastName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input lasst name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Last Name" />
                        </Form.Item>
                        <Form.Item
                            label="Email"
                            name="email"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input email',
                                },
                                {
                                    type: 'email',
                                    message: 'The input is not valid E-mail!',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Email" />
                        </Form.Item>
                        <Form.Item
                            label="Password"
                            name="password"
                            rules={[
                                {
                                    required: true,
                                    // message: 'Please input password',
                                },
                            ]}
                        >
                            <Input.Password
                                placeholder="Enter Password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                            />
                        </Form.Item>
                        <PasswordChecklist password={password} />

                        <Form.Item
                            label="Country"
                            name="country"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select country',
                                },
                            ]}
                        >
                            <Select
                                placeholder="Select Country"
                                disabled={true}
                            >
                                <Option value="india">India</Option>
                            </Select>
                        </Form.Item>
                        <div className="flex flex-row justify-end">
                            <Form.Item>
                                <div style={{ display: 'flex', gap: '10px' }}>
                                    <Button type="primary" htmlType="submit">
                                        Confirm
                                    </Button>
                                </div>
                            </Form.Item>
                        </div>
                    </Form>
                </Modal>
            </LocalConfigProvider>

            {/* ----------------payrates table--------------------- */}
            <PayRates />
        </>
    );
};

export default CLassPayRates;

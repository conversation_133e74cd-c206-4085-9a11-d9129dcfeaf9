import { Button, Table } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useParams } from 'wouter';
import { formatDate } from '~/components/common/function';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    DownloadInvoice,
    OrderInvoiceDetails,
} from '~/redux/actions/purchased-action';
import SharePassModal from '~/screens/customers/share-pass-modal';
import { RoleType, SUBJECT_TYPE } from '~/types/enums';

const OrderConfirmation = () => {
    const [_, setLocation] = useLocation();
    const [sharePassData, setSharePassData] = useState<any>(false);
    const [packageList, setPackageList] = useState<any>();
    const [orderData, setOrderData] = useState<any>();
    const [loader, startLoader, endLoader] = useLoader(true);
    const { orderId } = useParams();
    const dispatch = useAppDispatch();

    useEffect(() => {
        startLoader();
        if (orderId) {
            dispatch(OrderInvoiceDetails({ orderId: orderId }))
                .unwrap()
                .then((res: any) => {
                    const data = res.data.data;
                    const purchaseItems = data?.purchaseItems?.length
                        ? data?.purchaseItems.map((item: any, i: number) => ({
                              key: i + 1,
                              srNo: i + 1,
                              product: item.name,
                              quantity: item.quantity,
                              price: item.price,
                              sessionType: item.sessionType,
                              classType: item.classType,
                              bundledPricingId: item.isBundledPricing
                                  ? item.packageId
                                  : null,
                              packageId: item.isBundledPricing
                                  ? null
                                  : item.packageId,
                          }))
                        : [];
                    const productItems = data?.productItem?.length
                        ? data?.productItem.map((item: any, i: number) => ({
                              key: purchaseItems.length + i + 1,
                              srNo: purchaseItems.length + i + 1,
                              product: item.name,
                              quantity: item.quantity,
                              price: item.price,
                              isProduct: true,
                          }))
                        : [];
                    const customPackageItems = data?.customPackageItems?.length
                        ? data?.customPackageItems.map(
                              (item: any, i: number) => ({
                                  key:
                                      purchaseItems.length +
                                      productItems.length +
                                      i +
                                      1,
                                  srNo:
                                      purchaseItems.length +
                                      productItems.length +
                                      i +
                                      1,
                                  product: item.name,
                                  quantity: item.quantity,
                                  price: item.price,
                              })
                          )
                        : [];
                    setPackageList([
                        ...purchaseItems,
                        ...productItems,
                        ...customPackageItems,
                    ]);
                    setOrderData(data);
                })
                .finally(endLoader);
        }
    }, [orderId]);

    const handleDownloadInvoice = async () => {
        if (!orderId) return;

        try {
            const response = await dispatch(
                DownloadInvoice({ orderId })
            ).unwrap();

            if (!response) {
                throw new Error('No URL received from API.');
            }

            window.open(response, '_blank', 'noopener,noreferrer');
        } catch (error) {
            console.error('Error downloading invoice:', error);
        }
    };

    const columns = [
        {
            title: 'Sr. No.',
            dataIndex: 'srNo',
            key: 'srNo',
        },
        {
            title: 'Product',
            dataIndex: 'product',
            key: 'product',
        },
        {
            title: ' Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
        },
        {
            title: 'Unit Price',
            dataIndex: 'price',
            key: 'price',
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (record: any) =>
                !record.isProduct &&
                record.classType !== 'courses' &&
                ['day_pass', 'multiple'].includes(record.sessionType) && (
                    <Button
                        htmlType="button"
                        className="h-10   bg-purpleLight  text-white "
                        onClick={() =>
                            setSharePassData({
                                visible: true,
                                bundledPricingId: record?.bundledPricingId,
                                packageId: record?.packageId,
                            })
                        }
                    >
                        <p className="text-lg"> Share Pass </p>
                    </Button>
                ),
        },
    ];
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasPricingPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) => subject.type === SUBJECT_TYPE.ORDER
            )
        );
    }, [all_permissions_for_role]);
    return (
        <div className="">
            <div className="flex flex-row justify-between">
                <div className="flex flex-row items-end gap-6 pb-12">
                    <Title className=" text-[#1a3353]" level={4}>
                        Order Detail
                    </Title>
                    <p className=" text-[#455560] lg:text-[16px]">
                        Order ID: {orderData?.orderId}
                    </p>
                </div>
                <div className="flex  flex-row items-center gap-4">
                    {['completed', 'refund'].includes(
                        orderData?.paymentStatus
                    ) && (
                        <Button
                            onClick={handleDownloadInvoice}
                            htmlType="button"
                            className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                        >
                            <p> Print Receipt </p>
                        </Button>
                    )}
                    {(hasPricingPermission ||
                        store.role === RoleType.ORGANIZATION) && (
                        <Button
                            htmlType="button"
                            className="h-16  w-[110px]  bg-purpleLight text-white "
                            onClick={() => setLocation('/order-listing')}
                        >
                            <p>Order History </p>
                        </Button>
                    )}
                </div>
            </div>
            <div className="flex flex-row items-start gap-4">
                <div className="w-[70%] rounded-md border border-gray-200 px-5 py-5">
                    <p className=" font-semibold text-[#1a3353] lg:text-[16px]">
                        Order contents
                    </p>

                    <div>
                        <Table
                            dataSource={packageList}
                            columns={columns}
                            pagination={false}
                        />
                    </div>
                </div>
                <div className="w-[30%] rounded-md border border-gray-200 p-5">
                    <p className=" font-semibold  text-[#1a3353] lg:text-[16px]">
                        Order summary
                    </p>
                    <div className="flex flex-col gap-2">
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Client Name :</p>
                            <p className="text-[#455560]">
                                {orderData?.clientDetails.name}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Purchase Date :</p>
                            <p className="text-[#455560]">
                                {formatDate(orderData?.invoiceDate)}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Payment Status :</p>
                            <p className="text-[#455560]">
                                {orderData?.paymentStatus}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Item Total :</p>
                            <p className="text-[#455560]">
                                ₹{orderData?.subTotal}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Discount :</p>
                            <p className="text-[#455560]">
                                {orderData?.discount}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">
                                Cart Discount(
                                {orderData?.cartDiscountType === 'Flat'
                                    ? `₹ ${orderData?.cartDiscount}`
                                    : `${orderData?.cartDiscount} %`}
                                ) :
                            </p>
                            <p className="text-[#455560]">
                                {orderData?.cartDiscountAmount}
                            </p>
                        </div>
                        {orderData?.clientBillingDetails?.utCode ===
                        orderData?.billingDetails?.utCode ? (
                            <>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">CGST :</p>
                                    <p className="text-[#455560]">
                                        {orderData?.cgst}
                                    </p>
                                </div>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">SGST :</p>
                                    <p className="text-[#455560]">
                                        {orderData?.sgst}
                                    </p>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">IGST :</p>
                                    <p className="text-[#455560]">
                                        {orderData?.igst}
                                    </p>
                                </div>
                            </>
                        )}
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Total Amount </p>
                            <p className="text-[#455560]">
                                ₹{orderData?.totalAmountAfterGst}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Round Off </p>
                            <p className="text-[#455560]">
                                ₹ -{orderData?.roundOff}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Grand Total :</p>
                            <p className="text-[#455560]">
                                ₹{Math.floor(orderData?.grandTotal)}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            {/* <div className=" mx-auto  flex w-[33%] flex-row items-center gap-4 pt-20">
                <Button
                    htmlType="button"
                    className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                >
                    <p> Print Receipt </p>
                </Button>
                <Button
                    htmlType="button"
                    className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                    onClick={() => setLocation('/order-listing')}
                >
                    <p>Order History </p>
                </Button>
            </div> */}
            {sharePassData?.visible && (
                <SharePassModal
                    visible={sharePassData?.visible}
                    onClose={() => setSharePassData(null)}
                    clientId={orderData?.userId}
                    clientName={orderData?.clientDetails.name}
                    invoiceId={orderData?._id}
                    bundledPricingId={sharePassData?.bundledPricingId}
                    packageId={sharePassData?.packageId}
                />
            )}
        </div>
    );
};

export default OrderConfirmation;

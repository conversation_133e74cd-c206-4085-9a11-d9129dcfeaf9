import React, { useEffect, useState } from 'react';
import { ConfigProvider, Tabs, Radio, Typography, Button } from 'antd';
import CashPayment from './payment components/cash-payment';
import { goBack } from '~/components/common/function';
import CreditCardPayment from './payment components/credit-card';
import { getQueryParams } from '~/utils/getQueryParams';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { OrderInvoiceDetails } from '~/redux/actions/purchased-action';
import { SetPosPurchasedData } from '~/redux/slices/purchaged-slice';
import FullLoader from '~/components/library/loader/full-loader';
import AddressSelectionModal from '~/components/common/address-modal';
import SplitPayment from '~/screens/payment pages/payment components/split-payment';
const { Title } = Typography;
import { FacilitiesList } from '~/redux/actions/facility-action';
import { AddedPaymentMethodList } from '~/redux/actions/payment-method.action';

// Tabs Configuration
const tabs = [
    {
        key: 'credit',
        title: 'Credit Card',
        icon: '/icons/payment icons/credit-1.png',
    },
    {
        key: 'cash',
        title: 'Cash',
        icon: '/icons/payment icons/cash.png',
    },
    // {
    //     key: 'razorpay',
    //     title: 'Razorpay',
    //     icon: '/icons/payment icons/upi.png',
    // },
    // {
    //     key: 'upi',
    //     title: 'UPI',
    //     icon: '/icons/payment icons/upi.png',
    // },

    // {
    //     key: 'netbanking',
    //     title: 'Net Banking',
    //     icon: '/icons/payment icons/nb.png',
    // },
    // {
    //     key: 'wallet',
    //     title: 'Wallet',
    //     icon: '/icons/payment icons/wallet.png',
    // },

    {
        key: 'splitPayment',
        title: 'Split Payment',
        icon: '/icons/payment icons/upi.png',
    },
];

const PaymentScreen: React.FC = () => {
    const [activeTab, setActiveTab] = useState<string>('credit');
    const params = getQueryParams();
    const orderId = params.orderId;
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const [isAddressModalOpen, setIsAddressModalOpen] = useState(false);
    const [selectedFacilityId, setSelectedFacilityId] = useState<string | null>(
        null
    );
    const [paymentMethodTab, setPaymentTab] = useState<any>([]);
    const paymentMode = params.mode;

    const store = useAppSelector((state) => ({
        posPurchasedData: state.purchased_store.posPuchasedData,
        orderInvoiceDetail: state.purchased_store.orderInvoiceDetail,
        clientDetails: state.common_store.clientDetails,
    }));
    const address = store.clientDetails?.address || {};
    const businessAddress = store.clientDetails?.businessAddress || {};
    const defaultAddress = address?.isDefault
        ? address
        : businessAddress?.isDefault
        ? businessAddress
        : address;
    {
        store.orderInvoiceDetail?.clientDetails?.city
            ? `${store.orderInvoiceDetail?.billingDetails?.city}, ${store.orderInvoiceDetail?.billingDetails?.state}`
            : store.orderInvoiceDetail?.clientDetails?.state;
    }

    const [selectedAddress, setSelectedAddress] = useState({
        id: defaultAddress?._id || 'default',
        name: `${store.clientDetails?.firstName} ${store.clientDetails?.lastName}`,
        address: [
            defaultAddress?.addressLine1 || '',
            defaultAddress?.addressLine2 || '',
            defaultAddress?.postalCode || '',
        ]
            .filter(Boolean)
            .join(', '),
        isDefault: defaultAddress?.isDefault || false,
    });

    useEffect(() => {
        if (selectedAddress) {
            dispatch(
                SetPosPurchasedData({
                    ...store.posPurchasedData,
                    billingAddressId: selectedAddress?.id,
                })
            );
        }
    }, [selectedAddress]);

    const handleAddressSelect = (selected: any) => {
        setSelectedAddress(selected);
        dispatch(
            SetPosPurchasedData({
                ...store.posPurchasedData,
                billingAddressId: selected?.id,
            })
        );
    };

    const addresses = [address, businessAddress]
        .filter((addr) => addr?.state)
        .map((addr) => ({
            id: addr?._id,
            name:
                addr?.businessName ||
                `${store.clientDetails?.firstName} ${store.clientDetails?.lastName}`,
            address: [
                addr?.addressLine1 || '',
                addr?.addressLine2 || '',
                addr?.postalCode || '',
            ]
                .filter(Boolean)
                .join(', '),
            isDefault: addr?.isDefault || false,
        }));

    useEffect(() => {
        const fetchData = async () => {
            try {
                startLoader();

                let orderData: any = {}; // Only used if orderId exists
                let paymentDetails: any[] = [];

                // Step 1: If orderId exists, fetch order details
                if (orderId) {
                    const res: any = await dispatch(
                        OrderInvoiceDetails({ orderId })
                    ).unwrap();
                    const data = res?.data?.data;
                    orderData = {
                        userId: data?.user?._id,
                        platform: data?.platform,
                        organizationId: data?.organizationId,
                        facilityId: data?.facilityId,
                        purchaseItems: data?.purchaseItems?.map(
                            (item: any) => ({
                                ...item,
                                packageId: item._id,
                                quantity: item.quantity || 1,
                                discountedValue: item.discountValue || 0,
                                price: (item.price || 0) * (item.quantity || 1),
                            })
                        ),
                        customPackageItems: data?.customPackageItems?.map(
                            (item: any) => ({
                                ...item,
                                packageId: item._id,
                                quantity: item.quantity || 1,
                                discountedValue: item.discountValue || 0,
                                price: (item.price || 0) * (item.quantity || 1),
                            })
                        ),
                        discount: data?.discount,
                        subTotal: data?.subTotal,
                        total: data?.totalAmountAfterGst,
                        promoCode: data?.promoCode,
                        amountPaid: data?.total,
                    };

                    paymentDetails = data.paymentDetails;
                }

                // Step 2: Fetch Facilities
                const facilitiesRes: any = await dispatch(
                    FacilitiesList({ page: 1, pageSize: 30 })
                ).unwrap();
                const facilities = facilitiesRes?.data?.data?.list;
                if (!facilities?.length) throw new Error('No facilities found');

                const facilityId = facilities[0]._id;
                setSelectedFacilityId(facilityId);

                // Step 3: Fetch Payment Methods
                const paymentRes: any = await dispatch(
                    AddedPaymentMethodList({ facilityId, isActive: true })
                ).unwrap();

                const paymentMethods =
                    paymentRes?.data?.data.map((res: any) => ({
                        key: res.shortId,
                        title: res.name,
                        paymentMethodId: res._id,
                    })) || [];

                const cashAndSplit = paymentMethods.filter(
                    (pm) => pm.key === 'cash' || pm.key === 'splitPayment'
                );
                const otherMethods = paymentMethods.filter(
                    (pm) => pm.key !== 'cash' && pm.key !== 'splitPayment'
                );

                const sortedTab = [...otherMethods, ...cashAndSplit];
                if (!sortedTab.length)
                    throw new Error('No payment methods found');
                if (!orderId) {
                    paymentDetails = [
                        {
                            paymentMethod: sortedTab[0]?.key,
                            paymentMethodId: sortedTab[0]?.paymentMethodId,
                            amount: 0,
                            paymentDate: new Date(),
                            paymentStatus: 'pending',
                            paymentGateway: '',
                            description: 'Walk-in payment',
                        },
                    ];
                    dispatch(
                        SetPosPurchasedData({
                            ...store.posPurchasedData,
                            paymentDetails,
                        })
                    );
                } else {
                    dispatch(
                        SetPosPurchasedData({
                            ...orderData,
                            facilityId,
                            paymentDetails,
                        })
                    );
                }

                setPaymentTab(sortedTab);
                setActiveTab(paymentDetails[0]?.paymentMethod);
            } catch (err) {
                console.error('❌ fetchData error:', err);
            } finally {
                endLoader();
            }
        };

        fetchData();
    }, [orderId]);

    // useEffect(() => {
    //     if (paymentMethodTab.length) {
    //         // store.posPurchasedData = {
    //         //     ...store.posPurchasedData,
    //         //     paymentDetails: store.posPurchasedData.paymentDetails.map(
    //         //         (payment: any) => ({
    //         //             ...payment,
    //         //             paymentMethodId: paymentMethodTab[0].paymentMethodId,
    //         //             paymentMethod: paymentMethodTab[0].key,
    //         //         })
    //         //     ),
    //         // };
    //
    //     }
    // }, [activeTab, paymentMethodTab]);

    useEffect(() => {
        if (paymentMode) {
            setActiveTab(paymentMode);
        }
    }, [paymentMode]);

    const handleTabChange = async (item: any) => {
        // Updating paymentDetails array in store.purchaseData
        if (item !== 'splitPayment') {
            store.posPurchasedData = {
                ...store.posPurchasedData,
                paymentDetails: store.posPurchasedData.paymentDetails.map(
                    (payment: any) => ({
                        ...payment,
                        paymentMethodId: item?.paymentMethodId,
                        paymentMethod: item?.key,
                    })
                ),
            };

            dispatch(
                SetPosPurchasedData({
                    ...store.posPurchasedData,
                    paymentDetails: store.posPurchasedData.paymentDetails.map(
                        (payment: any) => ({
                            ...payment,
                            paymentMethodId: item?.paymentMethodId,
                            paymentMethod: item?.key,
                        })
                    ),
                })
            );
        }

        setActiveTab(item.key);
    };

    // useEffect(() => {
    //     startLoader();

    //     }
    //     endLoader()
    // }, [])
    return (
        <>
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <>
                    <div className="">
                        <div className="mb-8 flex w-full  lg:flex-row @sm:flex-col  @sm:gap-3">
                            <div className="flex w-[30%] flex-row items-center gap-5">
                                <img
                                    src="/icons/back.svg"
                                    alt="edit"
                                    className="h-[10px] cursor-pointer"
                                    onClick={goBack}
                                />
                                <Title className=" text-[#1a3353]" level={4}>
                                    Select a Payment Method
                                </Title>
                            </div>
                            {!orderId && (
                                <div className="flex w-[65%] items-start justify-between rounded-lg border border-[#1A3353] px-6 py-5 ">
                                    <div className="flex w-[60%] flex-col gap-3">
                                        <p className="text-16 font-medium text-[#1A3353]">
                                            Delivering to {selectedAddress.name}
                                        </p>
                                        <p className="text-14 text-[#1A3353] ">
                                            {selectedAddress.address}
                                        </p>
                                    </div>
                                    <div
                                        className="cursor-pointer font-medium text-[#8143D1]"
                                        onClick={() =>
                                            setIsAddressModalOpen(true)
                                        }
                                    >
                                        Change
                                    </div>
                                </div>
                            )}

                            {/* <div className="@sm:hidden"></div> */}
                        </div>
                    </div>

                    <div className="flex w-full lg:flex-row @sm:flex-col">
                        <div className="flex flex-col gap-12 lg:w-[30%]">
                            {paymentMethodTab.map((item: any, index: any) => (
                                <div key={item.key}>
                                    <div className="flex flex-row items-center gap-2">
                                        <Radio
                                            checked={activeTab === item.key}
                                            onChange={() =>
                                                handleTabChange(item)
                                            }
                                            className="ml-4"
                                        />
                                        <p className="mr-10">{item.title}</p>
                                        {/* <img
                                            src={item.icon}
                                            width={30}
                                            height={18}
                                            alt="payment method icon"
                                        /> */}
                                    </div>
                                    {/* Conditionally render the content below the radio button */}
                                    <div className="lg:hidden">
                                        {activeTab === item.key && (
                                            <div className="mt-4 lg:w-[65%]">
                                                {item.key === 'cash' && (
                                                    <CashPayment />
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="lg:w-[65%] @sm:hidden">
                            {activeTab === 'cash' && (
                                <>
                                    <div className="w-[70%]">
                                        <p className="text-[#1A3353] lg:text-4xl @sm:text-right">
                                            Balance Due : ₹{' '}
                                            {Math.floor(
                                                store.posPurchasedData?.total
                                            )}
                                            /-
                                        </p>
                                    </div>

                                    <CashPayment
                                        isMarkedAsPaid={orderId ? true : false}
                                    />
                                </>
                            )}
                            {activeTab !== 'cash' &&
                                activeTab !== 'splitPayment' && (
                                    <>
                                        <div className="w-[70%]">
                                            <p className="text-[#1A3353] lg:text-4xl @sm:text-right">
                                                Balance Due : ₹{' '}
                                                {Math.floor(
                                                    store.posPurchasedData
                                                        ?.total
                                                )}
                                                /-
                                            </p>
                                        </div>
                                        <CreditCardPayment
                                            isMarkedAsPaid={
                                                orderId ? true : false
                                            }
                                        />
                                    </>
                                )}

                            {activeTab === 'splitPayment' && (
                                <>
                                    <div className="w-[70%]">
                                        <p className="text-[#1A3353] lg:text-4xl @sm:text-right">
                                            Balance Due : ₹{' '}
                                            {Math.floor(
                                                store.posPurchasedData?.total
                                            )}
                                            /-
                                        </p>
                                    </div>
                                    <SplitPayment
                                        isMarkedAsPaid={orderId ? true : false}
                                        paymentMethodList={paymentMethodTab}
                                    />
                                </>
                            )}
                        </div>
                    </div>
                    <AddressSelectionModal
                        open={isAddressModalOpen}
                        onClose={() => setIsAddressModalOpen(false)}
                        addresses={addresses}
                        onSelect={handleAddressSelect}
                    />
                </>
            )}
        </>
    );
};

export default PaymentScreen;

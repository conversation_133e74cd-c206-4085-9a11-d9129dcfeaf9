import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
    Button,
    Checkbox,
    ConfigProvider,
    Input,
    Select,
    Table,
    Tabs,
} from 'antd';
import { useLocation, useParams } from 'wouter';
import Title from 'antd/es/typography/Title';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { ClientsDetails, CustomerList } from '~/redux/actions/customer-action';
import { getQueryParams } from '~/utils/getQueryParams';
import {
    ArrowLeftOutlined,
    ArrowRightOutlined,
    DeleteFilled,
    DeleteOutlined,
    EditOutlined,
    LoadingOutlined,
    PlusOutlined,
} from '@ant-design/icons';
import {
    CustomPricingDetails,
    CustomPricingListAPI,
    PricingListByActiveStatus,
    PurchasePricingPackages,
} from '~/redux/actions/pricing-actions';
import { capitalizeFirstLetter, goBack } from '~/components/common/function';
import { useDebounce } from '~/hooks/useDebounce';
import { featureListing, getFeatureById } from '~/redux/actions/feature-action';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import dayjs from 'dayjs';
import {
    SetPosPurchasedData,
    UpdatePosPurchasedData,
} from '~/redux/slices/purchaged-slice';
import Alertify from '~/services/alertify';
import ReconciliationModal from './reconciliation-modal';
import CommonTable from '~/components/common/commonTable';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { storeInventory } from '~/redux/actions/merchandise/inventory-action';

import AddClientModal from '../customers/add-client-modal';
import EditPackageModal from './edit-package-modal';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { useSelector } from 'react-redux';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import ChangeDiscountModal from '~/screens/pointOfSale/change-discount-modal';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';

const { Option } = Select;
import { cartValidate } from '~/redux/actions/purchased-action';

const PointOfSales: React.FC = () => {
    const { clientId, userId } = useParams();
    const [mainActiveTab, setMainActiveTab] = useState<string>('packages');
    const [activeTab, setActiveTab] = useState<any>('');
    const [shouldShowSelect, setShouldShowSelect] = useState<boolean>(false);
    const [location] = useLocation();
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedClient, setSelectClient] = useState<any>('');
    const [discountErrorMessage, setDiscountErrorMessage] = useState<any>('');
    const [selectedClientUserId, setSelectClientUserId] = useState<any>(userId);
    const [selectedData, setSelectedData] = useState<any[]>([]);
    const [searchPricing, setSearchPricing] = useState('');
    const [searchClient, setSearchClient] = useState('');
    const debouncedRequest = useDebounce((callback) => callback(), 200);
    const [featuredLoader, startFeaturedLoader, endFeaturedLoader] =
        useLoader(true);
    const [loader, startLoader, endLoader] = useLoader(true);
    const [tableLoader, startTableLoader, endTableLoader] = useLoader(true);
    const [page, setPage] = useState(1);
    const [showAddClientModal, setShowAddClientModal] = useState(false);
    const [isViewOnly, setIsViewOnly] = useState(false);
    const [customPackageDetails, setCustomPackageDetails] = useState(null);

    const [selectedFacilityId, setSelectedFacilityId] = useState('');
    const [selectedFacilityName, setSelectedFacilityName] =
        useState<string>('');
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(false);
    const [discountModalVisible, setDiscountModalVisible] =
        useState<boolean>(false);
    const [discountedItem, setDiscountedItem] = useState<any>({});
    const [inputEnabled, setInputEnabled] = useState<boolean>(false);
    const [isZOutPin, setIsZOutPin] = useState(false);
    const [requirePinModal, setRequirePinModal] = useState<boolean>(false);

    const [calculation, setCalculation] = useState<any>({
        gst: 0,
        subTotal: 0,
        total: 0,
        // promoCode: '',
        discountType: 'Percentage',
        cartDiscount: 0,
        cartDiscountAmount: 0,
    });
    const [isModalVisible, setIsModalVisible] = useState(false);
    const { role, userId: loginUserId } = useSelector(
        (state: any) => state.auth_store
    );

    const closeModal = () => setIsModalVisible(false);
    const [simplePackage, setSimplePackage] = useState('simple');

    useEffect(() => {
        dispatch(FacilitiesList({}))
            .unwrap()
            .then((res: any) => {
                console.log('firstFacility=================', res);
                if (res?.status === 200 || res?.status === 201) {
                    console.log(
                        'firstFacility================= selectedFacilityId',
                        selectedFacilityId
                    );
                    setSelectedFacilityId(res?.data?.data?.list?.[0]?._id);
                }
            });
        dispatch(storeInventory()).unwrap();
    }, []);

    useEffect(() => {
        const payload = {
            facilityId: selectedFacilityId,
        };

        if (selectedFacilityId) {
            dispatch(CustomPricingListAPI(payload));
        }
    }, [selectedFacilityId]);

    const [editPackageModal, setEditPackageModal] = useState(false);

    const showEditPackageModal = () => {
        setEditPackageModal(true);
    };

    const hideEditPackageModal = () => {
        setEditPackageModal(false);
    };

    const store = useAppSelector((state) => ({
        customerDetails: state.customer_store.customerDetails,
        organizationId: state.auth_store.organizationId,
        pricingListByActiveStatus:
            state.pricing_store.pricingListByActiveStatus,
        pricingListByActiveStatusCount:
            state.pricing_store.pricingListByActiveStatusCount,
        featureTabList: state.fetaureTab_store.featureList,
        featureTabDetails: state.fetaureTab_store.featureDetails,
        customerListForForms: state.customer_store.customerListForForms,
        posPurchasedData: state.purchased_store.posPuchasedData,
        inventoryList: state.inventory_store.storeInventoryList,
        facilityList: state.facility_store.facilityList,
        facilityListCount: state.facility_store.facilityListCount,
        customPackagesList: state.pricing_store.customPricingList,
    }));
    const posDataRef = useRef<any>(null);

    useEffect(() => {
        if (Object.keys(store.posPurchasedData || {}).length > 0) {
            posDataRef.current = store.posPurchasedData;
        }
    }, [store.posPurchasedData]);

    // useEffect(() => {
    //     if (role === RoleType.ORGANIZATION) return;

    //     dispatch(
    //         GetSettingActiveStatus({
    //             settingKey: 'settings_pin',
    //         })
    //     ).then((response: any) => {
    //         const settingData = response?.payload?.data?.data;
    //         const isEnabled = settingData?.isEnabled;
    //         const isActive = settingData?.isActive;

    //         if (isEnabled && isActive) {
    //             setRequirePinModal(true);
    //         } else {
    //             setRequirePinModal(false);
    //             setPinModalVisible(false);
    //         }
    //     });
    // }, [role, dispatch]);

    useEffect(() => {
        if (
            store.posPurchasedData &&
            Object.keys(store.posPurchasedData).length === 0 &&
            posDataRef.current
        ) {
            dispatch(SetPosPurchasedData(posDataRef.current));
        }
    }, [store.posPurchasedData]);

    const address = store.customerDetails?.address || {};
    const businessAddress = store.customerDetails?.businessAddress || {};
    const defaultAddress = address?.isDefault
        ? address
        : businessAddress?.isDefault
        ? businessAddress
        : address;

    const getUpdateStorePayload = (data = selectedData) => {
        const customPackageItems = data
            ?.filter((item) => item.isCustomPackage)
            ?.map((item) => ({
                ...item,
                customPackageId: item._id,
                quantity: item.quantity || 1,
                price: item.price,
                name: item.name,
                discountedValue: item.discountedValue || 0,
                tax: item.tax,
            }));

        const purchaseItems = data
            ?.filter((item) => !item.isCustomPackage)
            ?.map((item) => ({
                ...item,
                packageId: item.isPackage ? item._id : undefined,
                productId: item.isProduct ? item.productId : undefined,
                productVariantId: item.isProduct
                    ? item.productVariantId
                    : undefined,
                quantity: item.quantity || 1,
                price: item.price,
                tax: item.tax,
                discountedValue: item.discountedValue || 0,
                salePrice: item.salePrice || item.price,
                mrp: item.mrp || item.price,
            }));

        return {
            userId: !clientId && !userId ? selectedClientUserId : userId,
            clientId: clientId || selectedClient?.value || selectedClient,
            platform: 'Web',
            organizationId: store.organizationId,
            facilityId: selectedFacilityId,
            billingAddressId: defaultAddress?._id,
            purchaseItems,
            customPackageItems,
            cartDiscountType: calculation.discountType,
            cartDiscount: Number(calculation.cartDiscount) || 0,
            cartDiscountAmount: Number(calculation.cartDiscountAmount) || 0,
            subTotal: calculation.subTotal,
            total: calculation.total,
            amountPaid: calculation.total,
            gst: calculation.gst,
        };
    };

    const validateCart = async ({
        items = selectedData,
        userId = selectedClientUserId,
        discountType = calculation.discountType,
        cartDiscount = calculation.cartDiscount,
        facilityId = selectedFacilityId,
        updateStore = true,
    }) => {
        if (!items.length) return;
        const payload = {
            facilityId,
            userId,
            discount: {
                type: discountType,
                value: Number(cartDiscount),
                discountedBy: loginUserId,
            },
            items: items.map((item: any) => {
                const itemType = item?.isPackage
                    ? 'service'
                    : item?.isProduct
                    ? 'product'
                    : 'custom_package';
                const promotion = item?.promotion;
                return {
                    itemType,
                    itemId: item?._id,
                    quantity: item.quantity,
                    variantId: item?.isProduct ? item?.variantId : null,
                    promotionId: promotion?._id,
                    ...(promotion &&
                        !promotion?._id && {
                            discount: {
                                type: promotion?.type,
                                value: Number(promotion?.value),
                                discountedBy: loginUserId,
                            },
                        }),
                };
            }),
        };
        const response = await dispatch(cartValidate(payload)).unwrap();
        const data = response.data;
        setCalculation((prev: any) => ({
            ...prev,
            gst: data?.totalTax ?? 0,
            subTotal: data?.subTotalAfterItemLevelDiscount ?? 0,
            total: data?.grandTotal ?? 0,
            cartDiscountAmount: data?.cartDiscountAmount ?? 0,
        }));
        const updatedSelectedData = data.items.reduce(
            (acc: any[], item: any) => {
                const matchedItem = items.find(
                    (sel: any) => item._id === sel._id
                );
                if (matchedItem) {
                    acc.push({
                        ...matchedItem,
                        discountedValue: item?.discountAmount || 0,
                    });
                }
                return acc;
            },
            []
        );
        setSelectedIds(updatedSelectedData.map((item: any) => item._id));
        if (items.length !== updatedSelectedData.length)
            dispatch(
                PricingListByActiveStatus({
                    page: 1,
                    pageSize: 30,
                    search: searchPricing,
                    fetchWithBundled: true,
                })
            );
        const payloadData = getUpdateStorePayload(updatedSelectedData);
        if (Object.keys(store.posPurchasedData || {}).length > 0 && updateStore)
            dispatch(UpdatePosPurchasedData(payloadData));
        setSelectedData(updatedSelectedData);
    };

    const validateCartDebounced = useDebounce((data) => {
        validateCart(data);
    }, 2000);

    useEffect(() => {
        if (!selectedFacilityId) return;
        const posData = store.posPurchasedData;

        if (
            posData &&
            Object.keys(posData).length > 0 &&
            Array.isArray(posData.purchaseItems)
        ) {
            const {
                userId = '',
                purchaseItems = [],
                customPackageItems = [],
            } = posData;

            // Set selected client safely
            const client: any = store.customerListForForms?.find(
                (customer: any) => customer.userId === userId
            );
            if (client) {
                setSelectClient(client._id ?? '');
                setSelectClientUserId(client.userId);
            } else {
                setSelectClient('');
                setSelectClientUserId(null);
            }

            // Set selected packages safely
            const allItems = [
                ...(Array.isArray(purchaseItems) ? purchaseItems : []),
                ...(Array.isArray(customPackageItems)
                    ? customPackageItems
                    : []),
            ];

            const preselectedPackages = allItems.map((item: any) => ({
                _id: item.packageId
                    ? item.packageId
                    : item.customPackageId
                    ? item.customPackageId
                    : item.isProduct
                    ? item.inventoryId
                    : '',
                name: item.name ?? '',
                price: item.price ?? 0,
                quantity: item.quantity ?? 1,
                tax: item.tax ?? 0,
                discountedValue: 0,
                isPackage: item.isPackage || !!item.packageId,
                isProduct: item.isProduct || false,
                isCustomPackage: !!item.customPackageId,
                productId: item.productId || undefined,
                productVariantId: item.productVariantId || undefined,
                inventoryId: item.inventoryId || undefined,
                productType: item?.productType,
            }));
            validateCartDebounced({
                items: preselectedPackages,
                updateStore: false,
            });
            setSelectedData(preselectedPackages);
            // Set selected package IDs safely
            setSelectedIds(
                preselectedPackages?.map((item: any) => item._id ?? '')
            );
        }
    }, [store.customerListForForms]);

    const handleConfirm = () => {
        setPinModalVisible(false);
        if (isZOutPin) setIsModalVisible(true);
        else setInputEnabled(true);
        setIsZOutPin(false);
    };
    const handleCancel = () => {
        setIsZOutPin(false);
        setPinModalVisible(false);
    };
    const fetchCustomerOptions = async (searchText = '', page = 1) => {
        const response = await dispatch(
            CustomerList({
                page,
                pageSize: 10,
                search: searchText,
                isActive: true,
            })
        ).unwrap();

        return response?.data?.data?.list?.map((customer: any) => ({
            value: customer._id,
            label: `${customer.firstName} ${customer.lastName}`,
            userId: customer.userId,
            facilityId: customer.facilityId,
        }));
    };

    useEffect(() => {
        setShouldShowSelect(!clientId || !userId);
    }, [location, clientId]);

    useEffect(() => {
        if (store.featureTabList?.length > 0) {
            const firstTabId = store.featureTabList[0]._id;
            setActiveTab(firstTabId);
            startFeaturedLoader();
            dispatch(getFeatureById({ featureId: firstTabId })).unwrap();
        }
        endFeaturedLoader();
    }, [store.featureTabList]);

    const handleFeatureTabClick = (featureId: string) => {
        startFeaturedLoader();
        dispatch(getFeatureById({ featureId: featureId }))
            .unwrap()
            .then(() => {})
            .finally(endFeaturedLoader);
    };

    const handleInputChange = (
        key: keyof typeof calculation,
        value: number | string
    ) => {
        if (key === 'cartDiscount') {
            const actualPrice = calculation?.subTotal;
            const discountValue = Number(value) || 0; // Use new value instead of previous state
            let finalPrice = calculation?.subTotal;

            if (calculation.discountType === 'Percentage') {
                if (discountValue > 100) {
                    setDiscountErrorMessage('Percentage cannot exceed 100!');
                    return calculation; // Return early to prevent state override
                } else setDiscountErrorMessage('');
                finalPrice = actualPrice - (actualPrice * discountValue) / 100;
            } else if (calculation.discountType === 'Flat') {
                finalPrice = actualPrice - discountValue;
            }

            if (finalPrice < 0 || finalPrice > actualPrice) {
                setDiscountErrorMessage(
                    'Invalid discount! Final price cannot be negative or greater than the actual price.'
                );
            } else setDiscountErrorMessage('');
        }
        setCalculation((prev: any) => {
            const updatedCalculation = {
                ...prev,
                [key]: value,
            };
            validateCartDebounced({
                discountType: updatedCalculation.discountType,
                cartDiscount: updatedCalculation.cartDiscount,
            });
            return updatedCalculation;
        });
    };

    const handlePricingSearch = (value: string) => {
        setSearchPricing(value);
    };

    useEffect(() => {
        if (clientId) {
            startLoader();
            dispatch(ClientsDetails({ clientId: clientId }))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [clientId]);

    useEffect(() => {
        if (selectedClient) {
            const clientId = selectedClient?.value || selectedClient;
            dispatch(ClientsDetails({ clientId: clientId }));
        }
    }, [selectedClient]);

    useEffect(() => {
        startLoader();
        dispatch(featureListing({ isActive: true }))
            .unwrap()
            .then(() => {})
            .finally(endLoader);
    }, []);

    useEffect(() => {
        if (searchPricing) {
            debouncedRequest(() => {
                dispatch(
                    PricingListByActiveStatus({
                        page: 1,
                        pageSize: 30,
                        search: searchPricing,
                        fetchWithBundled: true,
                    })
                );
            });
        } else {
            startTableLoader();
            dispatch(
                PricingListByActiveStatus({
                    page: 1,
                    pageSize: 30,
                    fetchWithBundled: true,
                })
            )
                .unwrap()
                .then(() => {})
                .finally(endTableLoader);
        }
    }, [searchPricing]);

    const handleCheckboxChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        recordId: string,
        record: any
    ) => {
        const updatedIds = new Set(selectedIds);
        const updatedData = selectedData.filter(
            (item) => item._id !== record._id
        );

        if (e.target.checked) {
            updatedIds.add(recordId);
            updatedData.push({
                ...record,
                quantity: 1,
                discountedValue: 0,
                isBundledPricing: !!record?.isBundledPricing,
                isPackage: true,
            });
        } else {
            updatedIds.delete(recordId);
        }
        validateCartDebounced({ items: updatedData });
        setSelectedIds(Array.from(updatedIds));
        setSelectedData(updatedData);
    };

    const handleProductCheckboxChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        recordId: string,
        record: any
    ) => {
        const updatedIds = new Set(selectedIds);
        const updatedData = selectedData.filter(
            (item) => item._id !== record._id
        );

        if (e.target.checked) {
            updatedIds.add(recordId);
            updatedData.push({
                ...record,
                _id: record.productId,
                discountedValue: 0,
                quantity: 1,
                isProduct: true,
            });
        } else {
            updatedIds.delete(recordId);
        }
        validateCartDebounced({ items: updatedData });
        setSelectedIds(Array.from(updatedIds));
        setSelectedData(updatedData);
    };
    const handleCardClick = (item: any) => {
        setSelectedIds((prev) => {
            const updatedSet = new Set(prev);
            if (!updatedSet.has(item.packageId)) {
                updatedSet.add(item.packageId);
            }
            return Array.from(updatedSet);
        });

        let updatedSelectedData;
        const existingItem = selectedData?.find(
            (data) => data._id === item.packageId
        );
        if (existingItem) {
            updatedSelectedData = selectedData?.map((data) =>
                data._id === item.packageId
                    ? {
                          ...data,
                          isBundledPricing:
                              item.packageDetails?.isBundledPricing,
                          isPackage: true,
                      }
                    : data
            );
        } else {
            updatedSelectedData = [
                ...selectedData,
                {
                    _id: item.packageId,
                    name: item.packageDetails.name,
                    price: item.packageDetails.price || 0,
                    quantity: 1,
                    tax: item.packageDetails.tax || 0,
                    discountedValue: 0,
                    isBundledPricing: item.packageDetails?.isBundledPricing,
                    isPackage: true,
                },
            ];
        }
        validateCartDebounced({ items: updatedSelectedData });
        setSelectedData(updatedSelectedData);
    };

    const handleCustomCardClick = (
        CustomPackDetails: any,
        discountedValue: any,
        posPrice: any
    ) => {
        setCustomPackageDetails(CustomPackDetails);
        console.log(
            'Received data from modal:=========',
            CustomPackDetails,
            discountedValue
        );
        setSelectedIds((prev) => {
            const updatedSet = new Set(prev);
            // Ensure the item._id is being used as the unique identifier
            if (!updatedSet.has(CustomPackDetails._id)) {
                updatedSet.add(CustomPackDetails._id);
            }
            return Array.from(updatedSet);
        });
        let updatedSelectedData;
        const existingItem = selectedData?.find(
            (data) => data._id === CustomPackDetails._id
        );
        if (existingItem) {
            updatedSelectedData = selectedData?.map((data) =>
                data._id === CustomPackDetails._id
                    ? {
                          ...data,
                          isCustomPackage: true,
                      }
                    : data
            );
        } else {
            updatedSelectedData = [
                ...selectedData,
                {
                    ...CustomPackDetails,
                    quantity: 1, // Initial quantity set to 1
                    price:
                        CustomPackDetails.unitPrice *
                        CustomPackDetails.quantity,
                    discountedValue: 0,
                    isCustomPackage: true,
                },
            ];
        }
        validateCartDebounced({ items: updatedSelectedData });
        setSelectedData(updatedSelectedData);
    };

    const scrollbarRef = useRef<HTMLDivElement>(null);
    const [, setLocation] = useLocation();

    const scrollLeft = () => {
        if (scrollbarRef.current) {
            scrollbarRef.current.scrollLeft -= 150;
        }
    };

    // Function to scroll right
    const scrollRight = () => {
        if (scrollbarRef.current) {
            scrollbarRef.current.scrollLeft += 150;
        }
    };

    const handleWheel = (event: any) => {
        if (scrollbarRef.current) {
            // Prevent vertical scroll and scroll horizontally
            event.preventDefault();
            scrollbarRef.current.scrollLeft += event.deltaY;
        }
    };

    // Add event listener for mouse wheel scroll
    useEffect(() => {
        const scrollbar = scrollbarRef.current;

        if (scrollbar) {
            scrollbar.addEventListener('wheel', handleWheel, {
                passive: false,
            });
        }

        return () => {
            if (scrollbar) {
                scrollbar.removeEventListener('wheel', handleWheel);
            }
        };
    }, []);

    const columns: any = [
        {
            title: 'Product Name',
            dataIndex: 'name',
            key: 'name',
            // onCell: () => ({ style: { minWidth: 280, width: 280 } }),
            width: 250,
            render: (text: string, record: any) => (
                <ConfigProvider
                    theme={{
                        token: {
                            colorPrimary: 'primary',
                        },
                    }}
                >
                    <Checkbox
                        className=""
                        checked={selectedIds?.includes(record._id)}
                        onChange={(e: any) =>
                            handleCheckboxChange(e, record._id, record)
                        }
                    >
                        {text}
                    </Checkbox>
                </ConfigProvider>
            ),
        },
        {
            title: 'Price',
            dataIndex: 'price',
            key: 'price',
            width: 100,
            align: 'center',
        },
        {
            title: 'Tax %',
            dataIndex: 'tax',
            key: 'tax',
            width: 100,
            align: 'center',
        },
        {
            title: 'Sessions',
            width: 150,
            dataIndex: '',
            key: 'notes',
            align: 'center',
            render: (record: any) => {
                if (record?.services?.sessionType === 'single') {
                    return '1';
                } else if (record?.services?.sessionType === 'multiple') {
                    return record?.services?.sessionCount || '0';
                } else if (record?.services?.sessionType === 'unlimited') {
                    return 'Unlimited';
                } else if (record?.services?.sessionType === 'day_pass') {
                    return `${record?.services?.dayPassLimit} X Day Pass(es)`;
                } else {
                    return '-';
                }
            },
        },
        // {
        //     title: 'Account',
        //     dataIndex: 'account',
        //     key: 'account',
        //     width: 200,
        // },

        {
            title: 'Class Type',
            dataIndex: '',
            key: 'notes',
            // width: 250,
            ellipses: true,

            render: (record: any) => {
                const classTypes = record?.type
                    ? record?.type.split(',').map((type: string) => type.trim())
                    : record?.services?.type
                          ?.split(',')
                          .map((type: string) => type.trim());

                if (Array.isArray(classTypes)) {
                    const hasPersonalAppointment = classTypes?.includes(
                        'personalAppointment'
                    );

                    const remainingTypes = classTypes?.filter(
                        (type) => type !== 'personalAppointment'
                    );

                    return (
                        <>
                            {hasPersonalAppointment && <span>Appointment</span>}
                            {hasPersonalAppointment &&
                                remainingTypes.length > 0 &&
                                ', '}
                            {remainingTypes.length > 0 && (
                                <span>
                                    {remainingTypes
                                        .map(capitalizeFirstLetter)
                                        .join(', ')}
                                </span>
                            )}
                        </>
                    );
                } else {
                    return <span>{record?.services?.type}</span>;
                }
            },
        },
    ];

    const productColum: any = [
        {
            title: 'Product Name',
            dataIndex: 'name',
            key: 'name',
            // onCell: () => ({ style: { minWidth: 280, width: 280 } }),
            width: 250,
            render: (text: string, record: any) => (
                <ConfigProvider
                    theme={{
                        token: {
                            colorPrimary: 'primary',
                        },
                    }}
                >
                    <Checkbox
                        className=""
                        checked={selectedIds?.includes(record._id)}
                        onChange={(e: any) =>
                            handleProductCheckboxChange(e, record._id, record)
                        }
                        disabled={record.quantity === 0}
                    >
                        {text}
                    </Checkbox>
                </ConfigProvider>
            ),
        },
        {
            title: 'Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
            width: 100,
            align: 'center',
        },
        {
            title: 'Expiry Date',
            dataIndex: '',
            key: 'expiry',
            render: (record: any) => {
                return (
                    <div>
                        {record?.expiryDate &&
                            dayjs(record?.expiryDate).format('YYYY-MM-DD')}
                    </div>
                );
            },
        },
        {
            title: 'MRP',
            dataIndex: 'mrp',
            key: 'mrp',
            width: 100,
            align: 'center',
        },
        {
            title: 'Sale price',
            dataIndex: 'salePrice',
            key: 'price',
            width: 100,
            align: 'center',
        },
        {
            title: 'GST',
            dataIndex: 'tax',
            key: 'gst',
            width: 100,
            align: 'center',
        },
        {
            title: 'Discount price',
            dataIndex: 'discountPrice',
            key: 'discountPrice',
            width: 100,
            align: 'center',
        },
    ];
    const handleRemoveItem = (recordId: string) => {
        setSelectedIds((prev) => prev.filter((id) => id !== recordId));
        const updatedSelectedData = selectedData.filter(
            (item) => item._id !== recordId
        );
        validateCartDebounced({ items: updatedSelectedData });
        setSelectedData(updatedSelectedData);
    };

    const handleQuantityChange = (
        recordId: string,
        type: 'increment' | 'decrement'
    ) => {
        const updatedData = selectedData.map((item) => {
            if (item._id !== recordId) return item;

            const quantity =
                type === 'increment'
                    ? item.quantity + 1
                    : Math.max(1, item.quantity - 1);

            return {
                ...item,
                quantity,
            };
        });
        validateCartDebounced({ items: updatedData });
        setSelectedData(updatedData);
    };

    const handleDiscountClick = (record: any) => {
        if (!selectedClientUserId) {
            Alertify.error('Please select a client before proceeding.');
            return;
        }
        setDiscountedItem(record);
        setDiscountModalVisible(true);
    };

    const handleClientChange = (value: any, option: any) => {
        setSelectClient(value);
        setSelectClientUserId(option.userId);
        validateCartDebounced({
            userId: option.userId,
            facilityId: option.facilityId,
        });
    };

    const handleClose = () => {
        setDiscountModalVisible(false);
        setDiscountedItem({});
    };

    function discountChangesSubmit(data: any) {
        const updatedSelectedData = selectedData.map((item) =>
            item._id === discountedItem._id ? { ...item, ...data } : item
        );
        validateCartDebounced({ items: updatedSelectedData });
        setSelectedData(updatedSelectedData);
        setDiscountModalVisible(false);
    }

    const orderColumns: any = [
        {
            title: 'Item',
            dataIndex: 'name',
            key: 'name',
            // ellipsis: true,
            width: 250,
        },
        {
            title: 'Qty',
            dataIndex: 'quantity',
            align: 'center',
            key: 'quantity',
            width: 90,
            render: (_: any, record: any) => (
                <div className="flex w-[65px] items-center justify-between gap-1 ">
                    <Button
                        size="small"
                        onClick={() =>
                            handleQuantityChange(record._id, 'decrement')
                        }
                        disabled={record.quantity <= 1}
                    >
                        -
                    </Button>
                    <span>{record.quantity}</span>
                    <Button
                        size="small"
                        onClick={() =>
                            handleQuantityChange(record._id, 'increment')
                        }
                    >
                        +
                    </Button>
                </div>
            ),
        },
        {
            title: 'Price',
            key: 'subTotal',
            render: (_: any, record: any) => {
                const quantity = record.quantity || 1;
                return <span>{quantity * record.price}</span>;
            },
        },
        {
            title: 'Discount',
            key: 'discountedValue',
            align: 'center',
            render: (_: any, record: any) => {
                const totalDiscount = record.discountedValue || 0;
                return (
                    <span
                        className="cursor-pointer"
                        onClick={() =>
                            !record?.isBundledPricing &&
                            record.isPackage &&
                            handleDiscountClick(record)
                        }
                    >
                        {totalDiscount?.toFixed(2)}
                        {!record?.isBundledPricing && record.isPackage && (
                            <EditOutlined />
                        )}
                    </span>
                );
            },
        },
        {
            title: 'GST',
            dataIndex: 'tax',
            key: 'tax',
            align: 'center',
        },

        {
            title: '',
            key: 'action',
            width: 45,
            render: (_: any, record: any) => (
                <Button
                    type="link"
                    className="p-0 px-2 py-1 text-[#8143d1]"
                    onClick={() => handleRemoveItem(record._id)}
                >
                    <DeleteFilled />
                </Button>
            ),
        },
    ];

    const handleContinueToPay = () => {
        if (!clientId && !userId && !selectedClientUserId) {
            Alertify.error('Please select a client before proceeding.');
            return;
        }

        if (!selectedData || selectedData?.length === 0) {
            Alertify.error(
                'Please select at least one product before proceeding.'
            );
            return;
        }

        const payloadData = getUpdateStorePayload(selectedData);
        const payload = {
            ...payloadData,
            paymentDetails: [
                {
                    paymentMethod: 'credit_card',
                    amount: calculation.total,
                    paymentDate: new Date(),
                    paymentStatus: 'completed',
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                },
            ],
        };
        dispatch(SetPosPurchasedData(payload));
        setLocation('/payment');
    };

    const showModal = (zOut = false) => {
        const noClientSelected = !clientId && !userId && !selectedClientUserId;
        const noProductSelected = !selectedData || selectedData.length === 0;

        if (!zOut) {
            if (noClientSelected) {
                Alertify.error('Please select a client before proceeding.');
                return;
            }

            if (noProductSelected) {
                Alertify.error(
                    'Please select at least one product before proceeding.'
                );
                return;
            }
        }

        if (zOut) {
            if (role !== RoleType.ORGANIZATION) {
                dispatch(GetSettingActiveStatus({ settingKey: 'settings_pin' }))
                    .unwrap()
                    .then((response: any) => {
                        const settingData = response?.data?.data;
                        const isEnabled = settingData?.isEnabled;
                        const isActive = settingData?.isActive;

                        if (isEnabled && isActive) {
                            setRequirePinModal(true);
                            setIsZOutPin(true);
                            setPinModalVisible(true);
                        } else {
                            setIsModalVisible(true);
                        }
                    });
            } else {
                setIsModalVisible(true);
            }
            return;
        } else handleContinueToPay();
    };

    function adjustMarginBasedOnScale() {
        const scale = window.devicePixelRatio; // Detect current scale
        const targetDiv = document.getElementById('target-div'); // Get your target div

        if (scale === 1.5) {
            // For 150% scale
            targetDiv?.style && (targetDiv.style.marginTop = '3.5rem');
        } else {
            targetDiv?.style && (targetDiv.style.marginTop = '.8rem'); // Default margin
        }
    }

    // Run the function on page load
    useEffect(() => {
        adjustMarginBasedOnScale();
        window.addEventListener('resize', adjustMarginBasedOnScale);
        return () => {
            window.removeEventListener('resize', adjustMarginBasedOnScale);
        };
    }, []);

    // Optionally, update on window resize (for responsive behavior)

    const dynamicDate = () => {
        const dynamicDate = dayjs();
        return dynamicDate.format('dddd, DD MMMM, h:mm A');
    };

    const openAddClientModal = () => {
        setShowAddClientModal(true);
    };

    const closeAddClientModal = () => {
        setShowAddClientModal(false);
    };

    const handleClientAdded = (newClient: any) => {
        dispatch(
            CustomerList({
                page: page,
                pageSize: 30,
                isActive: true,
            })
        )
            .unwrap()
            .then((res: any) => {
                // console.log('Res-------------cvcvdfv--------', res);
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    const newlyAddedClient = res?.data?.data?.list?.[0];
                    if (newlyAddedClient) {
                        setSelectClient({
                            label: `${newlyAddedClient.firstName} ${newlyAddedClient.lastName}`,
                            value: newlyAddedClient._id,
                            userId: newlyAddedClient.userId,
                        });
                        setSelectClientUserId(newlyAddedClient.userId);
                    }
                    // setSelectClient(res?.data?.data?.list?.[0]?._id);
                    setSelectClientUserId(res?.data?.data?.list?.[0]?.userId);
                    validateCartDebounced({
                        userId: newlyAddedClient.userId,
                        facilityId: newlyAddedClient.facilityId,
                    });
                }
            });
    };

    useEffect(() => {
        if (!selectedFacilityId && store.facilityList?.length > 0) {
            const firstFacility =
                store.facilityList.length > 0
                    ? (store.facilityList[0] as
                          | { _id: string; facilityName: string }
                          | undefined)
                    : undefined;
            setSelectedFacilityId(firstFacility?._id || '');
            setSelectedFacilityName(firstFacility?.facilityName || '');
        }
    }, [store.facilityList]);

    const [customPackageCardId, setCustomPackageCardId] = useState<string>('');

    const handleCustomPackageCardId = (id: string) => {
        // Navigate to the next page with the package id in the URL
        setCustomPackageCardId(id);
        dispatch(CustomPricingDetails({ customPricingId: id }));
    };

    // for add pos permissions

    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasClientWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.POS &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.CLIENTS_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasZOutWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.POS_Z_OUT &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.TRANSACTION_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    return loader ? (
        <FullLoader state={true} />
    ) : (
        <>
            <div className="flex flex-row items-center justify-between  pb-10">
                <div className="flex items-center gap-10 ">
                    <div className="flex items-center gap-5">
                        <img
                            src="/icons/back.svg"
                            alt="edit"
                            className="h-[10px] cursor-pointer"
                            onClick={goBack}
                        />
                        <Title className=" text-[#1a3353]" level={4}>
                            Point of Sale
                        </Title>
                    </div>

                    <Select
                        style={{ width: '15vw' }}
                        options={store.facilityList.map((facility: any) => ({
                            label: facility?.facilityName,
                            value: facility?._id,
                        }))}
                        value={selectedFacilityId}
                        onChange={(value) => {
                            setSelectedFacilityId(value);
                            const selectedFacility = store.facilityList.find(
                                (facility: {
                                    _id: string;
                                    facilityName: string;
                                }) => facility._id === value
                            ) as
                                | { _id: string; facilityName: string }
                                | undefined;
                            setSelectedFacilityName(
                                selectedFacility?.facilityName || ''
                            );
                        }}
                    />
                </div>
                {(hasZOutWritePermission || role === RoleType.ORGANIZATION) && (
                    <Button
                        onClick={() => showModal(true)}
                        className="border-1 border-[#1A3353] px-8 py-7 text-xl text-[#1A3353]"
                    >
                        Z-out Reconciliation Front Desk
                    </Button>
                )}
            </div>

            <div className="  flex items-center justify-between  ">
                {/* <Tabs
                    activeKey={mainactiveTab}
                    onChange={setMainActiveTab}
                    tabPosition="top"
                    type="card"
                    style={{ marginBottom: 32 }}
                >
                    <Tabs.TabPane
                        tab={
                            <span
                                style={
                                    mainActiveTab === 'packages'
                                        ? {
                                              color: '#1890ff',
                                              fontWeight: 'bold',
                                          }
                                        : {}
                                }
                            >
                                Packages
                            </span>
                        }
                        key="packages"
                    />
                    <Tabs.TabPane
                        tab={
                            <span
                                style={
                                    mainActiveTab === 'product'
                                        ? {
                                              color: '#1890ff',
                                              fontWeight: 'bold',
                                          }
                                        : {}
                                }
                            >
                                Products
                            </span>
                        }
                        key="product"
                    />
                </Tabs> */}

                <div className="flex  flex-row items-center gap-4">
                    <div
                        onClick={() => setMainActiveTab('packages')}
                        className={` cursor-pointer rounded-md p-2 text-2xl font-medium text-[#1A3353] ${
                            mainActiveTab === 'packages'
                                ? 'relative z-10 translate-y-1 border-e border-s border-t border-black border-opacity-10 bg-white'
                                : 'relative z-10 translate-y-1 border-e border-s border-t border-transparent border-opacity-50 bg-transparent'
                        }`}
                    >
                        Packages
                    </div>
                    <div
                        onClick={() => setMainActiveTab('products')}
                        className={`cursor-pointer rounded-md p-2 text-2xl font-medium text-[#1A3353] ${
                            mainActiveTab === 'products'
                                ? 'relative z-10 translate-y-1 border-e border-s border-t border-black border-opacity-10 bg-white'
                                : 'relative z-10 translate-y-1 border-e border-s border-t border-transparent border-opacity-50 bg-transparent'
                        }`}
                    >
                        Products
                    </div>
                </div>

                <p className="text-[#1a3353]">{dynamicDate()}</p>
            </div>

            {/* Main Container */}
            <div className=" relative flex w-full gap-5  ">
                {/* Left Side: Packages and Table */}
                {mainActiveTab === 'packages' ? (
                    <>
                        <div className=" w-[55%] ">
                            {/* <div className="flex  flex-row items-center  gap-4 pb-4">
                                <div
                                    onClick={() => setSimplePackage('simple')}
                                    className={`cursor-pointer  text-lg text-[#1A3353] ${
                                        simplePackage === 'simple'
                                            ? 'border-b-2 border-primary'
                                            : 'border-b-2 border-transparent'
                                    }`}
                                >
                                    Simple Package
                                </div>
                                <div
                                    onClick={() => setSimplePackage('custom')}
                                    className={` cursor-pointer text-lg text-[#1A3353] ${
                                        simplePackage === 'custom'
                                            ? 'border-b-2 border-primary'
                                            : 'border-b-2 border-transparent'
                                    }`}
                                >
                                    Custom Package
                                </div>
                            </div> */}
                            <div className="  flex  flex-col justify-between">
                                <div className="sticky top-[0%] ">
                                    {/* Tabs */}
                                    <div className="mb-4 rounded-md border-b border-e border-s border-t  border-black border-opacity-10 pr-2 ps-2 pt-2 shadow-md @lg:h-[50vh]">
                                        {/* <div className="flex  flex-row items-center gap-4 pb-8 pt-2">
                                            <div
                                                onClick={() =>
                                                    setSimplePackage('simple')
                                                }
                                                className={`cursor-pointer  pb-1 text-xl font-medium text-[#1A3353] ${
                                                    simplePackage === 'simple'
                                                        ? 'border-b border-primary'
                                                        : 'border-b border-transparent'
                                                }`}
                                            >
                                                Simple Package
                                            </div>
                                            <div
                                                onClick={() =>
                                                    setSimplePackage('custom')
                                                }
                                                className={` cursor-pointer pb-1 text-xl font-medium text-[#1A3353] ${
                                                    simplePackage === 'custom'
                                                        ? 'border-b border-primary'
                                                        : 'border-b border-transparent'
                                                }`}
                                            >
                                                Custom Package
                                            </div>
                                        </div> */}
                                        <div className="relative">
                                            {/* Left Arrow Button */}
                                            <button
                                                className="absolute left-0 top-1/2 z-10 -translate-y-1/2 transform"
                                                onClick={scrollLeft}
                                            >
                                                <ArrowLeftOutlined />
                                            </button>

                                            <div
                                                ref={scrollbarRef}
                                                className="sidebar-scrollbar mx-auto mb-4 flex w-[95%] gap-4 overflow-x-scroll"
                                            >
                                                {store.featureTabList?.map(
                                                    (
                                                        tab: any,
                                                        index: number
                                                    ) => (
                                                        <div
                                                            key={index}
                                                            className={`cursor-pointer whitespace-nowrap rounded border px-4 py-2 text-center text-lg text-[#455560] ${
                                                                activeTab ===
                                                                tab._id
                                                                    ? 'bg-gray-200'
                                                                    : 'hover:bg-gray-100'
                                                            }`}
                                                            onClick={() => {
                                                                setActiveTab(
                                                                    tab._id
                                                                );
                                                                setSimplePackage(
                                                                    'simple'
                                                                ); // ensure "simple" tab selected
                                                                handleFeatureTabClick(
                                                                    tab._id
                                                                );
                                                            }}
                                                        >
                                                            {tab?.name}
                                                        </div>
                                                    )
                                                )}

                                                {/* 👉 Custom Package tab */}
                                                <div
                                                    className={`cursor-pointer whitespace-nowrap rounded border px-4 py-2 text-center text-lg text-[#455560] ${
                                                        activeTab ===
                                                        'custom-package'
                                                            ? 'bg-gray-200'
                                                            : 'hover:bg-gray-100'
                                                    }`}
                                                    onClick={() => {
                                                        setActiveTab(
                                                            'custom-package'
                                                        ); // special value
                                                        setSimplePackage(
                                                            'custom'
                                                        ); // ensure custom tab logic applies
                                                    }}
                                                >
                                                    Custom Package
                                                </div>
                                            </div>

                                            {/* Right Arrow Button */}
                                            <button
                                                className="absolute right-0 top-1/2 z-10 -translate-y-1/2 transform"
                                                onClick={scrollRight}
                                            >
                                                <ArrowRightOutlined />
                                            </button>
                                        </div>

                                        {simplePackage === 'simple' &&
                                            (featuredLoader ? (
                                                <div className="flex h-[26vh] w-full items-center justify-center">
                                                    <LoadingOutlined />
                                                </div>
                                            ) : (
                                                <div className="pos-package-card-div grid h-[26vh] grid-cols-4 content-start gap-4">
                                                    {store.featureTabDetails?.packageList
                                                        ?.slice(0, 12)
                                                        ?.map((item: any) => (
                                                            <div
                                                                key={
                                                                    item.packageId
                                                                }
                                                                style={{
                                                                    backgroundColor:
                                                                        item?.color,
                                                                }}
                                                                onClick={() =>
                                                                    handleCardClick(
                                                                        item
                                                                    )
                                                                }
                                                                className="flex h-20 cursor-pointer items-center justify-center break-words rounded p-4 text-center text-lg font-medium shadow hover:opacity-80"
                                                            >
                                                                {
                                                                    item
                                                                        ?.packageDetails
                                                                        ?.name
                                                                }
                                                            </div>
                                                        ))}
                                                </div>
                                            ))}

                                        {simplePackage === 'custom' && (
                                            <>
                                                <div className="pos-package-card-div mb-3 grid h-[26vh] grid-cols-4 content-start gap-4">
                                                    <div
                                                        onClick={() => {
                                                            showEditPackageModal();
                                                            setIsViewOnly(
                                                                false
                                                            );
                                                        }}
                                                        className="flex h-20 cursor-pointer items-center justify-center break-words rounded p-4 text-center text-lg font-medium shadow hover:opacity-80"
                                                    >
                                                        <PlusOutlined />
                                                    </div>
                                                    {store?.customPackagesList
                                                        ?.slice(0, 11)
                                                        ?.map((pkg: any) => (
                                                            <div
                                                                onClick={() => {
                                                                    handleCustomPackageCardId(
                                                                        pkg?._id
                                                                    );
                                                                    setIsViewOnly(
                                                                        true
                                                                    );
                                                                    showEditPackageModal();
                                                                }}
                                                                key={pkg?._id}
                                                                className="mb-2 flex h-20 cursor-pointer items-center justify-center rounded border bg-black/10 p-4 shadow hover:opacity-80"
                                                            >
                                                                <h3 className="text-center text-lg font-semibold text-[#1A3353] ">
                                                                    {pkg?.name}
                                                                </h3>
                                                            </div>
                                                        ))}
                                                </div>
                                            </>
                                        )}
                                    </div>

                                    {/* Table */}
                                    <div
                                        className="your-class flex flex-col gap-3  "
                                        id="target-div"
                                    >
                                        <div className="flex  flex-row justify-end   ">
                                            <Input.Search
                                                placeholder="Search "
                                                className="w-[35%]"
                                                value={searchPricing}
                                                allowClear
                                                onChange={(e) =>
                                                    handlePricingSearch(
                                                        e.target.value
                                                    )
                                                }
                                            />
                                        </div>
                                        <div className="h-[45vh] overflow-y-scroll ">
                                            {tableLoader ? (
                                                <div className="flex h-[45vh] w-full items-center justify-center">
                                                    <LoadingOutlined />
                                                </div>
                                            ) : (
                                                <ConfigProvider
                                                    theme={{
                                                        components: {
                                                            Table: {
                                                                cellPaddingBlock: 6,
                                                            },
                                                        },
                                                    }}
                                                >
                                                    <Table
                                                        dataSource={
                                                            store.pricingListByActiveStatus
                                                        }
                                                        columns={columns}
                                                        pagination={false}
                                                        className="rounded bg-white shadow"
                                                        // scroll={{ y: 200 }}
                                                        scroll={{ x: 600 }}
                                                    />
                                                </ConfigProvider>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </>
                ) : (
                    <>
                        <div className="  flex w-[55%] flex-col justify-between  ">
                            {/* Tabs */}
                            <div className="">
                                <div className="relative">
                                    <Table
                                        dataSource={store.inventoryList}
                                        columns={productColum}
                                        pagination={false}
                                        className="rounded bg-white shadow"
                                        // scroll={{ y: 200 }}
                                        scroll={{ x: 600 }}
                                    />
                                </div>
                            </div>
                        </div>
                    </>
                )}

                {/* Right Side: Order and Customer Details */}
                <div className="w-[45%]">
                    <div className="w-[100%] rounded-lg p-6 shadow">
                        <p className="mb-2 text-2xl font-bold text-[#1a3353]">
                            Orders
                        </p>
                        {/* <p className="text-xl text-[#455560]">
                            <span className="font-semibold text-[#1a3353]">
                                Order Id: &nbsp;
                            </span>
                            #34562
                        </p> */}
                        <p className="mt-1.5 text-xl text-[#455560]">
                            <span className="font-semibold text-[#1a3353]">
                                Date and Time: &nbsp;
                            </span>
                            {dynamicDate()}
                        </p>

                        <p className="mb-4 mt-4 text-2xl font-bold text-[#1a3353]">
                            Customer Details
                        </p>

                        {shouldShowSelect && (
                            <div className="flex w-full flex-row items-center justify-between gap-2">
                                <div className=" flex w-[80%] flex-row items-center gap-2">
                                    <p className=" w-[35%] text-xl font-bold text-[#1a3353]">
                                        Customer Name: &nbsp;
                                    </p>
                                    <InfiniteScrollSelect
                                        fetchOptions={fetchCustomerOptions}
                                        value={selectedClient}
                                        onChange={handleClientChange}
                                        placeholder="Select a customer"
                                        disabled={false}
                                        className="w-full"
                                    />
                                </div>
                                {(role === RoleType.ORGANIZATION ||
                                    hasClientWritePermission) && (
                                    <div className="w-[18%]">
                                        <Button
                                            onClick={openAddClientModal}
                                            className="h-14 bg-purpleLight text-xl text-white"
                                        >
                                            Add Client
                                        </Button>
                                    </div>
                                )}
                            </div>
                        )}
                        {!shouldShowSelect && (
                            <p className="mt-2.5 text-xl text-[#455560]">
                                <span className="font-semibold text-[#1a3353]">
                                    Name: &nbsp;
                                </span>
                                {store.customerDetails?.firstName}{' '}
                                {store.customerDetails?.lastName}
                            </p>
                        )}
                        <p className="mt-2.5 text-xl text-[#455560]">
                            <span className="font-semibold text-[#1a3353]">
                                Phone Number: &nbsp;
                            </span>
                            {!shouldShowSelect || selectedClient
                                ? store.customerDetails?.mobile
                                : ''}
                        </p>
                        <p className="mt-6 text-xl text-[#455560]">
                            <span className="font-semibold text-[#1a3353]">
                                Email: &nbsp;
                            </span>
                            {!shouldShowSelect || selectedClient
                                ? store.customerDetails?.email
                                : ''}
                        </p>

                        {/* Table */}
                        {tableLoader ? (
                            <FullLoader state={true} />
                        ) : (
                            <ConfigProvider
                                theme={{
                                    components: {
                                        Table: {
                                            cellPaddingBlock: 8,
                                        },
                                    },
                                }}
                            >
                                <Table
                                    className="pt-8"
                                    dataSource={selectedData}
                                    columns={orderColumns}
                                    pagination={false}
                                    scroll={{ x: 500 }}
                                />
                            </ConfigProvider>
                        )}

                        {/* Promo Input */}
                        <div className="flex flex-col gap-6 py-5 text-[#1A3353]">
                            <div className="flex items-center justify-between gap-2">
                                <div className="w-[20%]">
                                    <h2 className=" text-xl font-semibold">
                                        Sub Total
                                    </h2>
                                </div>
                                <div className="w-[70%]">
                                    <Input
                                        placeholder="Sub Total"
                                        className=""
                                        value={calculation?.subTotal}
                                        disabled
                                    />
                                </div>
                            </div>
                            <div className="flex items-center justify-between gap-2 ">
                                <div className="w-[20%]">
                                    <h2 className=" text-xl font-semibold">
                                        Discount
                                    </h2>
                                </div>
                                <div className="w-[70%]">
                                    <Input.Group className="flex" compact>
                                        <Select
                                            className="w-[20%]"
                                            defaultValue="Percentage"
                                            value={calculation.discountType}
                                            onChange={(value) =>
                                                handleInputChange(
                                                    'discountType',
                                                    value || ''
                                                )
                                            }
                                            style={{ width: '30%' }}
                                        >
                                            <Option value="Flat">Flat</Option>
                                            <Option value="Percentage">
                                                %
                                            </Option>
                                        </Select>
                                        <Input
                                            className="w-[80%]"
                                            placeholder="Enter value"
                                            value={calculation.cartDiscount}
                                            readOnly={
                                                !inputEnabled &&
                                                role !== RoleType.ORGANIZATION
                                            }
                                            type="number"
                                            min={0}
                                            step="any"
                                            // onMouseDown={(e) => {
                                            //     if (!inputEnabled &&
                                            //         role !==
                                            //             RoleType.ORGANIZATION
                                            //     ) {
                                            //         e.preventDefault();
                                            //         setPinModalVisible(true);
                                            //     }
                                            // }}
                                            onChange={(e) =>
                                                handleInputChange(
                                                    'cartDiscount',
                                                    parseFloat(
                                                        e.target.value
                                                    ) || 0
                                                )
                                            }
                                            // suffix={type === 'Percentage' ? '%' : ''}
                                        />
                                    </Input.Group>
                                </div>
                            </div>
                            {discountErrorMessage && (
                                <div className="flex items-center justify-between gap-2">
                                    <span className="text-red-500">
                                        {discountErrorMessage}
                                    </span>
                                </div>
                            )}
                            <div className="flex items-center justify-between gap-2">
                                <div className="w-[20%]">
                                    <h2 className=" text-xl font-semibold">
                                        GST
                                    </h2>
                                </div>
                                <div className="w-[70%]">
                                    <Input
                                        placeholder="GST"
                                        className=""
                                        disabled
                                        value={calculation?.gst?.toFixed(2)}
                                    />
                                </div>
                            </div>

                            <div className="flex justify-between font-bold">
                                <span>Total</span>
                                <span>{calculation?.total?.toFixed(2)}</span>
                            </div>
                        </div>

                        {/* Buttons */}
                        <div className="mt-14 flex justify-end gap-4">
                            {/* <Button className=" w-56 border-1 border-[#1A3353] ">
                                Back
                            </Button> */}
                            <Button
                                onClick={() => showModal(false)}
                                type="primary"
                                className="w-56 bg-purpleLight text-white"
                                disabled={discountErrorMessage}
                            >
                                Continue to Pay
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {showAddClientModal && (
                <AddClientModal
                    open={showAddClientModal}
                    onClose={closeAddClientModal}
                    isAdded={true}
                    onClientAdded={handleClientAdded}
                />
            )}

            {isModalVisible && (
                <ReconciliationModal
                    visible={isModalVisible}
                    onClose={closeModal}
                />
            )}

            {editPackageModal && (
                <EditPackageModal
                    handleClose={hideEditPackageModal}
                    visible={editPackageModal}
                    selectedFacilityId={selectedFacilityId}
                    customPackageCardId={customPackageCardId}
                    isViewOnly={isViewOnly}
                    handleCustomCardClick={handleCustomCardClick}
                />
            )}

            {pinModalVisible && requirePinModal && (
                <ModulePinConfirmationModal
                    visible={pinModalVisible}
                    onConfirm={isZOutPin ? handleConfirm : handleContinueToPay}
                    onCancel={handleCancel}
                    module={
                        isZOutPin ? SUBJECT_TYPE.POS_Z_OUT : SUBJECT_TYPE.POS
                    }
                    subModule={
                        isZOutPin
                            ? PERMISSIONS_ENUM.TRANSACTION_WRITE
                            : PERMISSIONS_ENUM.PURCHASE_WRITE
                    }
                />
            )}
            {discountModalVisible && (
                <ChangeDiscountModal
                    visible={discountModalVisible}
                    onConfirm={discountChangesSubmit}
                    onCancel={handleClose}
                    discountedItem={discountedItem}
                    facilityId={selectedFacilityId}
                    userId={selectedClientUserId}
                    authUser={loginUserId}
                />
            )}
        </>
    );
};

export default PointOfSales;

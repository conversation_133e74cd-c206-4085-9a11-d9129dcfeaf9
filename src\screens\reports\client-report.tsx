import { Button, Config<PERSON>rovider, DatePicker, Form, Select } from 'antd';
import { log } from 'console';

const ClientReport = ({
    heading,
    SchedulingInputs,
    PricingInputs,
    ShowServiceType,
}) => {
    return (
        <>
            <p className=" text-2xl font-semibold text-[#1a3353]">{heading}</p>
            <div className="py-10">
                <ConfigProvider
                    theme={{
                        token: { controlHeight: 40 },
                    }}
                >
                    <Form layout="vertical" className="">
                        <div className="flex w-[100%] flex-row items-center justify-between">
                            <div className="w-[48%]">
                                <Form.Item label="Start date">
                                    <DatePicker className="w-[100%]" />
                                </Form.Item>
                            </div>
                            <div className="w-[48%]">
                                <Form.Item label="End date">
                                    <DatePicker className="w-[100%]" />
                                </Form.Item>
                            </div>
                        </div>
                        {/* <div>
                              <p className="py-10 text-2xl font-semibold text-[#1a3353]">
                                  Filter Section
                              </p>
                          </div> */}
                        <div className="flex w-[100%] flex-row items-center justify-between ">
                            <div className="w-[48%]">
                                <Form.Item label="Facility">
                                    <Select className="w-[100%]" options={[]} />
                                </Form.Item>
                            </div>
                            {ShowServiceType && (
                                <div className="w-[48%]">
                                    <Form.Item label="Service Type">
                                        <Select
                                            className="w-[100%]"
                                            options={[]}
                                        />
                                    </Form.Item>
                                </div>
                            )}
                        </div>
                        {SchedulingInputs && (
                            <div className="flex w-[100%] flex-row items-center justify-between">
                                <div className="w-[48%]">
                                    <Form.Item label="By Staff (optional)">
                                        <Select
                                            className="w-[100%]"
                                            options={[]}
                                        />
                                    </Form.Item>
                                </div>
                                <div className="w-[48%]">
                                    <Form.Item label="Type of Service">
                                        <Select
                                            className="w-[100%]"
                                            options={[]}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        )}
                        {PricingInputs && (
                            <div className="flex w-[100%] flex-row items-center justify-between">
                                <div className="w-[48%]">
                                    <Form.Item label="By Staff (optional)">
                                        <Select
                                            className="w-[100%]"
                                            options={[]}
                                        />
                                    </Form.Item>
                                </div>
                                <div className="w-[48%]">
                                    <Form.Item label="Type of Service">
                                        <Select
                                            className="w-[100%]"
                                            options={[]}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        )}
                        <div className="flex flex-row justify-end">
                            <Button
                                className="mt-10 bg-purpleLight px-14 py-7 text-xl text-white  "
                                htmlType="submit"
                            >
                                Export as CSV
                            </Button>
                        </div>
                    </Form>
                </ConfigProvider>
            </div>
        </>
    );
};

export default ClientReport;

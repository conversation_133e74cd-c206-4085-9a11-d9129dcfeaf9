import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    Button,
    Checkbox,
    Col,
    ConfigProvider,
    Form,
    FormProps,
    Input,
    Radio,
    Row,
    Select,
    Typography,
    Upload,
} from 'antd';
import { useEffect, useState } from 'react';
import { useLocation, useParams } from 'wouter';
import { goBack } from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { AttributeTypeListForDropdown } from '~/redux/actions/attribute-action';
import { UploadImage } from '~/redux/actions/common-action';
import {
    AppointmentTypeDetail,
    CreateAppointmentType,
    UpdateAppointmentType,
} from '~/redux/actions/serviceCategoryAction';
const { Title, Paragraph } = Typography;

const AddAppointmentType = () => {
    const [form] = Form.useForm();
    const [imageUrl, setImageUrl] = useState<string>();

    const [loading, setLoading] = useState(false);
    const [appointmentTypes, setAppointmentTypes] = useState<any[]>([]);
    const dispatch = useAppDispatch();

    const [_, setLocation] = useLocation();

    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const { id, subItemId } = useParams<{ id: string; subItemId: string }>();

    const store = useAppSelector((state) => ({
        AppointmentTypeDetailData:
            state.service_category_store.AppointmentTypeDetailData,
        attributeTypeDropdownList:
            state.attribute_store.attributeTypeDropdownList,
    }));
    const appointmentId = String(subItemId);

    const handleAttributeTypeChange = async () => {
        await dispatch(
            AttributeTypeListForDropdown({ attributeType: 'tier' })
        ).then((res: any) => {
            setAppointmentTypes(
                res.payload?.data?.data?.map((data: any) => ({
                    label: data.name,
                    value: data._id,
                }))
            );
        });
    };

    const handleCreateOption = (inputValue: string) => {
        if (!appointmentTypes.find((option) => option.value === inputValue)) {
            const newOption = { value: inputValue, label: inputValue };
            setAppointmentTypes([
                newOption,
                ...store.attributeTypeDropdownList,
            ]);
            form.setFieldValue('appointmentId', inputValue);
        }
    };

    useEffect(() => {
        handleAttributeTypeChange();
    }, []);

    useEffect(() => {
        if (appointmentId !== '0') {
            startLoader();
            dispatch(AppointmentTypeDetail({ serviceId: id, appointmentId }))
                .unwrap()
                .finally(endLoader);
        }
    }, [id, appointmentId]);

    useEffect(() => {
        const appointmentData =
            store.AppointmentTypeDetailData?.appointmentType?.[0];
        console.log('Appointment Data:', appointmentData);
        if (appointmentId !== '0' && appointmentData) {
            form.setFieldsValue({
                name: appointmentData.name,
                duration: appointmentData.durationInMinutes,
                onlineScheduling: appointmentData.onlineBookingAllowed,
                featureOnline: appointmentData.isFeatured,
            });
            setImageUrl(appointmentData.image);
            console.log('Appointment Data image:', appointmentData.image);
        }
    }, [appointmentId, store.AppointmentTypeDetailData, form]);

    const handleImageUpload = (file: any) => {
        console.log('Uploaded file:', file);
        setLoading(true);
        dispatch(UploadImage({ file: file.file }))
            .then((res: any) => {
                console.log('Res------------------', res);
                setImageUrl(res?.payload?.res?.data?.data);
            })
            .finally(() => setLoading(false));
    };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );

    const onFinish: FormProps['onFinish'] = async (values) => {
        const appointmentData: any = store.attributeTypeDropdownList.find(
            (type: any) => type.value === values.appointmentId
        );
        startSubmitLoader();
        const payload = {
            serviceId: id,
            durationInMinutes: Number(values.duration),
            isFeatured: values?.featureOnline,
            image: imageUrl,
            onlineBookingAllowed: values.onlineScheduling,
            ...(appointmentId === '0'
                ? appointmentData
                    ? {
                          attributeId: values.appointmentId,
                          name: appointmentData?.label,
                          new: false,
                      }
                    : { name: values.appointmentId, new: true }
                : { name: values.name }),
        };
        console.log('Received payload of form:', payload);

        try {
            if (appointmentId !== '0') {
                await dispatch(
                    UpdateAppointmentType({
                        ...payload,
                        appointmentTypeId: appointmentId,
                    })
                )
                    .then((res: any) => {
                        if (
                            res?.payload?.status === 200 ||
                            res?.payload?.status === 201
                        ) {
                            form.resetFields();
                            goBack();
                        }
                    })
                    .finally(endSubmitLoader);
            } else {
                await dispatch(CreateAppointmentType(payload))
                    .then((res: any) => {
                        if (
                            res?.payload?.status === 200 ||
                            res?.payload?.status === 201
                        ) {
                            form.resetFields();
                            goBack();
                        }
                    })
                    .finally(endSubmitLoader);
            }
            // setLocation('/appointments-types');
        } catch (error) {
            console.error('Error saving appointment type:', error);
        }
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                },
                token: { borderRadius: 4 },
            }}
        >
            {loader ? (
                <FullLoader state />
            ) : (
                <>
                    <div className="flex items-center gap-4 lg:mb-16 @sm:mb-10">
                        <img
                            src="/icons/back.svg"
                            alt="edit"
                            className="h-[10px] cursor-pointer"
                            onClick={goBack}
                        />
                        <Title className="text-[#1A3353]" level={4}>
                            {appointmentId === '0'
                                ? 'Add Service'
                                : 'Edit Service'}
                        </Title>
                    </div>
                    <div className="flex items-start justify-between gap-6  @sm:gap-10">
                        <div className="mr-auto w-[65%]  @sm:w-full">
                            <Form
                                name="gymCreate"
                                layout="vertical"
                                size="large"
                                form={form}
                                onFinish={onFinish}
                                autoComplete="off"
                                initialValues={{
                                    onlineScheduling: true,
                                }}
                            >
                                <div className="rounded-lg border p-5 lg:p-10">
                                    {appointmentId === '0' ? (
                                        <Form.Item
                                            label="Name"
                                            name="appointmentId"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter or select appointment name!',
                                                },
                                            ]}
                                        >
                                            <Select
                                                showSearch
                                                style={{ width: '100%' }}
                                                filterOption={(input, option) =>
                                                    (option?.label ?? '')
                                                        .toLowerCase()
                                                        .includes(
                                                            input.toLowerCase()
                                                        )
                                                }
                                                placeholder="Enter or select Appointment name"
                                                options={appointmentTypes}
                                                onSearch={(value) => {
                                                    if (value) {
                                                        handleCreateOption(
                                                            value
                                                        );
                                                    }
                                                }}
                                                onSelect={(value) => {
                                                    form.setFieldValue(
                                                        'appointmentId',
                                                        value
                                                    );
                                                }}
                                            />
                                        </Form.Item>
                                    ) : (
                                        <Form.Item
                                            label="Name"
                                            name="name"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter a name',
                                                },
                                            ]}
                                        >
                                            <Input placeholder="Appointment Name" />
                                        </Form.Item>
                                    )}
                                    <Form.Item
                                        label="Duration in minutes"
                                        name="duration"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select duration',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            // defaultValue="15"
                                            // disabled
                                            placeholder="Select Duration"
                                            options={Array.from(
                                                { length: 80 },
                                                (_, i) => {
                                                    const multiple =
                                                        5 * (i + 1);
                                                    return {
                                                        label: `${multiple} Minutes`,
                                                        value: `${multiple}`,
                                                    };
                                                }
                                            )}
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        name="featureOnline"
                                        valuePropName="checked"
                                    >
                                        <Checkbox className="text-[#455560]">
                                            Feature Online
                                        </Checkbox>
                                    </Form.Item>
                                    <div className="checkbox-custom">
                                        <Paragraph className="ant-form-item-label mb-0">
                                            <label>Online Scheduling</label>
                                        </Paragraph>
                                    </div>
                                    <Paragraph className="mb-0 text-[#455560] lg:ps-2">
                                        Would you like to allow your clients to
                                        book this appointment online?
                                    </Paragraph>
                                    <Form.Item
                                        className="lg:ps-2"
                                        name="onlineScheduling"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select an option',
                                            },
                                        ]}
                                    >
                                        <Radio.Group>
                                            <Radio
                                                className="text-[#455560]"
                                                value={true}
                                            >
                                                Yes
                                            </Radio>
                                            <Radio
                                                className="text-[#455560]"
                                                value={false}
                                            >
                                                No
                                            </Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                </div>
                                <Form.Item>
                                    <div className="mt-8 flex justify-end gap-6 lg:pe-10 @sm:hidden">
                                        {' '}
                                        <Button
                                            onClick={goBack}
                                            htmlType="button"
                                            className="w-[110px] border-[#1A3353] bg-[#fff] text-[#1A3353]"
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            loading={submitLoader}
                                            htmlType="submit"
                                            className="w-[110px] bg-purpleLight text-white"
                                        >
                                            Save
                                        </Button>
                                    </div>
                                </Form.Item>
                            </Form>
                        </div>
                        <div className="w-[35%] rounded-lg border   lg:p-10  @sm:p-5 ">
                            <div className="">
                                <Typography.Title level={5}>
                                    <span className="text-primary ">
                                        UPLOAD IMAGE
                                    </span>
                                </Typography.Title>
                                {/* <Upload
                                    id="banner-upload"
                                    name="avatar"
                                    listType="picture-card"
                                    className="avatar-uploader overflow-hidden lg:mt-8 @sm:mt-2"
                                    showUploadList={false}
                                >
                                    {imageUrl ? (
                                        <img
                                            src={imageUrl}
                                            className="lg:object-contain @sm:rounded-3xl @sm:object-cover"
                                            alt="avatar"
                                            style={{
                                                width: '100%',
                                                height: '100%',
                                            }}
                                        />
                                    ) : (
                                        uploadButton
                                    )}
                                </Upload> */}

                                <Upload
                                    id="banner-upload"
                                    name="avatar"
                                    listType="picture-card"
                                    className="avatar-uploader overflow-hidden lg:mt-8 @sm:mt-2"
                                    showUploadList={false}
                                    // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                                    // beforeUpload={beforeUpload}
                                    // onChange={handleChange}
                                    customRequest={handleImageUpload}
                                >
                                    {imageUrl ? (
                                        <div className="relative h-full w-full">
                                            <img
                                                src={imageUrl}
                                                className="object-contain"
                                                alt="avatar"
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                }}
                                            />
                                            {loading && (
                                                <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/50">
                                                    <LoadingOutlined
                                                        style={{
                                                            fontSize: 24,
                                                            color: '#8143D1',
                                                        }}
                                                        spin
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        uploadButton
                                    )}
                                </Upload>
                            </div>
                            <Form.Item className="mt-8 flex justify-center  lg:hidden ">
                                <Button
                                    onClick={() => goBack()}
                                    htmlType="button"
                                    className="me-6 w-[110px]  border-[#1A3353] bg-[#fff]  text-xl  text-[#1A3353] "
                                >
                                    Cancel
                                </Button>
                                <Button
                                    // loading={loader}
                                    htmlType="submit"
                                    className=" w-[110px] bg-purpleLight text-xl  text-white"
                                >
                                    Save
                                </Button>
                            </Form.Item>
                        </div>
                    </div>
                </>
            )}
        </ConfigProvider>
    );
};

export default AddAppointmentType;

import React from 'react';
import { Modal, Button, Form, Select } from 'antd';

interface AssignPricingModalProps {
    isVisible: boolean;
    itemId: string | null;
    onClose: () => void;
}

const AssignPricingModal: React.FC<AssignPricingModalProps> = ({
    isVisible,
    itemId,
    onClose,
}) => {
    return (
        <Modal
            title="Assign Pricing"
            open={isVisible}
            onCancel={onClose}
            footer={[
                <Button
                    className="hover:text-[#8143d1]"
                    key="cancel"
                    onClick={onClose}
                >
                    Cancel
                </Button>,
                <Button
                    className="hover:text-black"
                    key="submit"
                    type="primary"
                    onClick={onClose}
                >
                    Assign
                </Button>,
            ]}
        >
            <p>{itemId}</p>
            <Form>
                <Form.Item
                    label="State"
                    name="state"
                    rules={[
                        {
                            required: true,
                            message: 'Please select state!',
                        },
                    ]}
                >
                    <Select
                        showSearch
                        placeholder="Select state"
                        filterOption={(input, option) =>
                            String(option?.label ?? '')
                                ?.toLowerCase()
                                .includes(input.toLowerCase())
                        }
                        // options={CountryOptions}
                        // onChange={handleStateChange}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default AssignPricingModal;

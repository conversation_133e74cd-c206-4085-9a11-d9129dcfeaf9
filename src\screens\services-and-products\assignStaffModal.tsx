import React, { useEffect } from 'react';
import { Modal, Button, Form, Select, FormProps } from 'antd';
import { Link } from 'wouter';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { capitalizeFirstLetter } from '~/components/common/function';
import { RoleType } from '~/types/enums';
import { TrainerListingForPayRate } from '~/redux/actions/appointment-action';
import {
    createPayRate,
    getAllPayRates,
} from '~/redux/actions/pay-rate-actions';

interface AssignStaffModalProps {
    isVisible: boolean;
    subTypeId: string | null;
    serviceId: string | null;
    onClose: () => void;
    serviceType?: string;
}

const AssignStaffModal: React.FC<AssignStaffModalProps> = ({
    isVisible,
    subTypeId,
    serviceId,
    onClose,
    serviceType,
}) => {
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        trainerListPayRate: state.appointment_store.trainerListPayRate,
    }));

    const TrainerOptions = store.trainerListPayRate?.map((item: any) => ({
        value: item.userId,
        label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
        id: item._id,
    }));

    useEffect(() => {
        if (isVisible) {
            dispatch(
                TrainerListingForPayRate({
                    serviceCategory: serviceId,
                    serviceType,
                    appointmentType: subTypeId,
                })
            );
        }
    }, [isVisible]);

    const onFinish: FormProps['onFinish'] = (values: any) => {
        console.log('Form Values:', values);

        const payload = {
            serviceCategory: serviceId,
            appointmentType: subTypeId,
            serviceType: serviceType,
            payRate: 'No-pay',
            value: values.value,
            userId: values.staff,
        };
        // console.log('payload--------------', payload);

        const afterSubmit = (res: any) => {
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                dispatch(
                    getAllPayRates({
                        page: 1,
                        pageSize: 10,
                        // staffId: values.staff,
                        serviceType: serviceType,
                        serviceCategory: serviceId,
                        appointmentType: subTypeId,
                    })
                );
                onClose();
            }
        };

        dispatch(createPayRate(payload)).then((res: any) => {
            afterSubmit(res);
        });
    };

    return (
        <Modal
            title="Assign Staff"
            open={isVisible}
            onCancel={onClose}
            footer={false}
        >
            {/* <div className="pb-5 pt-3">
                <p className="text-[#455560]">
                    <span className="font-semibold text-[#1A3353]">
                        Service Id: &nbsp;
                    </span>
                    {serviceId}
                </p>
                <p className="text-[#455560]">
                    <span className="font-semibold text-[#1A3353]">
                        Tier Id: &nbsp;
                    </span>
                    {subTypeId}
                </p>
            </div> */}
            <Form
                name="payRateForm"
                layout="vertical"
                size="large"
                form={form}
                autoComplete="off"
                initialValues={{ remember: true }}
                onFinish={onFinish}
            >
                <Form.Item
                    label="Staff List"
                    name="staff"
                    rules={[
                        {
                            required: true,
                            message: 'Please select staff',
                        },
                    ]}
                >
                    <Select
                        showSearch
                        placeholder="Select Staff"
                        filterOption={(input, option) =>
                            String(option?.label ?? '')
                                ?.toLowerCase()
                                .includes(input.toLowerCase())
                        }
                        options={TrainerOptions}
                        // onChange={handleStateChange}
                    />
                </Form.Item>
                <div className="mt-8 flex justify-end gap-4">
                    <Form.Item>
                        <Button
                            className="border-[#1A3353] text-[#1A3353]"
                            key="cancel"
                            onClick={onClose}
                        >
                            Cancel
                        </Button>
                    </Form.Item>
                    <Form.Item>
                        <Button
                            className="bg-purpleLight text-white"
                            type="primary"
                            htmlType="submit"
                        >
                            Assign
                        </Button>
                    </Form.Item>
                </div>
            </Form>
        </Modal>
    );
};

export default AssignStaffModal;

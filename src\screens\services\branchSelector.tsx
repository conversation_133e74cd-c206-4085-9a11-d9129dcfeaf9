import { useEffect, useState } from 'react';
import { Form, Select, Tag, Typography } from 'antd';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { FacilitiesListWithoutPage } from '~/redux/actions/facility-action';
const { Option } = Select;

interface Facility {
    _id: string;
    facilityName: string;
}

interface BranchSelectorProps {
    selected: any;
    form: any;
    setSelectedBranches?: (branches: string[]) => void;
    preSelectBranches: Facility[];
    setSelected: (selected: string[]) => void;
}

const { Text } = Typography;
const BranchSelector = (props: BranchSelectorProps) => {
    const [facilities, setFacilities] = useState<Facility[]>([]);
    const dispatch = useAppDispatch();

    const allOptions = [
        { label: 'All Branches', value: 'allBranches' },
        ...facilities.map((facility: Facility) => ({
            label: facility.facilityName,
            value: facility._id,
        })),
    ];

    // Fetch facilities from API and preselect first option if no branches are preselected
    useEffect(() => {
        dispatch(FacilitiesListWithoutPage())
            .then((res: any) => {
                if (res?.payload?.data?.data?.list) {
                    const facilityList = res.payload.data.data.list;
                    setFacilities(facilityList);

                    // If no branches are preselected and we have facilities, select the first one
                    if (
                        (!props.preSelectBranches ||
                            props.preSelectBranches.length === 0) &&
                        facilityList.length > 0 &&
                        (!props.selected || props.selected.length === 0)
                    ) {
                        const firstFacilityId = facilityList[0]._id;
                        props.setSelected([firstFacilityId]);
                        props.form.setFieldsValue({
                            facility: [firstFacilityId],
                        });

                        if (props.setSelectedBranches) {
                            props.setSelectedBranches([firstFacilityId]);
                        }
                    }
                }
            })
            .catch((error: any) => {
                console.error('Error fetching facilities:', error);
            });
    }, [dispatch, props.selected, props.preSelectBranches]); // Add dependencies to re-run when selected or preSelectBranches change

    // Handle preselected branches separately
    useEffect(() => {
        if (props.preSelectBranches && props.preSelectBranches.length > 0) {
            const preSelectedIds = props.preSelectBranches.map(
                (branch) => branch._id
            );
            props.setSelected(preSelectedIds);
            props.form.setFieldsValue({
                facility: preSelectedIds,
            });
            if (props.setSelectedBranches) {
                props.setSelectedBranches(preSelectedIds);
            }
        }
    }, [props.preSelectBranches]); // Only depend on preSelectBranches

    // Custom tag render function to hide 'allBranches' from display
    const tagRender = (props: CustomTagProps) => {
        const { value, closable, onClose } = props;
        if (value === 'allBranches') {
            return <></>;
        }
        return (
            <Tag
                closable={closable}
                onClose={onClose}
                style={{ marginRight: 3 }}
                className="ant-select-selection-item ant-select-multiple ant-select-lg"
            >
                <span className="ant-select-selection-item-content">
                    {' '}
                    {allOptions.find((opt) => opt.value === value)?.label}
                </span>
            </Tag>
        );
    };

    const handleChange = (values: string[]) => {
        const individualOptions = allOptions
            .filter((opt) => opt.value !== 'allBranches')
            .map((opt) => opt.value);
        const wasAllBranchesSelected = props.selected.includes('allBranches');
        const isAllBranchesSelected = values.includes('allBranches');
        // Case 1: selected 'allBranches'
        if (isAllBranchesSelected && !wasAllBranchesSelected) {
            // Select all options when "All Branches" is selected
            const allValues = allOptions.map((opt) => opt.value);
            props.setSelected(allValues);
            props.form.setFieldsValue({
                facility: allValues,
            });
            // Update parent component's state if setSelectedBranches is provided
            if (props.setSelectedBranches) {
                // Filter out 'allBranches' before sending to parent
                const filteredValues = allValues.filter(
                    (value) => value !== 'allBranches'
                );
                props.setSelectedBranches(filteredValues);
            }
            return;
        }

        // Case 2: unselected 'allBranches' specifically
        if (wasAllBranchesSelected && !isAllBranchesSelected) {
            // If 'allBranches' was unselected, clear all selections
            props.setSelected([]);
            props.form.setFieldsValue({
                facility: [],
            });
            // Update parent component's state if setSelectedBranches is provided
            if (props.setSelectedBranches) {
                // Empty array is already correct when unselecting all
                props.setSelectedBranches([]);
            }
            return;
        }

        // Case 3: 'allBranches' was props.selected, but unselect another option
        if (wasAllBranchesSelected && values.length < props.selected.length) {
            // Find which options were removed
            const removedOptions = props.selected.filter(
                (item: any) => !values.includes(item)
            );

            // Remove 'allBranches' and keep only the branches that weren't unselected
            const newSelected = props.selected.filter(
                (item: any) =>
                    item !== 'allBranches' && !removedOptions.includes(item)
            );

            props.setSelected(newSelected);
            props.form.setFieldsValue({
                facility: newSelected,
            });
            // Update parent component's state if setSelectedBranches is provided
            if (props.setSelectedBranches) {
                // Filter out 'allBranches' before sending to parent
                const filteredValues = newSelected.filter(
                    (value: any) => value !== 'allBranches'
                );
                props.setSelectedBranches(filteredValues);
            }
            return;
        }

        // Case 4: Normal selection/deselection without 'allBranches' involved
        if (!isAllBranchesSelected) {
            // select "All Branches" automatically when all individual branches are props.selected
            const isAllSelected = individualOptions.every((val) =>
                values.includes(val)
            );

            const finalSelected = isAllSelected
                ? [...values, 'allBranches']
                : values;

            props.setSelected(finalSelected);
            props.form.setFieldsValue({
                facility: finalSelected,
            });
            // Update parent component's state if setSelectedBranches is provided
            if (props.setSelectedBranches) {
                // Filter out 'allBranches' before sending to parent
                const filteredValues = finalSelected.filter(
                    (value) => value !== 'allBranches'
                );
                props.setSelectedBranches(filteredValues);
            }
            return;
        }

        // Default: update with the new values
        props.setSelected(values);
        props.form.setFieldsValue({
            facility: values,
        });
        // Update parent component's state if setSelectedBranches is provided
        if (props.setSelectedBranches) {
            // Filter out 'allBranches' before sending to parent
            const filteredValues = values.filter(
                (value) => value !== 'allBranches'
            );
            props.setSelectedBranches(filteredValues);
        }
    };
    // console.log('setSelectedsetSelectedsetSelected', props.selected);
    return (
        <div className="mb-8 flex w-full items-center">
            <Text className="w-[20%] font-medium text-[#1a3353]">
                Branch Name
            </Text>

            <div className="w-[80%]">
                <Form.Item
                    name="facility"
                    rules={[
                        {
                            required: true,
                            message: 'Please select at least one branch',
                        },
                    ]}
                >
                    <Select
                        mode="multiple"
                        // allowClear
                        placeholder="Select Branch"
                        value={props.selected}
                        maxTagCount={'responsive'}
                        onChange={handleChange}
                        tagRender={tagRender}
                    >
                        <Option key="allBranches" value="allBranches">
                            All Branches
                        </Option>
                        {facilities.map((facility: Facility) => (
                            <Option key={facility._id} value={facility._id}>
                                {facility.facilityName}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </div>
        </div>
    );
};

export default BranchSelector;

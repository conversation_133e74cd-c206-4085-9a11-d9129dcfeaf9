import { MoreOutlined } from '@ant-design/icons';
import { Dropdown, Pagination, Switch } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import CommonTable from '~/components/common/commonTable';
import CreateDiscountModal from './createDiscount';
import {
    DeleteDiscount,
    DiscountDetails,
    DiscountList,
    UpdateDiscountStatus,
} from '~/redux/actions/createDiscountAction';
import { useDispatch, useSelector } from 'react-redux';
import { getQueryParams } from '~/utils/getQueryParams';
import Alertify from '~/services/alertify';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { useAppSelector } from '~/hooks/redux-hooks';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';

const DiscountListing = () => {
    const [newDiscountModal, setNewDiscountModal] = useState(false);
    const dispatch = useDispatch();
    const [selectBranches, setSelectedBranches] = useState<string[]>([]);
    const [dataSource, setDataSource] = useState<string[]>([]);
    const [discountDetails, setDiscountDetails] = useState<string[]>([]);
    const [totalItems, setTotalItems] = useState(0);
    const params = getQueryParams();
    const [perPage, setPerPage] = useState(10);
    const pageParam = Number(params.page);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [edit, setEdit] = useState(false);
    const [editId, setEditId] = useState('');

    // For permissions
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasDiscountReadPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SERVICES_DISCOUNTS &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PROMOTION_READ
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    // for create discount
    const hasDiscountWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SERVICES_DISCOUNTS &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PROMOTION_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasDiscountUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SERVICES_DISCOUNTS &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PROMOTION_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasDiscountDeletePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SERVICES_DISCOUNTS &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PROMOTION_DELETE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const fetchDiscounts = (page: number, perPage: number) => {
        const queryParams = { page, perPage };
        if (hasDiscountReadPermission || store.role === RoleType.ORGANIZATION) {
            dispatch(DiscountList(queryParams))
                .then((response: any) => {
                    // console.log('Respons is::', response);
                    const data = response?.payload?.data?.data.map(
                        (item: any) => {
                            return {
                                ...item,
                                facilityIds: ((item?.facilityIds as []) || [])
                                    .map((cc: any) => cc.facilityName)
                                    .join(', '),
                            };
                        }
                    );
                    setDataSource(data);
                    setTotalItems(
                        response?.payload?.data?._metadata?.pagination?.total ||
                            0
                    );
                    // setDataSource(response?.payload?.data?.data);
                })
                .catch((error: any) => {
                    console.log('Error in fetch:', error);
                });
        } else {
            Alertify.error(
                "Sorry, you don't have the necessary permissions to perform this action"
            );
        }
    };
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState<boolean>(false);
    const [currentRecord, setCurrentRecord] = useState<any>(null);
    const openConfirmationModal = (record: any) => {
        console.log('record', record);
        setCurrentRecord(record);
        setConfirmationModalVisible(true);
    };
    console.log('currentRecord', currentRecord);
    const handleConfirmStatusChange = () => {
        setConfirmationModalVisible(false);
        dispatch(
            UpdateDiscountStatus({
                discountId: currentRecord._id,
                isActive: !currentRecord.isActive,
            })
        )
            .then(() => {
                Alertify.success('Discount status updated successfully');
                fetchDiscounts(currentPage, perPage);
            })
            .catch((error: any) => {
                console.log('Error in fetch:', error);
                Alertify.error('Could not update status!');
            });

        fetchDiscounts(currentPage, perPage);
        setCurrentRecord(null);
    };
    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
        setCurrentRecord(null);
    };
    const columns = [
        {
            key: 'discountName',
            title: 'Discount Name',
            dataIndex: 'name',
        },
        {
            title: 'Discount Type',
            key: 'type',
            dataIndex: 'type',
        },
        {
            title: 'Facility',
            dataIndex: 'facilityIds',
            key: 'facilityIds',
            width: '250px',
        },

        {
            title: 'Value',
            dataIndex: 'value',
            key: 'value',
            align: 'center',
        },
        {
            title: 'Status',
            dataIndex: '',
            key: 'isActive',
            align: 'center',
            render: (record: any) => {
                return (
                    <Switch
                        checked={record.isActive}
                        className="rounded-full transition-colors"
                        disabled={
                            !hasDiscountUpdatePermission &&
                            store.role != RoleType.ORGANIZATION
                        }
                        checkedChildren="ON"
                        unCheckedChildren="OFF"
                        onChange={() => openConfirmationModal(record)}
                    />
                );
            },
        },
        ...(hasDiscountUpdatePermission ||
        hasDiscountDeletePermission ||
        store.role === RoleType.ORGANIZATION
            ? [
                  {
                      title: 'Action',
                      dataIndex: '',
                      key: 'action',
                      width: '120px',
                      align: 'center',
                      render: (record: any) => {
                          const menuItems = [
                              ...(hasDiscountUpdatePermission ||
                              store.role === RoleType.ORGANIZATION
                                  ? [
                                        {
                                            key: 'edit',
                                            label: (
                                                <div
                                                    className="text-xl text-[#1A3353]"
                                                    onClick={() => {
                                                        setNewDiscountModal(
                                                            true
                                                        );
                                                        setEdit(true);
                                                        setEditId(record._id);
                                                    }}
                                                >
                                                    Edit Discount
                                                </div>
                                            ),
                                        },
                                    ]
                                  : []),
                              ...(hasDiscountDeletePermission ||
                              store.role === RoleType.ORGANIZATION
                                  ? [
                                        {
                                            key: 'delete',
                                            label: (
                                                <div
                                                    className="text-xl text-[#1A3353]"
                                                    onClick={() => {
                                                        console.log(
                                                            'discountId',
                                                            record._id
                                                        );
                                                        dispatch(
                                                            DeleteDiscount({
                                                                discountId:
                                                                    record._id,
                                                            })
                                                        ).then(() => {
                                                            Alertify.success(
                                                                'Discount deleted!'
                                                            );
                                                            fetchDiscounts(
                                                                currentPage,
                                                                perPage
                                                            );
                                                        });
                                                    }}
                                                >
                                                    Delete Discount
                                                </div>
                                            ),
                                        },
                                    ]
                                  : []),
                          ];

                          return (
                              <>
                                  <span className="flex justify-center gap-5 ">
                                      <div>
                                          <Dropdown
                                              menu={{ items: menuItems }}
                                              trigger={['click']}
                                          >
                                              <MoreOutlined
                                                  style={{
                                                      fontSize: '20px',
                                                      cursor: 'pointer',
                                                  }}
                                              />
                                          </Dropdown>
                                      </div>
                                  </span>
                              </>
                          );
                      },
                  },
              ]
            : []),
    ];

    useEffect(() => {
        if (hasDiscountReadPermission || store.role === RoleType.ORGANIZATION) {
            fetchDiscounts(currentPage, perPage);
            if (edit) {
                dispatch(
                    DiscountDetails({
                        discountId: editId,
                    })
                )
                    .then((response: any) => {
                        setDiscountDetails(response?.payload?.data?.data);
                    })
                    .catch((error: any) => {
                        console.log('Error in fetch:', error);
                    });
            }
        } else {
            Alertify.error(
                "Sorry, you don't have the necessary permissions to perform this action"
            );
        }
    }, [
        currentPage,
        perPage,
        edit,
        editId,
        hasDiscountReadPermission,
        store.role,
    ]);

    const handlePageChange = (page: number, perPage?: number) => {
        setCurrentPage(page);
        if (perPage) setPerPage(perPage);
    };

    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={columns}
                backButton={true}
                dataSource={dataSource}
                heading="Discount(s)"
                addNewTitle="Create Discount"
                addNewModal={
                    hasDiscountWritePermission ||
                    store.role === RoleType.ORGANIZATION
                }
                openModal={() => {
                    setNewDiscountModal(true);
                }}
            />

            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={totalItems}
                    pageSize={perPage}
                    onChange={handlePageChange}
                    showSizeChanger
                    pageSizeOptions={['10', '20', '50', '100']}
                />
            </div>
            <CreateDiscountModal
                edit={edit}
                setEdit={setEdit}
                editId={editId}
                setEditId={setEditId}
                selectBranches={selectBranches}
                setSelectedBranches={setSelectedBranches}
                newDiscountModal={newDiscountModal}
                setNewDiscountModal={setNewDiscountModal}
                discountDetails={discountDetails}
                setDiscountDetails={setDiscountDetails}
                setDataSource={setDataSource}
            />
            {confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={handleConfirmStatusChange}
                    onCancel={handleCancelStatusChange}
                    message={'Are you sure you want to change the status'}
                />
            )}
        </>
    );
};

export default DiscountListing;

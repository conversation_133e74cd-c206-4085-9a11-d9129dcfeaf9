// import { Typography } from 'antd';
import { CheckOutlined, CloseOutlined, MoreOutlined } from '@ant-design/icons';
import {
    Button,
    ConfigProvider,
    Dropdown,
    Image,
    Menu,
    Pagination,
    Switch,
    Tooltip,
    Typography,
} from 'antd';
import clsx from 'clsx';

import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { capitalizeFirstLetter } from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    BundlePricingList,
    CopyPricingAPI,
    DeletePricing,
    PricingList,
    UpdatePricingStatus,
} from '~/redux/actions/pricing-actions';
import Alertify from '~/services/alertify';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { getQueryParams } from '~/utils/getQueryParams';
// import NoShadowTable from '~/components/common/tableWithoutShadowBorder';
const { Title } = Typography;
const PricingListing: React.FC = () => {
    const params = getQueryParams();
    const searchParam = params.search;
    const pageSizeParam = Number(params.pageSize);
    const [search, setSearch] = useState('');
    const [isMakingDuplicate, setIsMakingDuplicate] = useState<boolean>(false);
    const [isDeleteing, setIsDeleting] = useState<boolean>(false);
    const [location, setLocation] = useLocation();
    const [selectedPricing, setSelectedPricing] = useState<string>(
        params.pricingType ? params.pricingType : 'regularPricing'
    );

    console.log('Selecred pricing-------------', selectedPricing);

    const pageParam = Number(params.page);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [loader, startLoader, endLoader] = useLoader(true);
    const [filterLoader, startFilterLoader, endFilterLoader] = useLoader(false);

    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const dispatch = useDispatch();
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const { pricingList, pricingListCount } = useSelector(
        (state: any) => state.pricing_store
    );

    const store = useAppSelector((state) => ({
        pricingList: state.pricing_store.pricingList,
        role: state.auth_store.role,
        bundlePricingList: state.pricing_store.bundlePricingList,
        bundlePricingListCount: state.pricing_store.bundlePricingListCount,
    }));

    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState<boolean>(false);
    const [pricingData, setPricingData] = useState<any>(null);

    // console.log('pricingList-----dfd------', store.bundlePricingList);

    const handlePricingTypeChange = (type: string) => {
        setSelectedPricing(type);
        setCurrentPage(1);
        setPageSize(10);

        navigate(`?pricingType=${type}&page=1&pageSize=10`, { replace: true });
    };

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?pricingType=${selectedPricing}&page=${page}&pageSize=${pageSize}&search=${search}`,
                { replace: true }
            );
        } else {
            navigate(
                `?pricingType=${selectedPricing}&page=${page}&pageSize=${pageSize}`,
                { replace: true }
            );
        }
    }
    const hasPricingPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PRICING_PRICING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PRICING_READ
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasPricingWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PRICING_PRICING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PRICING_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasPricingDeletePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PRICING_PRICING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PRICING_DELETE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasPricingUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PRICING_PRICING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PRICING_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasDiscountPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SERVICES_DISCOUNTS
            )
        );
    }, [all_permissions_for_role]);

    useEffect(() => {
        if (!hasPricingPermission && store.role !== RoleType.ORGANIZATION) {
            Alertify.error(
                "Sorry, you don't have the necessary permissions to perform this action"
            );
            return;
        }

        const fetchData = async () => {
            if (search) {
                startFilterLoader();
            } else {
                startLoader();
            }

            const action =
                selectedPricing === 'regularPricing'
                    ? PricingList
                    : BundlePricingList;

            try {
                await dispatch(
                    action({
                        page: currentPage,
                        pageSize: pageSizes,
                        ...(search && { search }),
                    })
                ).unwrap();
            } catch (error) {
                console.error('Error fetching data:', error);
            } finally {
                if (search) {
                    endFilterLoader();
                } else {
                    endLoader();
                }
            }
        };

        fetchData();
    }, [
        selectedPricing,
        currentPage,
        pageSizes,
        search,
        hasPricingPermission,
        store.role,
    ]);

    function handleSearch(value: string) {
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
        navigate(
            `?pricingType=${selectedPricing}&page=1&pageSize=10&search=${value}`,
            { replace: true }
        );
    }

    const handleCancelStatusChange = () => {
        setPricingData(null);
        setConfirmationModalVisible(false);
        setIsMakingDuplicate(false);
        setIsDeleting(false);
        setIsDeleting(false);
    };

    const handleDuplicate = async () => {
        await dispatch(CopyPricingAPI({ pricingId: pricingData?._id })).then(
            (res: any) => {
                console.log(res.payload);
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    setLocation(
                        `/${
                            pricingData?.isBundledPricing
                                ? 'create-bundle-pricing'
                                : 'create-pricing'
                        }/${res.payload.data?.data?._id}`
                    );
                }
            }
        );
        setIsMakingDuplicate(false);
        setPricingData(null);
        setConfirmationModalVisible(false);
    };

    const handleDelete = async () => {
        await dispatch(DeletePricing({ pricingId: pricingData?._id })).then(
            async (res: any) => {
                console.log(res.payload);
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    const action =
                        selectedPricing === 'regularPricing'
                            ? PricingList
                            : BundlePricingList;
                    await dispatch(
                        action({
                            page: currentPage,
                            pageSize: pageSizes,
                            ...(search && { search }),
                        })
                    ).unwrap();
                }
            }
        );
        setIsDeleting(false);
        setPricingData(null);
        setConfirmationModalVisible(false);
    };

    const handleConfirmStatusChange = () => {
        console.log(pricingData);
        const action =
            selectedPricing === 'regularPricing'
                ? 'regularPricingList'
                : 'BundlePricingList';
        dispatch(
            UpdatePricingStatus({
                packageId: pricingData._id,
                status: !pricingData.isActive,
                page: currentPage,
                pageSize: pageSizes,
                action: action,
            })
        );
        setConfirmationModalVisible(false);
        setPricingData(null);
    };

    const openConfirmationModal = (
        record: any,
        duplicate: boolean = false,
        deleteData: boolean = false
    ) => {
        setPricingData(record);
        setIsMakingDuplicate(duplicate);
        setIsDeleting(deleteData);
        setConfirmationModalVisible(true);
    };

    // for add pricing permissions
    const { role } = useSelector((state: any) => state.auth_store);

    const getColumns = (selectedPricing: string) => {
        const baseColumns = [
            {
                title: 'Pricing Option Name',
                dataIndex: 'name',
                render: (name: string, record: any) => (
                    <Link
                        to={
                            selectedPricing === 'regularPricing'
                                ? `/create-pricing/${record._id}?view=true`
                                : `/create-bundle-pricing/${record._id}?view=true`
                        }
                    >
                        {name}
                    </Link>
                ),
            },
            ...(selectedPricing === 'bundlePricing'
                ? []
                : [
                      {
                          title: 'Sessions',
                          dataIndex: '',
                          render: (record: any) => {
                              if (record?.services?.sessionType === 'single') {
                                  return '1';
                              } else if (
                                  record?.services?.sessionType === 'multiple'
                              ) {
                                  return record?.services?.sessionCount || '0';
                              } else if (
                                  record?.services?.sessionType === 'unlimited'
                              ) {
                                  return 'Unlimited';
                              } else if (
                                  record?.services?.sessionType === 'day_pass'
                              ) {
                                  return `${record?.services?.dayPassLimit} X Day Pass(es)`;
                              } else {
                                  return '-';
                              }
                          },
                      },
                  ]),
            {
                title: 'Service Type',
                dataIndex: '',
                align: 'center',
                render: (record: any) => {
                    const classTypes = record?.services?.type
                        ? record?.services?.type
                              .split(',')
                              .map((type: string) => type.trim())
                        : record?.classType
                              ?.split(',')
                              .map((type: string) => type.trim());

                    if (Array.isArray(classTypes)) {
                        const hasPersonalAppointment = classTypes.includes(
                            'personalAppointment'
                        );
                        const remainingTypes = classTypes.filter(
                            (type) => type !== 'personalAppointment'
                        );

                        return (
                            <>
                                {hasPersonalAppointment && (
                                    <span>Appointment</span>
                                )}
                                {hasPersonalAppointment &&
                                    remainingTypes.length > 0 &&
                                    ', '}
                                {remainingTypes.length > 0 && (
                                    <span>
                                        {remainingTypes
                                            .map(capitalizeFirstLetter)
                                            .join(', ')}
                                    </span>
                                )}
                            </>
                        );
                    }
                    return null;
                },
            },
            {
                title: 'Service Category',
                dataIndex: '',
                align: 'center',
                render: (record: any) => {
                    let serviceCategoryNames;

                    if (Array.isArray(record?.serviceCategoryNames)) {
                        serviceCategoryNames = record.serviceCategoryNames;
                    } else if (
                        typeof record?.serviceCategoryNames === 'string'
                    ) {
                        serviceCategoryNames =
                            record.serviceCategoryNames.split(',');
                    } else {
                        serviceCategoryNames = [];
                    }

                    const trimmedNames = serviceCategoryNames.map((name: any) =>
                        name.trim()
                    );
                    const displayNames = trimmedNames.slice(0, 2).join(', ');
                    const fullNames = trimmedNames.join(', ');

                    return (
                        <Tooltip title={fullNames}>
                            <div
                                style={{
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textAlign: 'center',
                                    textOverflow: 'ellipsis',
                                    maxWidth: 100,
                                    cursor: 'pointer',
                                }}
                            >
                                {trimmedNames.length > 2
                                    ? `${displayNames}, ...`
                                    : fullNames}
                            </div>
                        </Tooltip>
                    );
                },
            },
            {
                title: 'Price',
                dataIndex: '',
                render: (record: any) => {
                    const discountedPrice =
                        record.price &&
                        record.discountedValue &&
                        record.discountedValue;

                    return (
                        <span>
                            {discountedPrice && `${discountedPrice.toFixed(2)}`}
                        </span>
                    );
                },
            },
            {
                title: 'Sold Online',
                dataIndex: 'isSellOnline',
                align: 'center',
                render: (isSellOnline: boolean) => {
                    return isSellOnline ? <CheckOutlined /> : <CloseOutlined />;
                },
            },
            {
                title: 'Status',
                dataIndex: '',
                key: 'status',
                render: (record: any) => {
                    return (
                        <ConfigProvider
                            theme={{
                                components: {
                                    Switch: {
                                        colorTextQuaternary: 'gray',
                                        colorFillQuaternary: 'gray',
                                    },
                                },
                            }}
                        >
                            <Switch
                                className={clsx(
                                    'rounded-full transition-colors',
                                    record.isActive
                                        ? 'bg-switch-on'
                                        : 'bg-switch-off'
                                )}
                                id="swtich-off"
                                disabled={
                                    !hasPricingUpdatePermission &&
                                    store.role != RoleType.ORGANIZATION
                                }
                                checkedChildren="ON"
                                unCheckedChildren="OFF"
                                onChange={() => openConfirmationModal(record)}
                                checked={record.isActive || false}
                            />
                        </ConfigProvider>
                    );
                },
            },
            ...(hasPricingUpdatePermission ||
            hasPricingWritePermission ||
            hasPricingDeletePermission ||
            store.role === RoleType.ORGANIZATION
                ? [
                      {
                          title: 'Action',
                          dataIndex: '',
                          width: '120px',
                          align: 'center',
                          key: 'action',
                          render: (record: any) => {
                              const updateRoute =
                                  selectedPricing === 'regularPricing'
                                      ? `/create-pricing/${record._id}`
                                      : `/create-bundle-pricing/${record._id}`;
                              const menuItems = [
                                  ...(hasPricingUpdatePermission ||
                                  store.role === RoleType.ORGANIZATION
                                      ? [
                                            {
                                                key: 'edit',
                                                label: (
                                                    <Link to={updateRoute}>
                                                        Update
                                                    </Link>
                                                ),
                                            },
                                        ]
                                      : []),
                                  ...(hasPricingWritePermission ||
                                  store.role === RoleType.ORGANIZATION
                                      ? [
                                            {
                                                key: 'copy',
                                                label: (
                                                    <div
                                                        onClick={() =>
                                                            openConfirmationModal(
                                                                record,
                                                                true
                                                            )
                                                        }
                                                        style={{
                                                            cursor: 'pointer',
                                                        }}
                                                    >
                                                        Duplicate
                                                    </div>
                                                ),
                                            },
                                        ]
                                      : []),
                                  ...(hasPricingDeletePermission ||
                                  store.role === RoleType.ORGANIZATION
                                      ? [
                                            {
                                                key: 'delete',
                                                label: (
                                                    <div
                                                        onClick={() =>
                                                            openConfirmationModal(
                                                                record,
                                                                false,
                                                                true
                                                            )
                                                        }
                                                        style={{
                                                            cursor: 'pointer',
                                                        }}
                                                    >
                                                        Delete
                                                    </div>
                                                ),
                                            },
                                        ]
                                      : []),
                              ];
                              return (
                                  <Dropdown
                                      menu={{ items: menuItems }}
                                      trigger={['click']}
                                  >
                                      <MoreOutlined
                                          style={{
                                              fontSize: '20px',
                                              cursor: 'pointer',
                                          }}
                                      />
                                  </Dropdown>
                              );
                          },
                      },
                  ]
                : []),
        ];

        return baseColumns;
    };
    if (!hasPricingPermission && store.role !== RoleType.ORGANIZATION) {
        return null;
    }

    return loader ? (
        <FullLoader state={true} />
    ) : (
        <>
            <CommonTable
                className="min-w-min"
                headingContent="Listed below are the various ways that clients can pay for services as per business. Use this list to view and manage all your pricing options from a single spot."
                columns={getColumns(selectedPricing)}
                dataSource={
                    selectedPricing === 'regularPricing'
                        ? store.pricingList
                        : store.bundlePricingList
                }
                heading={
                    selectedPricing === 'regularPricing'
                        ? 'Regular Pricing'
                        : 'Bundle Pricing'
                }
                onSearch={handleSearch}
                loading={filterLoader}
                search={search}
                showSearch={true}
                {...((role === RoleType.ORGANIZATION ||
                    hasPricingWritePermission) &&
                    (selectedPricing === 'regularPricing'
                        ? {
                              addNewTitle: 'Create Pricing',
                              addNewLink: '/create-pricing/0',
                              checkPin: true,
                              module: SUBJECT_TYPE.PRICING_PRICING,
                              subModule: PERMISSIONS_ENUM.PRICING_WRITE,
                          }
                        : {
                              checkPin: true,
                              module: SUBJECT_TYPE.PRICING_PRICING,
                              subModule: PERMISSIONS_ENUM.PRICING_WRITE,
                          }))}
                discountManagementButton={
                    hasDiscountPermission ||
                    store.role === RoleType.ORGANIZATION
                }
                bundlePricing={true}
                pricingType={true}
                backButton={true}
                {...{
                    setSelectedPricing: handlePricingTypeChange,
                    selectedPricing,
                }}
            />

            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={
                        selectedPricing === 'regularPricing'
                            ? pricingListCount
                            : store.bundlePricingListCount
                    }
                    pageSize={pageSizes}
                    pageSizeOptions={['10', '20', '30']}
                    onChange={paginate}
                    hideOnSinglePage
                />
            </div>
            {confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={
                        isMakingDuplicate
                            ? handleDuplicate
                            : isDeleteing
                            ? handleDelete
                            : handleConfirmStatusChange
                    }
                    onCancel={handleCancelStatusChange}
                    message={`Are you sure you want to ${
                        isMakingDuplicate
                            ? 'duplicate the pricing'
                            : isDeleteing
                            ? 'delete the pricing'
                            : 'change the status'
                    }?`}
                />
            )}
        </>
    );
};

export default PricingListing;

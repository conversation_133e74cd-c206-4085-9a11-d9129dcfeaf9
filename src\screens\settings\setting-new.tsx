import { Switch } from 'antd';
import Title from 'antd/es/typography/Title';
import { useLocation, Link } from 'wouter';
import { useAppSelector } from '~/hooks/redux-hooks';
import { RoleType } from '~/types/enums';

const SettingNew = () => {
    const [_, setLocation] = useLocation();

    const navigateToRoom = () => {
        setLocation('/setting/room');
    };
    const navigateToAmenities = () => {
        setLocation('/amenities');
    };
    const navigateToFeatureTab = () => {
        setLocation('/setting/feature-tab');
    };
    function navigateToMembership() {
        setLocation('/setting/membership');
    }
    function navigateToRevenueCategory() {
        setLocation('/setting/revenue-category');
    }
    function navigateToAnnouncements() {
        setLocation('/setting/announcements');
    }
    function navigateToPaymentMethods() {
        setLocation('/payment-methods');
    }
    function navigateToClientOnboarding() {
        setLocation('/client-onboarding');
    }
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    return (
        <div>
            <Title className="pb-8 pt-5 text-[#1a3353]" level={4}>
                Settings
            </Title>
            <div className="rounded-2xl border border-[#E6EBF1] p-7 shadow-md lg:w-[80%]">
                <div className="rounded-2xl border border-[#E6EBF1] px-8 py-10 lg:w-[100%]">
                    <div className="flex w-[100%] flex-row gap-8 border-b border-[#E6EBF1] text-2xl font-semibold text-[#1a3353]">
                        <div className="w-[50%] pb-5">
                            <p>Names</p>
                        </div>
                        <div className="w-[50%] pb-5">
                            <p>Action</p>
                        </div>
                    </div>
                    <div className="flex h-20 w-[100%] flex-row items-center gap-8 border-b border-[#E6EBF1] text-xl  text-[#455560]">
                        <div
                            className="w-[50%] "
                            onClick={navigateToClientOnboarding}
                        >
                            <p className="cursor-pointer">Client Onboarding</p>
                        </div>
                        <div className="w-[50%] ">
                            <Link to={`/client-onboarding`}>
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                        </div>
                    </div>
                    <div className="flex h-20 w-[100%] flex-row items-center gap-8 border-b border-[#E6EBF1] text-xl  text-[#455560]">
                        <div className="w-[50%] " onClick={navigateToRoom}>
                            <p className="cursor-pointer">Rooms</p>
                        </div>
                        <div className="w-[50%] ">
                            <Link to={`/setting/room`}>
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                        </div>
                    </div>
                    <div className="flex h-20 w-[100%] flex-row items-center gap-8 border-b border-[#E6EBF1] text-xl  text-[#455560]">
                        <div className="w-[50%] ">
                            <p
                                className="cursor-pointer"
                                onClick={navigateToFeatureTab}
                            >
                                Featured Tabs
                            </p>
                        </div>
                        <div className="w-[50%] ">
                            <Link to={`/setting/feature-tab`}>
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                        </div>
                    </div>
                    {...store.role !== RoleType.FRONT_DESK_ADMIN
                        ? [
                              <div className="flex h-20 w-[100%] flex-row items-center gap-8 border-b border-[#E6EBF1] text-xl  text-[#455560]">
                                  <div
                                      className="w-[50%] "
                                      onClick={navigateToAmenities}
                                  >
                                      <p className="cursor-pointer">
                                          Amenities{' '}
                                      </p>
                                  </div>
                                  <div className="w-[50%] ">
                                      <Link to={`/amenities`}>
                                          <img
                                              src="/icons/common/edit.svg"
                                              alt="edit"
                                              className="h-[20px] cursor-pointer"
                                          />
                                      </Link>
                                  </div>
                              </div>,
                          ]
                        : []}
                    <div className="flex h-20 w-[100%] flex-row items-center gap-8 border-b border-[#E6EBF1] text-xl  text-[#455560]">
                        <div
                            className="w-[50%] "
                            onClick={navigateToMembership}
                        >
                            <p className="cursor-pointer">Membership </p>
                        </div>
                        <div className="w-[50%] ">
                            <Link to={`/setting/membership`}>
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                        </div>
                    </div>
                    <div className="flex h-20 w-[100%] flex-row items-center gap-8 border-b border-[#E6EBF1] text-xl  text-[#455560]">
                        <div
                            className="w-[50%] "
                            onClick={navigateToRevenueCategory}
                        >
                            <p className="cursor-pointer">Revenue Category </p>
                        </div>
                        <div className="w-[50%] ">
                            <Link to={`/setting/revenue-category`}>
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                        </div>
                    </div>
                    <div className="flex h-20 w-[100%] flex-row items-center gap-8 border-b border-[#E6EBF1] text-xl  text-[#455560]">
                        <div
                            className="w-[50%] "
                            onClick={navigateToPaymentMethods}
                        >
                            <p className="cursor-pointer">Payment Method </p>
                        </div>
                        <div className="w-[50%] ">
                            <Link to={`/payment-methods`}>
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                        </div>
                    </div>
                    <div className="flex h-20 w-[100%] flex-row items-center gap-8 border-b border-[#E6EBF1] text-xl  text-[#455560]">
                        <div
                            className="w-[50%] "
                            onClick={navigateToAnnouncements}
                        >
                            <p className="cursor-pointer">Announcements </p>
                        </div>
                        <div className="w-[50%] ">
                            <Link to={`/setting/announcements`}>
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SettingNew;

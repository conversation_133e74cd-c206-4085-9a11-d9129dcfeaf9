import { useEffect, useState } from 'react';
import { Button, Checkbox, Collapse, ConfigProvider, Form, Input } from 'antd';
import { useDispatch } from 'react-redux';
import { GetSettings, SaveSettings } from '~/redux/actions/settings-actions';
import Title from 'antd/es/typography/Title';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import LocalConfigProvider from '~/components/common/local-config-provider';

const { Panel } = Collapse;
const { Group: CheckboxGroup } = Checkbox;

interface SettingsState {
    clientOnboarding: {
        assessment: {
            weight: boolean;
            measurement: boolean;
        };
        policies: {
            facilityWaiver: boolean;
            safetyBriefingDone: boolean;
            checkForID: boolean;
        };
        notes: boolean;
    };
    staffOnboarding: {
        pin: boolean;
        skillsExperience: boolean;
        payRates: boolean;
        pinStandBy: number;
    };
}

const SettingScreen = () => {
    const dispatch = useDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const optionsAssessment = [
        { label: 'Weightyy', value: 'weight' },
        { label: 'Measurement', value: 'measurement' },
    ];

    const optionsPolicies = [
        { label: 'Facility Waiver', value: 'facilityWaiver' },
        { label: 'Safety Briefing Done', value: 'safetyBriefingDone' },
        { label: 'Check For ID', value: 'checkForID' },
    ];

    const initialState: SettingsState = {
        clientOnboarding: {
            assessment: {
                weight: false,
                measurement: false,
            },
            policies: {
                facilityWaiver: false,
                safetyBriefingDone: false,
                checkForID: false,
            },
            notes: false,
        },
        staffOnboarding: {
            pin: false,
            skillsExperience: false,
            payRates: false,
            pinStandBy: 30,
        },
    };

    const [state, setState] = useState<SettingsState | any>(initialState);

    const handleCheckboxChange =
        (section: any, key: any, subKey?: any) => (e: any) => {
            setState((prevState: any) => {
                const newSection = { ...prevState[section] };

                if (subKey) {
                    newSection[key] = {
                        ...newSection[key],
                        [subKey]: e.target.checked,
                    };
                } else {
                    newSection[key] = e.target.checked;
                }
                return { ...prevState, [section]: newSection };
            });
        };

    const handleGroupChange =
        (section: any, key: any, options: any) => (list: any) => {
            setState((prevState: any) => ({
                ...prevState,
                [section]: {
                    ...prevState[section],
                    [key]: Object.fromEntries(
                        options.map((option: any) => [
                            option.value,
                            list.includes(option.value),
                        ])
                    ),
                },
            }));
        };

    const handleCheckAllChange =
        (section: any, key: any, options: any) => (e: any) => {
            setState((prevState: any) => {
                const newState = { ...prevState };
                newState[section] = {
                    ...newState[section],
                    [key]: {
                        ...newState[section][key],
                        ...Object.fromEntries(
                            options.map((option: any) => [
                                option.value,
                                e.target.checked,
                            ])
                        ),
                    },
                };
                return newState;
            });
        };

    function saveSettings() {
        console.log('Save settings-----------', state);
        dispatch(SaveSettings({ state })).then((res: any) => {
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                dispatch(GetSettings({}));
            }
        });
    }

    useEffect(() => {
        startLoader();
        dispatch(GetSettings({}))
            .unwrap()
            .then((data: any) => {
                if (data && data.data && data.data.data) {
                    const { _id, ...responseData } = data.data.data;
                    if (Object.keys(responseData).length > 0) {
                        setState(responseData);
                    }
                }
            })
            .finally(endLoader);
    }, []);

    // ===========================stand by time input====================

    const onChange = (text: any) => {
        console.log('onChange:', text.target.value);
        setState((state: any) => ({
            ...state,
            staffOnboarding: {
                ...state.staffOnboarding,
                pinStandBy: Number(text.target.value),
            },
        }));
    };
    const sharedProps = {
        onChange,
        value: state.staffOnboarding?.pinStandBy,
    };

    return (
        <LocalConfigProvider>
            <div className="">
                <Title className="pb-8 pt-5 text-[#1a3353]" level={4}>
                    Settings
                </Title>
                <ConfigProvider
                    theme={{
                        token: {
                            colorPrimary: '#8143D1',
                        },
                        components: {
                            Collapse: {
                                contentBg: '#fff',
                                headerBg: '#fff',
                            },
                        },
                    }}
                >
                    {loader ? (
                        <FullLoader state={true} />
                    ) : (
                        <>
                            <Collapse
                                bordered={false}
                                className="flex flex-col gap-5"
                                defaultActiveKey={['1', '2', '3']}
                            >
                                <Panel
                                    className="rounded-xl"
                                    header={
                                        <p className="font-bold text-[#1a3353]">
                                            Client Onboarding
                                        </p>
                                    }
                                    key="1"
                                >
                                    {/* Assessment */}
                                    <div className="flex flex-col gap-14 py-5 ps-12">
                                        <div className="w-fit">
                                            <ConfigProvider theme={{}}>
                                                <Checkbox
                                                    indeterminate={
                                                        Object.values(
                                                            state
                                                                .clientOnboarding
                                                                ?.assessment
                                                        ).filter(Boolean)
                                                            .length > 0 &&
                                                        Object.values(
                                                            state
                                                                .clientOnboarding
                                                                ?.assessment
                                                        ).filter(Boolean)
                                                            .length <
                                                            optionsAssessment.length
                                                    }
                                                    onChange={handleCheckAllChange(
                                                        'clientOnboarding',
                                                        'assessment',
                                                        optionsAssessment
                                                    )}
                                                    checked={Object.values(
                                                        state.clientOnboarding
                                                            ?.assessment
                                                    ).every(Boolean)}
                                                >
                                                    Assessment
                                                </Checkbox>
                                            </ConfigProvider>
                                            <div className="ml-12 flex flex-col gap-4 pt-4">
                                                {optionsAssessment.map(
                                                    (option) => (
                                                        <Checkbox
                                                            key={option.value}
                                                            checked={
                                                                state
                                                                    .clientOnboarding
                                                                    ?.assessment[
                                                                    option.value
                                                                ]
                                                            }
                                                            onChange={handleCheckboxChange(
                                                                'clientOnboarding',
                                                                'assessment',
                                                                option.value
                                                            )}
                                                        >
                                                            {option.label}
                                                        </Checkbox>
                                                    )
                                                )}
                                            </div>
                                        </div>

                                        {/* Policies */}
                                        <div className="">
                                            <Checkbox
                                                indeterminate={
                                                    Object.values(
                                                        state.clientOnboarding
                                                            ?.policies
                                                    ).filter(Boolean).length >
                                                        0 &&
                                                    Object.values(
                                                        state.clientOnboarding
                                                            ?.policies
                                                    ).filter(Boolean).length <
                                                        optionsPolicies.length
                                                }
                                                onChange={handleCheckAllChange(
                                                    'clientOnboarding',
                                                    'policies',
                                                    optionsPolicies
                                                )}
                                                checked={Object.values(
                                                    state.clientOnboarding
                                                        ?.policies
                                                ).every(Boolean)}
                                            >
                                                Policies
                                            </Checkbox>
                                            <div className="ml-12 flex flex-col gap-4 pt-4">
                                                {optionsPolicies.map(
                                                    (option) => (
                                                        <Checkbox
                                                            key={option.value}
                                                            checked={
                                                                state
                                                                    .clientOnboarding
                                                                    ?.policies[
                                                                    option.value
                                                                ]
                                                            }
                                                            onChange={handleCheckboxChange(
                                                                'clientOnboarding',
                                                                'policies',
                                                                option.value
                                                            )}
                                                        >
                                                            {option.label}
                                                        </Checkbox>
                                                    )
                                                )}
                                            </div>
                                        </div>

                                        {/* Notes */}
                                        <div className=" ">
                                            <Checkbox
                                                checked={
                                                    state.clientOnboarding
                                                        ?.notes
                                                }
                                                onChange={handleCheckboxChange(
                                                    'clientOnboarding',
                                                    'notes'
                                                )}
                                            >
                                                Notes
                                            </Checkbox>
                                        </div>
                                    </div>
                                </Panel>

                                {/* Staff Onboarding */}
                                <Panel
                                    className="rounded-xl"
                                    header={
                                        <p className="font-bold text-[#1a3353]">
                                            Staff Onboarding
                                        </p>
                                    }
                                    key="2"
                                >
                                    <div className="flex flex-col  py-5 ps-12">
                                        <div className=" lg:h-24 @sm:h-24">
                                            <Checkbox
                                                checked={
                                                    state.staffOnboarding?.pin
                                                }
                                                onChange={handleCheckboxChange(
                                                    'staffOnboarding',
                                                    'pin'
                                                )}
                                            >
                                                Pin
                                            </Checkbox>
                                            {state.staffOnboarding?.pin && (
                                                <div className="flex flex-row items-center gap-1 lg:ps-10">
                                                    <p className="text-xl text-gray-600">
                                                        Standby Time
                                                    </p>
                                                    <Input
                                                        className="h-9 w-[10%]"
                                                        {...sharedProps}
                                                        maxLength={2}
                                                        min={0}
                                                        type="number"
                                                    />
                                                    <p className="text-xl text-gray-600">
                                                        Seconds
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                        <div className="flex flex-col gap-14">
                                            <Checkbox
                                                checked={
                                                    state.staffOnboarding
                                                        ?.skillsExperience
                                                }
                                                onChange={handleCheckboxChange(
                                                    'staffOnboarding',
                                                    'skillsExperience'
                                                )}
                                            >
                                                Skills and Experience
                                            </Checkbox>
                                            <Checkbox
                                                checked={
                                                    state.staffOnboarding
                                                        ?.payRates
                                                }
                                                onChange={handleCheckboxChange(
                                                    'staffOnboarding',
                                                    'payRates'
                                                )}
                                            >
                                                Pay Rates
                                            </Checkbox>
                                        </div>
                                    </div>
                                </Panel>
                            </Collapse>
                            <Form>
                                <div className="flex items-end justify-end gap-5 py-16 lg:flex-row">
                                    <Form.Item>
                                        <Button
                                            // type="default "
                                            className="border-1 border-[#1A3353] px-8 py-7 text-xl lg:w-[120px]"
                                        >
                                            cancel
                                        </Button>
                                    </Form.Item>
                                    <Form.Item>
                                        <Button
                                            type="primary"
                                            className="bg-purpleLight px-14 py-7 text-xl lg:w-[120px] "
                                            htmlType="submit"
                                            onClick={saveSettings}
                                        >
                                            Save
                                        </Button>
                                    </Form.Item>
                                </div>
                            </Form>
                        </>
                    )}
                </ConfigProvider>
            </div>
        </LocalConfigProvider>
    );
};

export default SettingScreen;

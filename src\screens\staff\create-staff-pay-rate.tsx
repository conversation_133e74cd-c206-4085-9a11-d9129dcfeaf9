import { But<PERSON>, ConfigProvider, Form, Select } from 'antd';
import Title from 'antd/es/typography/Title';
function goBack() {
    window.history.back();
}
const CreateStaffPayRate = () => {
    return (
        <>
            <div className="flex items-center gap-4 ">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title level={4}>Create Staff Pay Rate</Title>
            </div>
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            itemMarginBottom: 22,
                            verticalLabelMargin: -5,
                        },
                    },
                }}
            >
                <div className="border-[rgba(0, 0, 0, 0.1)] my-10  rounded-xl border-1 lg:w-[80%] lg:p-8 @sm:p-4 ">
                    <Form
                        name="PayrateCreate"
                        layout="vertical"
                        size="large"
                        autoComplete="off"
                    >
                        <div className="flex lg:flex-row   lg:gap-10 @sm:flex-col">
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label={
                                        <p className="text-xl font-semibold lg:ps-2">
                                            Service Category
                                        </p>
                                    }
                                >
                                    <Select>
                                        <Select.Option value="demo">
                                            Demo
                                        </Select.Option>
                                    </Select>
                                </Form.Item>
                            </div>
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label={
                                        <p className="text-xl font-semibold lg:ps-2">
                                            Appointment Type
                                        </p>
                                    }
                                >
                                    <Select>
                                        <Select.Option value="demo">
                                            Demo
                                        </Select.Option>
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex gap-10  lg:flex-row @sm:flex-col">
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label={
                                        <p className="text-xl font-semibold lg:ps-2">
                                            Type
                                        </p>
                                    }
                                >
                                    <Select>
                                        <Select.Option value="demo">
                                            Demo
                                        </Select.Option>
                                    </Select>
                                </Form.Item>
                            </div>
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label={
                                        <p className="text-xl font-semibold lg:ps-2">
                                            Length
                                        </p>
                                    }
                                >
                                    <Select>
                                        <Select.Option value="demo">
                                            Demo
                                        </Select.Option>
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex gap-10  lg:flex-row @sm:flex-col">
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label={
                                        <p className="text-xl font-semibold lg:ps-2">
                                            Tier
                                        </p>
                                    }
                                >
                                    <Select>
                                        <Select.Option value="demo">
                                            Demo
                                        </Select.Option>
                                    </Select>
                                </Form.Item>
                            </div>
                            <div className=" w-full px-2 md:w-1/2">
                                <Form.Item
                                    label={
                                        <p className="text-xl font-semibold lg:ps-2">
                                            Staff Pay Rate
                                        </p>
                                    }
                                >
                                    <Select>
                                        <Select.Option value="demo">
                                            Demo
                                        </Select.Option>
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>
                    </Form>

                    <div className="flex gap-5 lg:justify-end lg:pt-32 @sm:justify-center">
                        <Button
                            type="default "
                            className="border-1 border-[#1A3353] py-7 text-xl  lg:w-[120px]  @sm:w-[100px]"
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            className="bg-[#8143d1] py-7 text-xl  lg:w-[120px] @sm:w-[100px]"
                            htmlType="submit"
                        >
                            Save
                        </Button>
                    </div>
                </div>
            </ConfigProvider>
        </>
    );
};

export default CreateStaffPayRate;

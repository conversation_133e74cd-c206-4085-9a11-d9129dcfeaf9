import { Button, Form, Input, Select } from 'antd';
import Title from 'antd/es/typography/Title';
import React, { useState } from 'react';
const { Option } = Select;

import PasswordChecklist from '~/components/common/password-checklist';

const StaffOnboard = () => {
    const [password, setPassword] = useState('');

    return (
        <>
            <Title className=" text-[#1a3353]" level={4}>
                Setup your Account
            </Title>

            <div className=" mt-10 rounded-xl border-1 pb-5 lg:w-[75%] lg:px-14 @sm:px-5 ">
                <Form
                    className="pt-10"
                    name="add-trainer"
                    layout="vertical"
                    size="large"
                    autoComplete="off"
                    initialValues={{ country: 'india' }}
                    // form={form}
                >
                    <div className="flex lg:flex-row lg:gap-10 @sm:flex-col">
                        <Form.Item
                            label="First Name"
                            className="w-full"
                            name="firstName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter first name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter First Name" />
                        </Form.Item>
                        <Form.Item
                            className="w-full"
                            label="Last Name"
                            name="lastName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter last name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Last Name" />
                        </Form.Item>
                    </div>
                    <div className="flex lg:flex-row lg:gap-10 @sm:flex-col">
                        <Form.Item
                            className="w-full"
                            label="Email"
                            name="email"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter email',
                                },
                                {
                                    type: 'email',
                                    message: 'This is not valid E-mail!',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Email" />
                        </Form.Item>
                        <Form.Item
                            className="w-full"
                            label="Password"
                            name="password"
                            rules={[
                                {
                                    required: true,
                                    // message: 'Please input password',
                                },
                            ]}
                        >
                            <Input.Password
                                placeholder="Enter Password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                            />
                        </Form.Item>
                    </div>

                    <div className="flex lg:flex-row lg:gap-10 lg:py-6 @sm:flex-col">
                        <Form.Item
                            className="w-full"
                            label="Country"
                            name="country"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select country',
                                },
                            ]}
                        >
                            <Select
                                placeholder="Select Country"
                                disabled={false}
                            >
                                <Option value="india">India</Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            className="w-full"
                            label="Pin"
                            name="Password"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter Pin',
                                },
                            ]}
                        >
                            <Input.Password placeholder="Enter Pin" />
                        </Form.Item>
                    </div>
                    <PasswordChecklist password={password} />
                    <div className="flex items-end justify-end gap-5  lg:flex-row">
                        <Form.Item>
                            <Button
                                // type="default "
                                className="border-1 border-[#1A3353] py-7 text-xl  lg:w-[120px]"
                            >
                                Cancel
                            </Button>
                        </Form.Item>
                        <Form.Item>
                            <Button
                                type="primary"
                                className="bg-purpleLight py-7 text-xl lg:w-[120px]  "
                                htmlType="submit"
                            >
                                Save
                            </Button>
                        </Form.Item>
                    </div>
                </Form>
            </div>
        </>
    );
};

export default StaffOnboard;

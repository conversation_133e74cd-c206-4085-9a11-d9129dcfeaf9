import { Button, Checkbox, Form, Modal, Select, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { LoaderIcon } from 'react-hot-toast';
import { useDispatch } from 'react-redux';
import {
    AddNewActionId,
    GetPermissionsWithoutActionId,
    GetSubjectDetails,
} from '~/redux/actions/permission-action';
import Alertify from '~/services/alertify';

const { Text } = Typography;
interface Permission {
    _id: string;
    type: string;
    description: string;
    isActive: boolean;
    isDelegated: boolean;
    name: string;
    permitted: boolean;
}
const AddActionModal = (props: any) => {
    const dispatch = useDispatch();
    const [pageLoading, setPageLoading] = useState(false);
    const [permissions, setPermissions] = useState<Permission[]>([]);
    const [selectedAction, setSelectedAction] = useState<string>('');

    useEffect(() => {
        dispatch(GetPermissionsWithoutActionId({ pageSize: 1000 }))
            .then((res: any) => {
                const permissionsData = res?.payload?.data?.data || [];
                setPageLoading(false);
                setPermissions(permissionsData);
            })
            .catch((error: any) => {
                Alertify.error(
                    'Error GetPermissionsWithoutActionId API',
                    error
                );
            });
    }, [dispatch]);
    const handleCheckboxChange = (permissionId: string, checked: boolean) => {
        setPermissions((prevPermissions) =>
            prevPermissions.map((permission) =>
                permission._id === permissionId
                    ? { ...permission, permitted: checked }
                    : permission
            )
        );
    };
    const handleActionSelect = (value: string) => {
        setSelectedAction(value);
    };
    const updatePermissions = () => {
        setPageLoading(true);
        const checkedPermissions = permissions
            .filter((permission) => permission.permitted)
            .map((permission) => permission._id);

        dispatch(
            AddNewActionId({
                subject: props.subjectName,
                type: props.subjectType,
                module: props.module,
                action: selectedAction,
                permissions: checkedPermissions,
            })
        )
            .then((response: any) => {
                Alertify.success('Permissions updated successfully');
                setPageLoading(false);
                props.setActionModal(false);
                dispatch(GetSubjectDetails({ subjectType: props.subjectType }))
                    .then((res: any) => {
                        const fetchedData = res?.payload?.data?.data;
                        // console.log('GetSubjectDetails', res?.payload?.data?.data);
                        props.setSubjectName(fetchedData.subject);
                        props.setModule(fetchedData.actions[0].module);
                        // console.log('Module is: ', fetchedData.actions[0].module);
                        props.setStoredPolicyData(fetchedData);
                    })
                    .catch((error: any) => {
                        Alertify.error('Error GetSubjectDetails API', error);
                    });
            })
            .catch((error: any) => {
                Alertify.error('Error updating permissions', error);
                setPageLoading(false);
            });
    };
    return (
        <div>
            <Modal
                title="Add Action"
                centered
                width={1000}
                open={props.actionModal}
                onCancel={() => {
                    props.setActionModal(false);
                }}
                footer={null}
            >
                <Form layout="vertical" size="large" autoComplete="off">
                    <Form.Item
                        label="Action Name"
                        name="actionName"
                        rules={[
                            {
                                required: true,
                                message: 'Please select an action',
                            },
                        ]}
                    >
                        <Select
                            options={props.actionOptions}
                            placeholder="Select an action"
                            onChange={handleActionSelect}
                            value={selectedAction}
                        />
                    </Form.Item>
                    <div className="relative">
                        <div className="max-h-[60vh] min-h-[45vh] overflow-y-scroll rounded-lg border-1 p-4">
                            {pageLoading ? (
                                <div className="flex min-h-[45vh] items-center justify-center">
                                    <LoaderIcon className="h-[30px] w-[30px]" />
                                </div>
                            ) : (
                                <div>
                                    <div className="grid grid-cols-3 gap-4">
                                        {permissions.map((permission) => (
                                            <div
                                                key={permission._id}
                                                className="flex items-start p-2"
                                            >
                                                <Checkbox
                                                    checked={
                                                        permission.permitted
                                                    }
                                                    onChange={(e) =>
                                                        handleCheckboxChange(
                                                            permission._id,
                                                            e.target.checked
                                                        )
                                                    }
                                                >
                                                    <div className="flex flex-col">
                                                        <Text strong>
                                                            {permission.name}
                                                        </Text>
                                                        <Text type="secondary">
                                                            {
                                                                permission.description
                                                            }
                                                        </Text>
                                                    </div>
                                                </Checkbox>
                                            </div>
                                        ))}
                                    </div>

                                    <div className="text-end">
                                        <div>
                                            <Button
                                                type="primary"
                                                className="invisible w-[150px] bg-primary text-white"
                                                disabled={true}
                                            ></Button>
                                        </div>
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            className="absolute bottom-4 right-4 w-[150px] bg-primary text-white"
                                            onClick={updatePermissions}
                                        >
                                            SAVE CHANGES
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </Form>
            </Modal>
        </div>
    );
};

export default AddActionModal;

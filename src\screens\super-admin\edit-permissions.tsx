import { useEffect, useState } from 'react';
import { Button, Select, Typography } from 'antd';
import { useDispatch } from 'react-redux';
import { useLocation } from 'wouter';
import Alertify from '~/services/alertify';
import {
    GetAllPermissionsOnRole,
    UpdateAllPermissionsOnRole,
} from '~/redux/actions/permission-action';

const { Text, Title } = Typography;

interface Action {
    label: string;
    value: string;
    permitted: boolean;
}

interface Permission {
    name: string;
    actions: Action[];
    defaultValue: string;
}

interface PermissionSection {
    category: string;
    permissions: Permission[];
}

interface Subject {
    subject: string;
    actions: Array<{
        action: string;
        _id: string;
        permitted: boolean;
    }>;
}

interface RawDataItem {
    module: string;
    subjects: Subject[];
}

interface SelectedValues {
    [key: string]: string;
}

const EditPermissions = () => {
    const [location] = useLocation();
    const dispatch = useDispatch();
    const [permissionsData, setPermissionsData] = useState<PermissionSection[]>(
        []
    );
    const [isEditing, setIsEditing] = useState<boolean>(false);
    const [selectedValues, setSelectedValues] = useState<SelectedValues>({});
    const [roleId, setRoleId] = useState<string>('');

    const fetchPermissions = () => {
        const path = location.split('/');
        const roleId = String(path[path.length - 1]);
        setRoleId(roleId);
        const payload = { roleId };

        dispatch(GetAllPermissionsOnRole(payload))
            .then((res: any) => {
                const rawData: RawDataItem[] = res?.payload?.data?.data || [];
                const groupedPermissions: Record<string, PermissionSection> =
                    {};

                rawData.forEach((item: RawDataItem) => {
                    const category = item.module;

                    if (!groupedPermissions[category]) {
                        groupedPermissions[category] = {
                            category,
                            permissions: [],
                        };
                    }

                    item.subjects?.forEach((subject: Subject) => {
                        const defaultOptions: Action[] = [
                            {
                                label: 'RESTRICTED',
                                value: 'RESTRICTED',
                                permitted: false,
                            },
                        ];

                        const userActions: Action[] =
                            subject.actions?.map((action) => ({
                                label: action.action.toUpperCase(),
                                value: action._id,
                                permitted: action.permitted,
                            })) || [];

                        const actionOptions = [
                            ...defaultOptions,
                            ...userActions,
                        ];

                        let defaultValue = 'RESTRICTED';
                        const permittedActions = userActions.filter(
                            (action) => action.permitted
                        );
                        if (permittedActions.length > 0) {
                            defaultValue = permittedActions[0].value;
                        }

                        const key = `${category}-${subject.subject}`;
                        setSelectedValues((prev) => ({
                            ...prev,
                            [key]: defaultValue,
                        }));

                        groupedPermissions[category].permissions.push({
                            name: subject.subject,
                            actions: actionOptions,
                            defaultValue,
                        });
                    });
                });

                const transformedData = Object.values(groupedPermissions);
                setPermissionsData(transformedData);
            })
            .catch((error: any) => {
                Alertify.error(error);
            });
    };

    useEffect(() => {
        fetchPermissions();
    }, [dispatch]);

    const handleEdit = () => {
        setIsEditing(true);
    };

    const handleSave = () => {
        const policies: string[] = [];

        permissionsData.forEach((section) => {
            section.permissions.forEach((perm) => {
                const key = `${section.category}-${perm.name}`;
                const selectedValue = selectedValues[key];

                if (selectedValue !== 'RESTRICTED') {
                    policies.push(selectedValue);
                }
            });
        });

        dispatch(UpdateAllPermissionsOnRole({ roleId, policies }))
            .then((res: any) => {
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    Alertify.success('Permissions updated successfully');
                    fetchPermissions();
                }
            })
            .catch((error: any) => {
                Alertify.error(error);
            });
        setIsEditing(false);
    };

    const handleSelectChange = (
        value: string,
        section: string,
        permName: string
    ) => {
        const key = `${section}-${permName}`;
        setSelectedValues((prev) => ({
            ...prev,
            [key]: value,
        }));
    };

    return (
        <div className="mx-auto w-full rounded-xl p-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={() => window.history.back()}
                    />
                    <Title level={4} className="underline">
                        Role Permissions
                    </Title>
                </div>
                {!isEditing ? (
                    <Button
                        className="bg-primary text-white"
                        onClick={handleEdit}
                    >
                        EDIT
                    </Button>
                ) : null}
            </div>
            <div className="mt-4 p-8">
                {permissionsData.map((section, index) => (
                    <div key={index}>
                        <Text strong>{section.category}</Text>
                        {section.permissions.map((perm, i) => (
                            <div
                                key={i}
                                className="my-4 flex items-center justify-between rounded-md px-4 py-2 shadow-sm"
                            >
                                <Text>{perm.name}</Text>
                                <Select
                                    options={perm.actions}
                                    value={
                                        selectedValues[
                                            `${section.category}-${perm.name}`
                                        ]
                                    }
                                    onChange={(value) =>
                                        handleSelectChange(
                                            value,
                                            section.category,
                                            perm.name
                                        )
                                    }
                                    disabled={!isEditing}
                                    className="w-[150px]"
                                />
                            </div>
                        ))}
                    </div>
                ))}
            </div>
            <div className="text-right">
                {isEditing ? (
                    <Button
                        className="bg-primary text-white"
                        onClick={handleSave}
                    >
                        SAVE
                    </Button>
                ) : null}
            </div>
        </div>
    );
};

export default EditPermissions;

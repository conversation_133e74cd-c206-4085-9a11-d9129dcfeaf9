import React, { useEffect, useState } from 'react';
import { Button, Form, Modal, Select, Tabs, Typography } from 'antd';
import { useLocation } from 'wouter';
import AllPermissionsList from './all-permissions-list';
import { useDispatch } from 'react-redux';
import { GetSubjectDetails } from '~/redux/actions/permission-action';
import Alertify from '~/services/alertify';
import { LoaderIcon } from 'react-hot-toast';
import AddActionModal from './add-action-modal';

const { Title } = Typography;

const EditPolicy = () => {
    const [location] = useLocation();

    const path = location.split('/');
    const subjectType = String(path[path.length - 1]);
    // console.log('subjectType', subjectType);
    const [subjectName, setSubjectName] = useState<string>('');
    const [module, setModule] = useState<string>('');
    const [tabItems, setTabItems] = useState<any[]>([]);
    const [permissions, setPermissions] = useState<Record<string, boolean>>({});
    const [storedPolicyData, setStoredPolicyData] = useState<any>(null);
    const dispatch = useDispatch();
    const [actionModal, setActionModal] = useState<boolean>(false);
    const [existingActions, setExistingActions] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    useEffect(() => {
        dispatch(GetSubjectDetails({ subjectType }))
            .then((res: any) => {
                const fetchedData = res?.payload?.data?.data;
                // console.log('GetSubjectDetails', res?.payload?.data?.data);
                setSubjectName(fetchedData.subject);
                setModule(fetchedData.actions[0].module);
                // console.log('Module is: ', fetchedData.actions[0].module);
                setStoredPolicyData(fetchedData);
            })
            .catch((error: any) => {
                Alertify.error('Error GetSubjectDetails API', error);
            });
    }, [dispatch, setSubjectName, setStoredPolicyData]);

    useEffect(() => {
        setLoading(true);
        if (storedPolicyData) {
            // console.log('storedPolicyData:::::::::', storedPolicyData);
            try {
                setSubjectName(storedPolicyData.subject);
                // Log each action object with its properties
                if (
                    storedPolicyData.actions &&
                    storedPolicyData.actions.length > 0
                ) {
                    const formattedTabItems = storedPolicyData.actions.map(
                        (action: any) => ({
                            key: action._id,
                            label: (
                                <div className="uppercase">{action.action}</div>
                            ),
                            children: (
                                <div className="max-h-[60vh] min-h-[45vh] overflow-y-scroll rounded-lg border-1 p-4">
                                    <AllPermissionsList actionId={action._id} />
                                </div>
                            ),
                        })
                    );

                    setTabItems(formattedTabItems);

                    // Initialize permissions state
                    const initialPermissions: Record<string, boolean> = {};
                    storedPolicyData.actions.forEach((action: any) => {
                        initialPermissions[action._id] = true; // Default to permitted
                    });
                    setPermissions(initialPermissions);
                    setLoading(false);
                } else {
                    // Fallback items if no actions are available
                    setTabItems([
                        {
                            key: '1',
                            label: 'No Actions Available',
                            children:
                                'No action data was found for this policy.',
                        },
                    ]);
                    setLoading(false);
                }
            } catch (error) {
                console.error(
                    'Error parsing policy data from localStorage:',
                    error
                );
            }
        }
    }, [storedPolicyData, setTabItems]);

    useEffect(() => {
        if (storedPolicyData?.actions) {
            const actions = storedPolicyData.actions.map((action: any) =>
                action.action.toLowerCase()
            );
            setExistingActions(actions);
        }
    }, [storedPolicyData]);

    const actionOptions = [
        {
            label: 'Create',
            value: 'create',
            disabled: existingActions.includes('create'),
        },
        {
            label: 'View',
            value: 'view',
            disabled: existingActions.includes('view'),
        },
        {
            label: 'Update',
            value: 'update',
            disabled: existingActions.includes('update'),
        },
        {
            label: 'Full Access',
            value: 'full access',
            disabled: existingActions.includes('full access'),
        },
    ];

    // // Fallback items if no actions are available
    // const items = [
    //     {
    //         key: '1',
    //         label: 'No Actions Available',
    //         children: 'No action data was found for this policy.',
    //     },
    // ];
    return (
        <div className="mx-auto w-full rounded-xl p-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={() => window.history.back()}
                    />
                    <Title level={4}>Edit Policy - {subjectName}</Title>
                </div>
                <Button
                    className="bg-primary text-white"
                    onClick={() => setActionModal(true)}
                >
                    Add Action +
                </Button>
            </div>
            <div className="mt-8 min-h-[45vh] rounded-xl border-1 bg-slate-50 p-8">
                {loading ? (
                    <div className="flex min-h-[45vh] items-center justify-center">
                        <LoaderIcon className="h-[30px] w-[30px]" />
                    </div>
                ) : (
                    <Tabs
                        defaultActiveKey={
                            tabItems.length > 0 ? tabItems[0].key : '1'
                        }
                        items={tabItems}
                        // onChange={tabOnChange}
                        tabPosition="top"
                        tabBarStyle={{}}
                    />
                )}
            </div>
            <AddActionModal
                actionModal={actionModal}
                setActionModal={setActionModal}
                actionOptions={actionOptions}
                subjectType={subjectType}
                module={module}
                subjectName={subjectName}
                setSubjectName={setSubjectName}
                setModule={setModule}
                setStoredPolicyData={setStoredPolicyData}
            />
        </div>
    );
};

export default EditPolicy;

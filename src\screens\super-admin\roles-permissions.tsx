import { EditFilled } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { Link } from 'wouter';
import CommonTable from '~/components/common/commonTable';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { GetAllRolesList } from '~/redux/actions/permission-action';
import Alertify from '~/services/alertify';

interface Role {
    _id: string;
    name: string;
    type: string;
    isActive: boolean;
    isDefault: boolean;
    policies: any[];
    delegatedPolicies: any[];
    createdAt: string;
    updatedAt: string;
}

const RolePermissions = () => {
    const [roles, setRoles] = useState<Role[]>([]);

    const Columns = [
        {
            title: 'Role',
            dataIndex: 'name',
            key: 'name',
            width: '50%',
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            width: '10%',
            render: (record: Role) => (
                <Link to={`/edit-permissions/${record._id}`}>
                    <EditFilled className="cursor-pointer text-blue-500" />
                </Link>
            ),
        },
    ];

    const dispatch = useAppDispatch();
    useEffect(() => {
        dispatch(GetAllRolesList())
            .then((res: any) => {
                const rolesData = res?.payload?.data?.data || [];
                setRoles(rolesData);
                // console.log('GetAllRolesList is: ', rolesData);
            })
            .catch((error: any) => {
                Alertify.error(error);
            });
    }, [dispatch]);

    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={Columns}
                dataSource={roles}
                bulkAction={false}
                backButton={true}
                heading="Roles"
            />
        </>
    );
};

export default RolePermissions;

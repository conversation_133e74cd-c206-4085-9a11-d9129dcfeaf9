import { Typography } from 'antd';
import React, { useEffect, useRef, useState, useMemo } from 'react';
import { useParams } from 'wouter';
import { useAppDispatch } from '~/hooks/redux-hooks';
import useSocket from '~/hooks/useSocket';

const { Title } = Typography;

const customStyle = {
    fontFamily: '"Helvetica Neue", Helvetica, Arial, sans-serif',
};

const calculateTimeLeft = (date: string, to: string) => {
    const now = new Date();
    const formattedDate = date?.split('T')[0];
    const toTime = new Date(`${formattedDate}T${to}:00`);

    if (isNaN(toTime.getTime())) {
        return { minutes: 0, seconds: 0, expired: true };
    }

    const diffInMs = toTime.getTime() - now.getTime();
    if (diffInMs <= 0) return { minutes: 0, seconds: 0, expired: true };

    const diffInSeconds = Math.floor(diffInMs / 1000);
    return {
        minutes: Math.floor(diffInSeconds / 60),
        seconds: diffInSeconds % 60,
        expired: false,
    };
};

const Booth = React.memo(
    ({
        booth,
        index,
        onTimeExpired,
    }: {
        booth: any;
        index: number;
        onTimeExpired: () => void;
    }) => {
        const [timeLeft, setTimeLeft] = useState<string>(booth.timeLeft);

        useEffect(() => {
            if (booth.timeLeft === 'Expired') return;

            const interval = setInterval(() => {
                const time = calculateTimeLeft(booth.date, booth.to);
                if (time.expired) {
                    clearInterval(interval);
                    setTimeLeft('Expired');
                    onTimeExpired();
                } else {
                    setTimeLeft(`${time.minutes}m ${time.seconds}s left`);
                }
            }, 1000);

            return () => clearInterval(interval);
        }, [booth.date, booth.to, booth.timeLeft, onTimeExpired]);

        return (
            <div
                className={`flex min-h-56 w-1/3 flex-col items-center justify-around rounded-[1vw] border-1 border-gray-200 px-2 py-10 font-medium sm:w-1/3 sm:rounded-[.7vw] sm:border-[.2vw] sm:px-[2vw] sm:py-[5vw] sm:text-[5vw] ${
                    !booth.available ? 'bg-[#A77BDF] text-white' : 'bg-[#fff]'
                }`}
            >
                {/* <Title
                    className={`m-0 ${
                        !booth.available ? 'text-white' : 'text-gray-600'
                    }  `}
                    style={{ fontSize: 'clamp(1rem, 8vw, 45rem)' }}
                >
                    {index}
                </Title> */}
                <Title
                    className={`m-0 ${
                        !booth.available ? 'text-white' : 'text-gray-600'
                    } sm:mb-[3vw] sm:mt-[2vw] `}
                    style={{ fontSize: 'clamp(1rem, 4vw, 45rem)' }}
                >
                    {booth.name}
                </Title>
                {timeLeft !== 'Expired' && !booth.available && (
                    <Title
                        className={`m-0 ${
                            timeLeft ? 'bg-[#8143D1] text-white' : 'invisible'
                        } mt-5 rounded-full px-[3vw] py-[1vw] sm:rounded-full sm:px-[3vw] sm:py-[1vw] `}
                        style={{ fontSize: 'clamp(1.5rem, 3vw, 14rem)' }}
                    >
                        {timeLeft}
                    </Title>
                )}
            </div>
        );
    }
);

const WaitTimeScreen = () => {
    const dispatch = useAppDispatch();
    const { id } = useParams();
    const socketRef = useSocket(dispatch, id);
    const [roomData, setRoomData] = useState<any[]>([]);

    const handleTimeExpired = () => {
        if (socketRef.current) {
            console.log('Time expired. Emitting refresh-wait-time event...');
            socketRef.current.emit('refresh-wait-time', { facilityId: id });
        }
    };

    useEffect(() => {
        if (!socketRef.current) return;

        const handleUpdate = (data: any) => {
            // console.log("Data---------", data);
            const transformedData = data?.data?.map((room: any) => {
                const timeLeft = calculateTimeLeft(room?.date, room?.to);
                return {
                    ...room,
                    id: room._id,
                    name: room.roomName,
                    date: room.date,
                    to: room.to,
                    timeLeft: timeLeft.expired
                        ? 'Expired'
                        : `${timeLeft.minutes}m ${timeLeft.seconds}s left`,
                    available: room.isavailable,
                };
            });

            setRoomData(transformedData);
        };

        socketRef.current.on('updateWaitingTime', handleUpdate);

        return () => {
            socketRef.current?.off('updateWaitingTime', handleUpdate);
        };
    }, [socketRef]);

    // console.log("Reoom data-------", roomData )

    return (
        <div
            className="min-h-screen w-full bg-[#8143D1] p-14 sm:px-32 sm:py-16"
            // style={customStyle}
        >
            <div className="waitTime_screen_font text-[7vw] font-bold leading-none text-white sm:text-[7vw] xl:text-[6vw]">
                Welcome to {roomData?.[0]?.facilityName}
            </div>
            <div className=" waitTime_screen_font mt-4 text-[3.5vw] leading-none text-white sm:mt-10 sm:text-[4.5vw] xl:text-[4vw]">
                Check availability of your desired booth (station)
            </div>
            <div className="mt-10 flex flex-col items-center justify-center rounded-xl border-[.2vw] sm:mt-[10vw] ">
                <div className="flex w-[100%] items-center justify-between p-3 sm:p-[2vw]">
                    <div className="flex flex-row items-center justify-center gap-2 border-r-2 border-white pe-4 sm:pe-2">
                        <div className="h-8 w-8 gap-2 rounded-full bg-white sm:h-[4vw] sm:w-[4vw]"></div>
                        <div
                            className="waitTime_screen_font font-medium text-white sm:rounded-[1.3vw] sm:px-[1vw]"
                            style={{ fontSize: 'clamp(1rem, 4vw, 45rem)' }}
                        >
                            Available for Reservation
                        </div>
                    </div>
                    <div className="flex flex-row items-center justify-center gap-2">
                        <div className="h-8 w-8 rounded-full border  border-white bg-[#A77BDF] sm:h-[4vw] sm:w-[4vw]"></div>
                        <div
                            className="waitTime_screen_font font-medium text-white sm:rounded-[1.3vw] sm:px-[1vw]"
                            style={{ fontSize: 'clamp(1rem, 4vw, 45rem)' }}
                        >
                            Waiting
                        </div>
                    </div>
                </div>
                <div className="flex w-full flex-wrap justify-center text-center">
                    {roomData?.map((booth: any, index: number) => (
                        <Booth
                            key={booth.id}
                            booth={booth}
                            index={index + 1}
                            onTimeExpired={handleTimeExpired}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default WaitTimeScreen;

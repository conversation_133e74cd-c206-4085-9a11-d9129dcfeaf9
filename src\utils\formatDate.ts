/**
 * Formats an ISO date string to 'DD-MM-YYYY' format.
 * @param {string} isoDate - The ISO date string to format.
 * @returns {string} - The formatted date string.
 */
export function formatDate(isoDate: string) {
    const date = new Date(isoDate);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = date.getFullYear();

    return `${day}-${month}-${year}`;
}
// Example usage:
const isoDate = '2024-09-29T18:30:00.000Z';
const formattedDate = formatDate(isoDate);
// console.log(formattedDate); // Output: '29-09-2024'

export function formatTime(isoDate: string): string {
    const date = new Date(isoDate);
    if (!(date instanceof Date) || isNaN(date.getTime())) {
        throw new Error('Invalid date');
    }

    // Get hours and minutes
    let hours: number = date.getHours();
    const minutes: number = date.getMinutes();

    // Determine AM or PM
    const period: string = hours >= 12 ? 'PM' : 'AM';

    // Convert to 12-hour format
    hours = hours % 12 || 12; // If hours is 0 (midnight), set it to 12

    // Add leading zeros if necessary
    const formattedHours: string = hours.toString().padStart(2, '0');
    const formattedMinutes: string = minutes.toString().padStart(2, '0');

    return `${formattedHours}:${formattedMinutes} ${period}`;
}

// Example usage
// console.log(formatTime(isoDate)); // e.g., "07:30 PM"

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

// Define the types for the working hours
interface TimeSlot {
    from: string;
    to: string;
    _id: string;
}

interface WorkingHours {
    [day: string]: TimeSlot[];
}

interface ConvertedTimeSlot {
    from: string;
    to: string;
    _id: string;
}

interface ConvertedWorkingHours {
    [day: string]: ConvertedTimeSlot[];
}

export function manipulateWorkingHours(
    workingHours: WorkingHours
): ConvertedWorkingHours {
    const daysOfWeek = Object.keys(workingHours);
    const convertedWorkingHours: ConvertedWorkingHours = {};

    daysOfWeek.forEach((day) => {
        const dayData = workingHours[day];

        if (Array.isArray(dayData)) {
            convertedWorkingHours[day.toLowerCase()] = dayData.map((slot) => ({
                from: dayjs(slot.from).format('HH:mm'),
                to: dayjs(slot.to).format('HH:mm'),
            }));
        }
    });

    return convertedWorkingHours;
}

// Helper function to capitalize the first letter of a string
function capitalizeFirstLetter(string: string): string {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

export function manipulateWorkingHoursIntoMoment(
    workingHours: WorkingHours
): ConvertedWorkingHours {
    const daysOfWeek = Object.keys(workingHours);
    const convertedWorkingHours: ConvertedWorkingHours = {};

    daysOfWeek.forEach((day) => {
        if (day !== '_id') {
            const capitalizedDay = capitalizeFirstLetter(day);
            convertedWorkingHours[capitalizedDay] = workingHours[day].map(
                (slot) => {
                    const fromTime = dayjs.tz(slot.from, 'HH:mm', 'UTC');
                    const toTime = dayjs.tz(slot.to, 'HH:mm', 'UTC');
                    return {
                        from: fromTime,
                        to: toTime,
                        _id: slot._id,
                    };
                }
            );
        } else {
            convertedWorkingHours[day] = workingHours[day];
        }
    });

    return convertedWorkingHours;
}

import React, { useState } from 'react';
import { startOfWeek, addDays, format, isSameDay, subWeeks, addWeeks } from 'date-fns';
import { Button } from 'antd';

const fakeSchedule = [
  {
    id: 1,
    time: '09:00 AM',
    title: 'Yoga Class',
    instructor: '<PERSON><PERSON><PERSON>',
    duration: '60 mins',
    location: 'Studio A',
    status: 'Available',
    image: 'https://via.placeholder.com/60',
  },
  {
    id: 2,
    time: '11:00 AM',
    title: 'HIIT Training',
    instructor: '<PERSON><PERSON><PERSON>',
    duration: '45 mins',
    location: 'Studio B',
    status: 'Booked',
    image: 'https://via.placeholder.com/60',
  },
  {
    id: 3,
    time: '05:00 PM',
    title: 'Zumba Dance',
    instructor: '<PERSON><PERSON>',
    duration: '50 mins',
    location: 'Main Hall',
    status: 'Available',
    image: 'https://via.placeholder.com/60',
  },
];

const BookingTab = ({ branding }: { branding: any }) => {
  const today = new Date();
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(today, { weekStartsOn: 1 }));
  const [selectedDate, setSelectedDate] = useState(today);

  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeekStart, i));

  const goToPreviousWeek = () => {
    const newStart = subWeeks(currentWeekStart, 1);
    setCurrentWeekStart(newStart);
    setSelectedDate(newStart); // Optional: Select first day of new week
  };

  const goToNextWeek = () => {
    const newStart = addWeeks(currentWeekStart, 1);
    setCurrentWeekStart(newStart);
    setSelectedDate(newStart); // Optional
  };

  return (
    <div style={{ fontFamily: branding.fontFamily }}>
      {/* ⏮️ Week Navigation */}
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 12 }}>
        <Button onClick={goToPreviousWeek}>Previous</Button>
        <div style={{ fontSize: 16, fontWeight: 500 }}>
          {format(weekDays[0], 'd MMM')} - {format(weekDays[6], 'd MMM yyyy')}
        </div>
        <Button onClick={goToNextWeek}>Next</Button>
      </div>

      {/* 🗓️ Date Selector */}
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 20 }}>
        {weekDays.map((date, idx) => {
          const isSelected = isSameDay(date, selectedDate);
          return (
            <div
              key={idx}
              onClick={() => setSelectedDate(date)}
              style={{
                textAlign: 'center',
                flex: 1,
                cursor: 'pointer',
                fontWeight: isSelected ? 'bold' : 'normal',
                borderBottom: isSelected ? `2px solid ${branding.primaryColor}` : '1px solid #eee',
                padding: '8px 0',
              }}
            >
              <div style={{ fontSize: 12, color: '#666' }}>{format(date, 'EEE').toUpperCase()}</div>
              <div>{format(date, 'd MMM')}</div>
            </div>
          );
        })}
      </div>

      {/* 📋 Schedule Cards */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
        {fakeSchedule.map((item) => (
          <div
            key={item.id}
            style={{
              display: 'flex',
              alignItems: 'center',
              border: '1px solid #ddd',
              borderRadius: 10,
              padding: 12,
              background: '#fff',
              boxShadow: '0 1px 4px rgba(0,0,0,0.05)',
            }}
          >
            <img
              src={item.image}
              alt={item.title}
              style={{ width: 60, height: 60, borderRadius: 8, marginRight: 16 }}
            />
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: 600 }}>{item.title}</div>
              <div style={{ fontSize: 12, color: '#666' }}>
                {item.time} • {item.duration} • {item.instructor}
              </div>
              <div style={{ fontSize: 12, color: '#999' }}>{item.location}</div>
            </div>
            <div style={{ fontSize: 13, color: branding.primaryColor, fontWeight: 500 }}>
              {item.status}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BookingTab;

import React, { useEffect, useState } from 'react';
import {
    startOfWeek,
    addDays,
    format,
    isSameDay,
    subWeeks,
    addWeeks,
} from 'date-fns';
import { <PERSON><PERSON>, Spin } from 'antd';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { getTrainerAvailability } from '~/redux/actions/widget/widget.action';
import { useLoader } from '~/hooks/useLoader';
import TrialBookingFlow from './TrialBookingFLow/TrialBookingFLow';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';

const PersonalAppointmentTab = ({
    branding,
    widgetId,
}: {
    branding: any;
    widgetId: string;
}) => {
    const today = new Date();
    const dispatch = useAppDispatch();
    const [currentWeekStart, setCurrentWeekStart] = useState(
        startOfWeek(today, { weekStartsOn: 1 })
    );
    const [selectedDate, setSelectedDate] = useState(today);
    const [scheduleData, setScheduleData] = useState<any[]>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const [selectedSlot, setSelectedSlot] = useState<any | null>(null);

    const weekDays = Array.from({ length: 7 }, (_, i) =>
        addDays(currentWeekStart, i)
    );

    const goToPreviousWeek = () => {
        const newStart = subWeeks(currentWeekStart, 1);
        setCurrentWeekStart(newStart);
        setSelectedDate(newStart);
    };

    const goToNextWeek = () => {
        const newStart = addWeeks(currentWeekStart, 1);
        setCurrentWeekStart(newStart);
        setSelectedDate(newStart);
    };

    useEffect(() => {
        startLoader();
        const formattedDate = format(selectedDate, 'yyyy-MM-dd');
        dispatch(getTrainerAvailability({ date: formattedDate }))
            .unwrap()
            .then((res: any) => {
                const flattened = res.data.trainers.flatMap((trainer: any) =>
                    trainer.timeSlots
                        .filter(
                            (slot: any) =>
                                slot.availabilityStatus === 'available'
                        )
                        .flatMap((slot: any) =>
                            slot.payrates.map((rate: any) => ({
                                trainer: trainer.userName,
                                trainerId: trainer.userId,
                                image: trainer.profilePicture,
                                location: trainer.facilityName,
                                date: trainer.date,
                                from: slot.from,
                                to: slot.to,
                                status: slot.availabilityStatus,
                                privacy: slot.privacy,
                                classType: slot.classType,
                                subtypeName: rate.subtypeName,
                                durationInMinutes: rate.durationInMinutes,
                                serviceName: rate.serviceName,
                                payRateId: rate._id,
                                pricing: rate.pricing,
                                serviceCategory: rate.serviceId,
                                subTypeId: rate.appointmentType,
                                Description: rate.serviceDescription,
                            }))
                        )
                );

                setScheduleData(flattened);
            })
            .catch((err) => {
                console.error('Error fetching trainer availability:', err);
                setScheduleData([]);
            })
            .finally(() => {
                endLoader();
            });
    }, [selectedDate]);

    if (selectedSlot) {
        return (
            <TrialBookingFlow
                slot={selectedSlot}
                widgetId={widgetId}
                onBack={() => setSelectedSlot(null)} // back button resets flow
            />
        );
    }

    return (
        <div style={{ fontFamily: branding.fontFamily, padding: '16px' }}>
            {/* Week Navigation */}
            <div
                style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: 16,
                }}
            >
                <ArrowLeftOutlined
                    onClick={goToPreviousWeek}
                    className="cursing-pointer sm:text-[2.5rem] lg:text-xl"
                />
                <div
                    style={{
                        display: 'flex',
                        flex: 1,
                        justifyContent: 'space-around',
                    }}
                >
                    {weekDays.map((date, idx) => {
                        const isSelected = isSameDay(date, selectedDate);
                        return (
                            <div
                                key={idx}
                                onClick={() => setSelectedDate(date)}
                                style={{
                                    textAlign: 'center',
                                    cursor: 'pointer',
                                    borderBottom: isSelected
                                        ? `2px solid ${branding.primaryColor}`
                                        : '1px solid #eee',
                                    paddingBottom: 4,
                                    minWidth: 50,
                                }}
                            >
                                <div className="text-[#999] sm:text-[1.45rem] lg:text-[0.8rem] ">
                                    {format(date, 'EEE').toUpperCase()}
                                </div>
                                <div className="sm:text-[1.6rem] lg:text-[1.2rem] ">
                                    {format(date, 'd MMM')}
                                </div>
                            </div>
                        );
                    })}
                </div>

                <ArrowRightOutlined
                    onClick={goToNextWeek}
                    className="cursing-pointer sm:text-[2.5rem] lg:text-xl"
                />
            </div>

            {/* Schedule List */}
            <Spin spinning={loader}>
                <div className="border-t sm:border-[#999] lg:border-[#eee]">
                    {scheduleData.length === 0 ? (
                        <div
                            style={{
                                textAlign: 'center',
                                color: '#999',
                                padding: '24px 0',
                            }}
                        >
                            No sessions for this day.
                        </div>
                    ) : (
                        scheduleData.map((item, index) => (
                            <div
                                key={index}
                                className="flex items-center justify-between gap-4 border-b px-4 py-7 sm:flex-col sm:gap-6 sm:border-[#555] lg:flex-row lg:border-[#eee]"
                            >
                                {/* Time Block */}
                                <div className="flex flex-row items-center sm:w-[100%] sm:justify-between lg:w-[65%] lg:gap-5">
                                    <div className="w-full sm:w-[40%] lg:w-[20%]">
                                        <div className="font-semibold sm:text-[2.5rem] lg:text-[1.1rem]">
                                            {item.from} - {item.to}
                                        </div>
                                        <div className="text-[#999] sm:text-[2rem] lg:text-sm">
                                            {item.durationInMinutes}-Min
                                        </div>
                                    </div>

                                    {/* Avatar + Details */}
                                    <div className="flex flex-1 items-center  gap-3">
                                        <img
                                            src={
                                                item.image ||
                                                'https://staginghop.hkstest.uk/assets/Profile_icon.png'
                                            }
                                            alt="avatar"
                                            className="rounded-full object-cover sm:h-[5rem] sm:w-[5rem] lg:h-12 lg:w-12"
                                        />
                                        <div>
                                            <div className="font-semibold sm:text-[2.2rem] lg:text-[1rem]">
                                                {item.subtypeName}
                                            </div>
                                            <div className="text-[#999] sm:text-[2rem] lg:text-sm">
                                                {item.trainer}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Location + Book Button */}
                                <div className="flex  flex-row items-center sm:w-[100%] sm:gap-10 lg:w-[35%] lg:justify-end lg:gap-5">
                                    <div className="mb-2 text-center font-semibold text-[#555] sm:mb-0 sm:text-left sm:text-[2.2rem] lg:text-sm">
                                        {item.location}
                                    </div>
                                    <button
                                        type="button"
                                        className="rounded-md border border-black px-4 text-black sm:py-1.5 sm:text-[2.5rem] lg:py-2 lg:text-sm"
                                        onClick={() => setSelectedSlot(item)}
                                    >
                                        Book Now
                                    </button>
                                </div>
                            </div>
                        ))
                    )}
                </div>
            </Spin>
        </div>
    );
};

export default PersonalAppointmentTab;

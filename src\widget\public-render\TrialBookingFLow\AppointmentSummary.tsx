import React, { useState, useEffect } from 'react';
import { Button, Select, message, Spin } from 'antd';
import { format, parse, addMinutes } from 'date-fns';
import { postApi } from '~/services/api-services';
import { useAppDispatch } from '~/hooks/redux-hooks';
import {
    bookpersonalAppointment,
    getTrainerSlot,
} from '~/redux/actions/widget/widget.action';

const AppointmentSummary = ({
    slot,
    userInfo,
    selectedPackage,
    widgetId,
    onBack,
    onResetFlow,
}: {
    slot: any;
    userInfo: any;
    selectedPackage: any;
    widgetId: string;
    onBack: () => void;
    onResetFlow: () => void;
}) => {
    const [availableSlots, setAvailableSlots] = useState<any[]>([]);
    const [selectedFromTime, setSelectedFromTime] = useState<string | null>(
        null
    );
    const [selectedSlot, setSelectedSlot] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [packageId, setPackageId] = useState(false);
    const dispatch = useAppDispatch();

    useEffect(() => {
        const fetchSlots = async () => {
            setLoading(true);
            const response: any = await dispatch(
                getTrainerSlot({
                    trainerId: slot.trainerId,
                    date: slot.date,
                    serviceCategoryId: slot.serviceCategory,
                    subtypeId: slot.subTypeId,
                    organizationId: widgetId,
                    userId: userInfo._id,
                })
            );
            setLoading(false);
            if (response?.payload?.data?.timeSlots) {
                const filtered = response.payload.data.timeSlots.filter(
                    (s: any) => s.isAvailable
                );
                setAvailableSlots(filtered);
                setPackageId(response.payload.data.purchasePackage[0]._id);
            }
        };

        fetchSlots();
    }, [
        dispatch,
        slot.trainerId,
        slot.date,
        slot.serviceCategory,
        slot.subTypeId,
        widgetId,
    ]);

    const handleSelectTime = (fromTime: string) => {
        setSelectedFromTime(fromTime);
        const matched = availableSlots.find((s) => s.from === fromTime);
        setSelectedSlot(matched || null);
    };

    const handleConfirm = async () => {
        if (!selectedSlot) {
            message.error('Please select a time');
            return;
        }

        const payload = {
            clientId: userInfo._id,
            trainerId: slot.trainerId,
            pricingId: slot.pricing.find((p: any) => p.isTrialPricing)?._id,
            purchaseId: packageId,
            date: slot.date,
            from: selectedSlot.from,
            to: selectedSlot.to,
            classType: 'personalAppointment',
            dateRange: 'Single',
            serviceCategory: slot.serviceCategory,
            subType: slot.subTypeId,
            duration: slot.durationInMinutes,
            checkIn: false,
            organizationId: widgetId,
        };

        try {
            const response = await dispatch(
                bookpersonalAppointment(payload)
            ).unwrap();
            console.log(response, 'response');
            if (response.status === 200) {
                message.success('Appointment confirmed!');
                setTimeout(() => {
                    onResetFlow();
                }, 1000);
            }
        } catch (err) {
            message.error('Booking failed.');
        }
    };

    const selectedEnd = selectedSlot?.to;
    const duration = selectedSlot?.durationInMinutes || slot.durationInMinutes;

    return (
        <div
            style={{
                // padding: 24,
                fontFamily: "'Poppins', sans-serif",
            }}
            className=""
        >
            <div
                style={{
                    background: '#fff',
                }}
                className="mx-auto flex flex-col   sm:w-[100%] lg:w-[50%]"
            >
                <h3 className="text-center font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                    {format(new Date(slot.date), 'EEE MMM dd yyyy')}
                </h3>
                <h2
                    className="text-center font-semibold sm:text-[2.2rem] lg:text-[1rem]"
                    // style={{ textAlign: 'center', marginBottom: 0 }}
                >
                    {slot.subtypeName}
                </h2>
                <p className="text-center font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                    {selectedFromTime} – {selectedEnd || '--'} ({duration} Mins)
                </p>

                <hr className="my-4" />

                <div className="flex flex-col gap-2 border-b border-[##E5E7EB] pb-3">
                    <p className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                        Staff
                    </p>
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            marginTop: 8,
                        }}
                    >
                        <img
                            src={
                                slot.image ||
                                'https://staginghop.hkstest.uk/assets/Profile_icon.png'
                            }
                            alt="trainer"
                            style={{
                                // width: 80,
                                // height: 80,
                                borderRadius: '50%',
                                objectFit: 'cover',
                                marginRight: 20,
                            }}
                            className=" h-14 w-14"
                        />
                        <span className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            {slot.trainer}
                        </span>
                    </div>

                    <div className="">
                        <p className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                            Location
                        </p>
                        <div className="mt-1 font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            {slot.location}
                        </div>
                    </div>
                </div>

                <div className="mb-7 flex flex-col  pt-3">
                    <p className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                        Details
                    </p>
                    <div className="flex flex-row items-center justify-between gap-2 border-b border-[##E5E7EB] pb-3">
                        <p className="font-medium text-[#000] sm:text-[2.2rem] lg:text-[0.85rem]">
                            Start Time:
                        </p>
                        <Select
                            value={selectedFromTime}
                            onChange={handleSelectTime}
                            placeholder="Select time"
                            loading={loading}
                            variant="borderless"
                            style={{
                                borderBottom: '1px solid #e5e7eb',
                                borderRadius: '0px',
                                width: '30%',
                            }}
                        >
                            {availableSlots.map((s) => (
                                <Select.Option key={s.from} value={s.from}>
                                    {format(
                                        parse(s.from, 'HH:mm', new Date()),
                                        'hh:mm a'
                                    )}
                                </Select.Option>
                            ))}
                        </Select>
                    </div>
                    <div className="flex flex-row items-center justify-between border-b border-[#E5E7EB] pb-3 pt-2">
                        <div className="font-medium text-[#000] sm:text-[2.2rem] lg:text-[0.85rem]">
                            End Time:{' '}
                        </div>
                        <span className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            {selectedEnd || '--'}
                        </span>
                    </div>
                    <div className="flex flex-row  items-center justify-between border-b border-[##E5E7EB] pb-3 pt-2">
                        <div className="font-medium text-[#000] sm:text-[2.2rem] lg:text-[0.85rem]">
                            Appointment Length:{' '}
                        </div>
                        <span className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            {duration} Mins
                        </span>{' '}
                    </div>
                </div>

                <button
                    className="w-full rounded-lg bg-[#A065EA] text-white hover:border-none hover:bg-[#A065EA] hover:text-white sm:py-8 sm:text-4xl lg:py-1 lg:text-lg"
                    onClick={handleConfirm}
                    disabled={!selectedSlot}
                >
                    Book Appointment
                </button>
            </div>
        </div>
    );
};

export default AppointmentSummary;

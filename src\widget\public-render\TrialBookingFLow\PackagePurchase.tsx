import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Typography } from 'antd';
const { Title, Paragraph } = Typography;

const PackagePurchase = ({
    slot,
    widgetId,
    user,
    onBack,
    onPackageSelect,
}: {
    slot: any;
    widgetId: string;
    user: any;
    onBack: () => void;
    onPackageSelect: (pkg: any) => void;
}) => {
    const trialPackages = slot.pricing?.filter((p: any) => p.isTrialPricing);

    return (
        <div
            style={{
                padding: 24,
                fontFamily: 'Poppins, sans-serif',
            }}
        >
            <Button
                type="link"
                onClick={onBack}
                style={{ padding: 0, color: '#686D76' }}
                className="sm:mb-20 sm:text-4xl lg:mb-6 lg:text-lg"
            >
                ← Back
            </Button>

            {/* <Title level={3}>Choose a Trial Package</Title>
      <Paragraph>Select one of the following trial packages to continue.</Paragraph> */}
            <div
                style={{
                    padding: 24,

                    background: '#fff',
                }}
                className="mx-auto flex flex-col gap-3  sm:w-[100%] lg:w-[50%]"
            >
                {trialPackages?.map((pkg: any) => (
                    <Card
                        key={pkg._id}
                        title={
                            <p className="font-semibold text-[#1a3353] sm:py-5 sm:text-5xl lg:py-1 lg:text-2xl">
                                {pkg.name}
                            </p>
                        }
                        // bordered
                        style={{ marginBottom: 16 }}
                        actions={[
                            <button
                                style={{
                                    backgroundColor: '#A065EA',
                                    width: '90%',
                                }}
                                // type="primary"
                                onClick={() => onPackageSelect(pkg)}
                                block
                                className="w-full rounded-lg bg-[#A065EA] text-white hover:border-none hover:bg-[#A065EA] hover:text-white sm:py-8 sm:text-4xl lg:py-1 lg:text-lg"
                            >
                                Proceed to Pay
                            </button>,
                        ]}
                    >
                        <Paragraph className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            <strong className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                                Sessions:
                            </strong>{' '}
                            {pkg.sessionCount}
                        </Paragraph>
                        <Paragraph className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            <strong className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                                Validity:
                            </strong>{' '}
                            {pkg.expiredInDays}
                            {pkg.durationUnit}
                        </Paragraph>
                        <Paragraph className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            <strong className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                                Description:
                            </strong>{' '}
                            {pkg.description || 'No description available.'}
                        </Paragraph>
                        <Paragraph className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            <strong className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                                GST:
                            </strong>{' '}
                            {pkg.tax}%
                        </Paragraph>
                        <Paragraph className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                            <strong className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                                Unit Price:
                            </strong>{' '}
                            ₹{pkg.price}
                        </Paragraph>
                    </Card>
                ))}
            </div>
        </div>
    );
};

export default PackagePurchase;

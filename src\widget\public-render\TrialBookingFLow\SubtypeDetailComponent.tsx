// TrialBookingFlow/SubtypeDetailComponent.tsx
import React from 'react';
import { Button } from 'antd';

const SubtypeDetailComponent = ({
    slot,
    widgetId,
    onBack,
    onSignupClick,
}: {
    slot: any;
    widgetId: string;
    onBack: () => void;
    onSignupClick: () => void;
}) => {
    if (!slot) return null;
    console.log(slot, 'slotttting');
    const hasTrialPricing = slot.pricing?.some((p: any) => p.isTrialPricing);
    const TrialPricing = slot.pricing
        ?.filter((item: any) => item.isTrialPricing)
        .map((item: any) => ({ ...item }));

    console.log(TrialPricing);

    return (
        <div style={{ padding: 24, fontFamily: "'Poppins', sans-serif" }}>
            <Button
                type="link"
                onClick={onBack}
                style={{ padding: 0, color: '#686D76' }}
                className="sm:mb-20 sm:text-4xl lg:mb-6 lg:text-lg"
            >
                ← Back
            </Button>

            <div
                style={{
                    padding: 24,

                    background: '#fff',
                }}
                className="mx-auto flex flex-col gap-3  sm:w-[100%] lg:w-[50%]"
            >
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        marginBottom: 24,
                    }}
                >
                    <img
                        src={
                            slot.image ||
                            'https://staginghop.hkstest.uk/assets/Profile_icon.png'
                        }
                        alt="Trainer"
                        style={{
                            // width: 80,
                            // height: 80,
                            borderRadius: '50%',
                            objectFit: 'cover',
                            marginRight: 20,
                        }}
                        className="sm:h-40 sm:w-40 lg:h-20 lg:w-20"
                    />
                    <div>
                        <h2 className="font-semibold sm:text-[2.2rem] lg:text-[1rem]">
                            {slot.subtypeName}
                        </h2>
                        <p
                            style={{ margin: '4px 0' }}
                            className="text-[#999] sm:text-[2rem] lg:text-sm"
                        >
                            {slot.trainer}
                        </p>
                        <p
                            style={{ margin: 0 }}
                            className="text-[#999] sm:text-[2rem] lg:text-sm"
                        >
                            {slot.durationInMinutes}-Min
                        </p>
                    </div>
                </div>

                <div>
                    <p className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                        <span className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                            {' '}
                            Time:
                        </span>{' '}
                        {slot.from} – {slot.to}
                    </p>
                    <p className="pt-5 font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                        <span className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                            Location:
                        </span>{' '}
                        {slot.location}
                    </p>

                    {slot.Description &&
                        slot.Description.replace(/<[^>]*>/g, '').trim() && (
                            <p className="py-5  font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                                {slot.Description.replace(/<[^>]*>/g, '')}
                            </p>
                        )}
                    <p className="font-medium text-[#999] sm:text-[2.2rem] lg:text-[1rem]">
                        <span className="font-semibold text-[#000] sm:text-[2.2rem] lg:text-[1rem]">
                            Trial Price:
                        </span>{' '}
                        ₹ {TrialPricing[0]?.price}
                    </p>
                </div>

                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        gap: 16,
                        alignItems: 'center',
                        marginTop: 24,
                    }}
                >
                    {hasTrialPricing && (
                        <button
                            // type="primary"
                            className="w-full rounded-lg bg-[#A065EA] text-white hover:border-none hover:bg-[#A065EA] hover:text-white sm:py-8 sm:text-4xl lg:py-1 lg:text-lg"
                            onClick={onSignupClick}
                        >
                            {hasTrialPricing ? 'Book Trial' : 'Book Now'}
                        </button>
                    )}

                    {/* <Button
                        type="default"
                        style={{ width: '50%' }}
                        onClick={() => alert('Login flow coming soon')}
                    >
                        Login
                    </Button> */}
                </div>
            </div>
        </div>
    );
};

export default SubtypeDetailComponent;

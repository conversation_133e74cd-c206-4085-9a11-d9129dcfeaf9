import React, { useMemo } from 'react';
import { Button, Checkbox, ConfigProvider, Form, Input, message } from 'antd';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch } from '~/hooks/redux-hooks';
import {
    EmailOrMobileValidation,
    registerClient,
} from '~/redux/actions/widget/widget.action';
import debounce from 'lodash/debounce';

const SignupComponent = ({
    slot,
    onBack,
    widgetId,
    onSignupSuccess,
}: {
    slot: any;
    onBack: () => void;
    widgetId: string;
    onSignupSuccess: (user: any) => void;
}) => {
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const [form] = Form.useForm();

    const validationCache = useMemo(() => new Map<string, boolean>(), []);

    const checkAvailability = async (
        value: string,
        isEmail: boolean
    ): Promise<boolean> => {
        if (!value) return true;
        if (validationCache.has(value)) return validationCache.get(value)!;
        try {
            const res = await dispatch(
                EmailOrMobileValidation({ value })
            ).unwrap();
            const isAvailable = !res?.res?.data;
            validationCache.set(value, isAvailable);
            return isAvailable;
        } catch (err) {
            return true; // fail-safe
        }
    };

    const handleSignup = async () => {
        try {
            const values = await form.validateFields();
            startLoader();

            // ✅ Check email & phone availability manually
            const [emailAvailable, phoneAvailable] = await Promise.all([
                checkAvailability(values.email, true),
                checkAvailability(values.phone, false),
            ]);

            if (!emailAvailable) {
                form.setFields([
                    {
                        name: 'email',
                        errors: ['Email is already in use.'],
                    },
                ]);
                endLoader();
                return;
            }

            if (!phoneAvailable) {
                form.setFields([
                    {
                        name: 'phone',
                        errors: ['Mobile number is already in use.'],
                    },
                ]);
                endLoader();
                return;
            }

            const payload = {
                type: 'mobile',
                firstName: values.firstName,
                lastName: values.lastName,
                mobile: values.phone,
                organizationId: widgetId,
                isUserAcceptTerms: values.isUserAcceptTerms,
                countryCode: '+91',
                email: values.email,
            };

            const result = await dispatch(registerClient(payload)).unwrap();
            if (result.data.data.user) {
                message.success('Registration successful');
                onSignupSuccess(result.data.data.user);
            }
        } catch (error: any) {
            console.error('Signup error:', error?.message || error);
            if (!error.errorFields)
                message.error(error?.message || 'Signup failed');
        } finally {
            endLoader();
        }
    };

    return (
        <div
            style={{
                padding: 24,
                fontFamily: "'Poppins', sans-serif",
            }}
        >
            <Button
                type="link"
                onClick={onBack}
                style={{ padding: 0, color: '#686D76' }}
                className="sm:mb-20 sm:text-4xl lg:mb-6 lg:text-lg"
            >
                ← Back
            </Button>

            <div
                style={{
                    padding: 24,

                    background: '#fff',
                }}
                className="mx-auto flex flex-col gap-3  sm:w-[100%] lg:w-[50%]"
            >
                <h2
                    style={{ marginBottom: 24 }}
                    className="font-semibold text-[#1a3353] sm:text-5xl lg:text-2xl"
                >
                    Sign up to Book Trial
                </h2>

                <ConfigProvider
                    theme={{
                        token: {
                            controlHeight: 28, // applies globally to all controls
                        },
                        components: {
                            Input: {
                                controlHeight: 28, // force specifically for Input component
                                fontSize: 14,
                            },
                            Form: {
                                itemMarginBottom: 22,
                                verticalLabelMargin: -5,
                            },
                            Checkbox: {
                                fontSize: 14,
                                colorText: '#455560',
                            },
                        },
                    }}
                >
                    <Form form={form} layout="vertical" requiredMark={false}>
                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    First Name
                                    <span style={{ color: 'red' }}>*</span>
                                </span>
                            }
                            name="firstName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter your first name',
                                },
                            ]}
                        >
                            <Input placeholder="First name" />
                        </Form.Item>

                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Last Name
                                </span>
                            }
                            name="lastName"
                        >
                            <Input size="large" placeholder="Last name" />
                        </Form.Item>

                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Email{' '}
                                </span>
                            }
                            name="email"
                            rules={[
                                {
                                    type: 'email',
                                    message: 'Please enter a valid email',
                                },
                                {
                                    required: false,
                                    message: 'Email is required',
                                },
                            ]}
                        >
                            <Input
                                size="large"
                                placeholder="Enter your email"
                            />
                        </Form.Item>

                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Phone{' '}
                                    <span style={{ color: 'red' }}>*</span>
                                </span>
                            }
                            name="phone"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter your phone number',
                                },
                            ]}
                        >
                            <Input
                                size="large"
                                placeholder="Enter your phone number"
                            />
                        </Form.Item>

                        <Form.Item
                            name="isUserAcceptTerms"
                            valuePropName="checked"
                            rules={[
                                {
                                    validator: (_, value) =>
                                        value
                                            ? Promise.resolve()
                                            : Promise.reject(
                                                  new Error(
                                                      'Please accept terms and conditions'
                                                  )
                                              ),
                                },
                            ]}
                            className="lg:-mt-5"
                        >
                            <Checkbox>
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    I accept the terms and conditions
                                </span>
                            </Checkbox>
                        </Form.Item>

                        <button
                            className="w-full rounded-lg bg-[#A065EA] text-white hover:border-none hover:bg-[#A065EA] hover:text-white sm:py-8 sm:text-4xl lg:py-1 lg:text-lg"
                            // htmlType="button"
                            onClick={handleSignup}
                            // loading={loader}
                            // block
                            // style={{
                            //     backgroundColor: '#A065EA',
                            // }}
                        >
                            Sign up
                        </button>
                    </Form>
                </ConfigProvider>
            </div>
        </div>
    );
};

export default SignupComponent;

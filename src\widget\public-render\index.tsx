import React, { useState } from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import BookingTab from './BookingTab';
import PATab from './PersonalAppointmentTab';
import widgetStore from '~/redux/widget-store';
import CoursesTab from './CoursesTab';
import { message } from 'antd';
// import './widget.css';
// import ScheduleTab from './ScheduleTab';

const widgetBrandingConfig: Record<string, any> = {
    test123: {
        primaryColor: '#4CAF50',
        fontFamily: "'Roboto', sans-serif",
        borderRadius: '12px',
        backgroundColor: '#f9f9f9',
    },
    mygym456: {
        primaryColor: '#FF5722',
        fontFamily: "'Poppins', sans-serif",
        borderRadius: '8px',
        backgroundColor: '#ffffff',
    },
};

const defaultBranding = {
    primaryColor: '#1A3353',
    fontFamily: "'Arial', sans-serif",
    borderRadius: '10px',
    backgroundColor: '#ffffff',
};

const App = ({ widgetId, branding }: { widgetId: string; branding: any }) => {
    const [activeTab, setActiveTab] = useState('PA');

    const tabs = [
        // { key: 'Bookings', label: 'Bookings' },
        { key: 'PA', label: 'Personal Appointment' },
        // { key: 'classes', label: 'Classes' },
        // { key: 'courses', label: 'Courses' },
    ];

    const containerStyle: React.CSSProperties = {
        fontFamily: branding.fontFamily,
        backgroundColor: branding.backgroundColor,
        borderRadius: branding.borderRadius,
        color: '#222',
        maxWidth: 960,
        // margin: '20px auto',
        // padding: '20px',
        // boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    };

    const tabBarStyle: React.CSSProperties = {
        // display: 'flex',
        // gap: '24px',
        borderBottom: '1px solid #ccc',
        paddingBottom: '8px',
        marginBottom: '20px',
    };

    // const tabStyle = (key: string): React.CSSProperties => ({
    //     fontWeight: activeTab === key ? 'bold' : 'normal',
    //     cursor: 'pointer',
    //     color: activeTab === key ? branding.primaryColor : '#222',
    //     borderBottom:
    //         activeTab === key ? `2px solid ${branding.primaryColor}` : 'none',
    //     paddingBottom: '4px',
    // });

    const renderTabContent = () => {
        switch (activeTab) {
            case 'Bookings':
                return <BookingTab branding={branding} />;
            case 'PA':
                return <PATab branding={branding} widgetId={widgetId} />;
            case 'courses':
                // return <ScheduleTab branding={branding} />;
                return <CoursesTab branding={branding} />;
            case 'pricing':
                return <p>💰 Pricing Plans</p>;
            default:
                return null;
        }
    };

    return (
        <div style={containerStyle} className="mx-auto sm:p-0">
            <div
                style={tabBarStyle}
                className="sm:grid sm:grid-cols-2 sm:gap-5 lg:flex lg:flex-row lg:items-center lg:justify-between"
            >
                {tabs.map((tab) => (
                    <div
                        className={`sm:borde cursor-pointer text-[#1a3352] sm:rounded-md sm:border-[#1a3353] sm:py-3 sm:text-center sm:text-[2.125rem] lg:border-none lg:text-xl ${
                            activeTab === tab.key
                                ? 'font-semibold '
                                : 'font-normal'
                        }`}
                        key={tab.key}
                        // style={tabStyle(tab.key)}
                        onClick={() => setActiveTab(tab.key)}
                    >
                        {tab.label}
                    </div>
                ))}
            </div>
            <div>{renderTabContent()}</div>
        </div>
    );
};

// Mount to DOM
const root = document.getElementById('hop-widget-root');
const widgetId = root?.dataset?.widgetId;
const branding = widgetBrandingConfig[widgetId || ''] || defaultBranding;

if (root && widgetId) {
    message.config({
        duration: 2,
        maxCount: 3,
        top: 100,
    });
    const rootElement = ReactDOM.createRoot(root);
    rootElement.render(
        <Provider store={widgetStore}>
            <App widgetId={widgetId} branding={branding} />
        </Provider>
    );
} else {
    console.error('❌ Widget root not found or widgetId missing');
}

/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ['./index.html', './src/**/*.{js,ts,jsx,tsx,mdx}'],
    theme: {
        extend: {
            colors: {
                transparent: 'transparent',
                current: 'currentColor',
                primary: {
                    DEFAULT: '#8143D1',
                    dark: '#0b7620',
                    light: '#e1fdf8',
                    pale: '#ff0099',
                },
                purpleLight: { DEFAULT: 'rgba(167, 123, 223, 1)' },
                secondary: { DEFAULT: 'rgba(129, 67, 209, 0.3)' },
                warning: { DEFAULT: '#fb8903', light: '#fff0ca' },
                danger: { DEFAULT: '#ff0000', light: '#ffebeb' },
                success: { DEFAULT: '#5cb85c', light: '#ecffef' },
                gray: {
                    dark: '#464646',
                    DEFAULT: '#3c4858',
                    medium: '#818181',
                    light: '#b7b7b7',
                    lightest: '#F4F4F4',
                },
                popover: 'rgba(0, 80, 69, 1)',
                'checkbox-checked': '#8143D1',
                'switch-on': '#8143D1',
                'switch-off': '#D0D4D7',
            },
            screens: {
                '@sm': { max: '768px' },
                '@md': { min: '769px', max: '1024px' },
                '@lg': { min: '1025px', max: '1440px' },
                '@lgtab': { min: '1024px', max: '1200px' },
                '@xl': { min: '1441px', max: '1919px' },
                '@2xl': { min: '1920px' },
            },
            fontSize: {
                10: '1rem',
                12: '1.2rem', // 12px
                14: '1.4rem', // 14px
                16: '1.6rem', // 16px
                18: '1.8rem', // 18px
                20: '2.0rem', // 20px
                24: '2.4rem', // 24px
                25: '2.5rem', // 25px
                28: '2.8rem', //28px
                32: '3.2rem', // 32px
                48: '4.8rem', // 48px
                56: '5.6rem', // 56px
                64: '6.4rem', // 64px
            },

            padding: {
                5: '1rem',
                15: '2rem',
            },
            borderWidth: {
                DEFAULT: '1px',
                0: '0px',
                1: '1px',
            },
        },
    },
    plugins: [],
    important: true,
    corePlugins: {
        // preflight: false, // <== disable this!
    },
};

('https://mrtnschndlr.medium.com/things-to-rethink-with-tailwind-css-part-1-breakpoints-b2809e2bbb2');

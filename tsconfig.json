{
    "compilerOptions": {
        "target": "es5",
        "lib": ["dom", "dom.iterable", "esnext"],
        "useDefineForClassFields": true,
        "module": "ES2015",
        "skipLibCheck": true,
        "allowJs": true,
        "esModuleInterop": true,

        /* Bundler mode */
        "moduleResolution": "node",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",

        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "baseUrl": ".",
        "paths": {
            "~/*": ["src/*"]
        }
    },
    "include": ["src"],
    "exclude": ["node_modules"],
    "references": [{ "path": "./tsconfig.node.json" }]
}
